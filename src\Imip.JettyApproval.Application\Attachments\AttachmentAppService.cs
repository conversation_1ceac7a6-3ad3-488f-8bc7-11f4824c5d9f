using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.Mapping.Mappers;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Application service for Attachment entity
/// </summary>
[RemoteService(IsEnabled = false)]
public class AttachmentAppService :
    CrudAppService<Attachment, AttachmentDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateAttachmentDto, CreateUpdateAttachmentDto>,
    IAttachmentAppService
{
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly AttachmentMapper _mapper;
    private readonly IBlobContainer _blobContainer;
    private readonly IConfiguration _configuration;

    private readonly ILogger<AttachmentAppService> _logger;

    public AttachmentAppService(
        IAttachmentRepository attachmentRepository,
        AttachmentMapper mapper,
        IBlobContainerFactory blobContainerFactory,
        IConfiguration configuration,
        ILogger<AttachmentAppService> logger)
        : base(attachmentRepository)
    {
        _attachmentRepository = attachmentRepository;
        _mapper = mapper;
        _blobContainer = blobContainerFactory.Create("default");
        _configuration = configuration;
        _logger = logger;
    }

    public override async Task<AttachmentDto> CreateAsync(CreateUpdateAttachmentDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _attachmentRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<AttachmentDto> UpdateAsync(Guid id, CreateUpdateAttachmentDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _attachmentRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<AttachmentDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<AttachmentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _attachmentRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<AttachmentDto>(totalCount, dtos);
    }

    /// <summary>
    /// Uploads a file
    /// </summary>
    public async Task<FileUploadResultDto> UploadFileAsync(FileUploadDto input, string fileName, string contentType, byte[] fileBytes)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(fileName))
        {
            throw new UserFriendlyException(L["FileNameCannotBeEmpty"]);
        }

        if (fileBytes == null || fileBytes.Length == 0)
        {
            throw new UserFriendlyException(L["FileContentCannotBeEmpty"]);
        }

        try
        {
            var fileSize = fileBytes.Length;

            // Generate a unique blob name
            var blobName = $"{Guid.NewGuid():N}_{Path.GetFileName(fileName)}";

            // Save the file to blob storage
            using (var memoryStream = new MemoryStream(fileBytes))
            {
                await _blobContainer.SaveAsync(blobName, memoryStream);
            }

            // Create an attachment entity
            var attachment = new Attachment(
                GuidGenerator.Create(),
                fileName,
                contentType,
                fileSize,
                blobName,
                input.Description,
                input.ReferenceId,
                input.ReferenceType
            );

            // Save the attachment metadata to the database
            await _attachmentRepository.InsertAsync(attachment);

            // Return the result
            return new FileUploadResultDto
            {
                Id = attachment.Id,
                FileName = attachment.FileName ?? "unknown_file",
                ContentType = attachment.ContentType ?? "application/octet-stream",
                Size = attachment.Size,
                UploadTime = attachment.CreationTime,
                Url = GetFileUrl(attachment.Id),
                StreamUrl = GetFileStreamUrl(attachment.Id)
            };
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(L["FailedToUploadFile"], ex.Message);
        }
    }

    /// <summary>
    /// Gets the download URL for a file
    /// </summary>
    private string GetFileUrl(Guid id)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/attachment/download/{id}";
    }

    /// <summary>
    /// Gets the stream URL for a file
    /// </summary>
    private string GetFileStreamUrl(Guid id)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/attachment/stream/{id}";
    }


    public async Task<PagedResultDto<FileUploadResultDto>> GetAllAsync(GetAttachmentsInput input)
    {
        // Create a queryable for attachments
        var query = await _attachmentRepository.GetQueryableAsync();

        // Apply filters if provided
        if (input.ReferenceId.HasValue)
        {
            query = query.Where(a => a.ReferenceId == input.ReferenceId);
        }

        if (!string.IsNullOrWhiteSpace(input.ReferenceType))
        {
            query = query.Where(a => a.ReferenceType == input.ReferenceType);
        }

        if (!string.IsNullOrWhiteSpace(input.FileNameFilter))
        {
            query = query.Where(a => a.FileName != null && a.FileName.Contains(input.FileNameFilter));
        }

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply sorting
        var sortedQuery = query;
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            // Use Dynamic LINQ for sorting
            sortedQuery = query.OrderBy(input.Sorting);
        }
        else
        {
            // Default sorting by creation time descending
            sortedQuery = query.OrderByDescending(a => a.CreationTime);
        }

        // Apply paging
        var pagedQuery = sortedQuery
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        // Execute query
        var attachments = await AsyncExecuter.ToListAsync(pagedQuery);

        // Map to DTOs
        var items = attachments.Select(a => new FileUploadResultDto
        {
            Id = a.Id,
            FileName = a.FileName ?? "unknown_file",
            ContentType = a.ContentType ?? "application/octet-stream",
            Size = a.Size,
            UploadTime = a.CreationTime,
            Url = GetFileUrl(a.Id),
            StreamUrl = GetFileStreamUrl(a.Id)
        }).ToList();

        // Return paged result
        return new PagedResultDto<FileUploadResultDto>(totalCount, items);
    }


    /// <summary>
    /// Uploads a file using a single DTO
    /// </summary>
    public async Task<FileUploadResultDto> UploadWithFileContentAsync(FileUploadInputDto input)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(input.FileName))
        {
            throw new UserFriendlyException(L["FileNameCannotBeEmpty"]);
        }

        if (input.FileContent == null || input.FileContent.Length == 0)
        {
            throw new UserFriendlyException(L["FileContentCannotBeEmpty"]);
        }

        try
        {
            var fileSize = input.FileContent.Length;

            // Generate a unique blob name
            var blobName = $"{Guid.NewGuid():N}_{Path.GetFileName(input.FileName)}";

            // Save the file to blob storage
            using (var memoryStream = new MemoryStream(input.FileContent))
            {
                await _blobContainer.SaveAsync(blobName, memoryStream);
            }

            // Create an attachment entity
            var attachment = new Attachment(
                GuidGenerator.Create(),
                input.FileName,
                input.ContentType,
                fileSize,
                blobName,
                input.Description,
                input.ReferenceId,
                input.ReferenceType
            );

            // Save the attachment metadata to the database
            await _attachmentRepository.InsertAsync(attachment);

            // Return the result
            return new FileUploadResultDto
            {
                Id = attachment.Id,
                FileName = attachment.FileName ?? "unknown_file",
                ContentType = attachment.ContentType ?? "application/octet-stream",
                Size = attachment.Size,
                UploadTime = attachment.CreationTime,
                Url = GetFileUrl(attachment.Id),
                StreamUrl = GetFileStreamUrl(attachment.Id)
            };
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(L["FailedToUploadFile"], ex.Message);
        }
    }


    /// <summary>
    /// Downloads a file by ID
    /// </summary>
    public async Task<FileDto> DownloadAsync(Guid id)
    {
        // Get the attachment metadata
        var attachment = await _attachmentRepository.GetAsync(id);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["FileNotFound"]);
        }

        try
        {
            // Check if BlobName is null
            if (string.IsNullOrEmpty(attachment.BlobName))
            {
                throw new UserFriendlyException(L["FileContentNotFound"]);
            }

            // Get the file from blob storage
            var stream = await _blobContainer.GetAsync(attachment.BlobName);
            if (stream == null)
            {
                throw new UserFriendlyException(L["FileContentNotFound"]);
            }

            // Read the file content
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            return new FileDto(
                attachment.FileName ?? "unknown_file",
                attachment.ContentType ?? "application/octet-stream",
                memoryStream.ToArray()
            );
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(L["FailedToDownloadFile"], ex.Message);
        }
    }


    /// <summary>
    /// Gets a list of attachments by reference ID and type
    /// </summary>
    public async Task<List<FileUploadResultDto>> GetByReferenceAsync(Guid referenceId, string referenceType)
    {
        // Get attachments by reference
        var attachments = await _attachmentRepository.GetListAsync(
            a => a.ReferenceId == referenceId && a.ReferenceType == referenceType
        );

        // Map to DTOs
        return attachments.Select(a => new FileUploadResultDto
        {
            Id = a.Id,
            FileName = a.FileName ?? "unknown_file",
            ContentType = a.ContentType ?? "application/octet-stream",
            Size = a.Size,
            UploadTime = a.CreationTime,
            Url = GetFileUrl(a.Id),
            StreamUrl = GetFileStreamUrl(a.Id)
        }).ToList();
    }

    /// <summary>
    /// Deletes a file by ID
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id)
    {
        // Get the attachment metadata
        var attachment = await _attachmentRepository.FindAsync(id);
        if (attachment == null)
        {
            return false;
        }

        try
        {
            // Delete the file from blob storage if BlobName is not null
            if (!string.IsNullOrEmpty(attachment.BlobName))
            {
                await _blobContainer.DeleteAsync(attachment.BlobName);
            }

            // Delete the attachment metadata from the database
            await _attachmentRepository.DeleteAsync(attachment);

            return true;
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(L["FailedToDeleteFile"], ex.Message);
        }
    }

    /// <summary>
    /// Downloads multiple files as a zip archive
    /// </summary>
    public async Task<byte[]> BulkDownloadAsync(BulkDownloadDto input)
    {
        if (input.FileIds == null || input.FileIds.Count == 0)
        {
            throw new UserFriendlyException(L["NoFilesSelectedForDownload"]);
        }

        // Create a memory stream to hold the zip file
        using var memoryStream = new MemoryStream();

        // Create the zip archive
        using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
        {
            // Dictionary to track file names and avoid duplicates
            var fileNames = new Dictionary<string, int>();

            // Process each file
            foreach (var fileId in input.FileIds)
            {
                try
                {
                    // Get the attachment metadata
                    var attachment = await _attachmentRepository.FindAsync(fileId);
                    if (attachment == null)
                    {
                        // Skip files that don't exist
                        continue;
                    }

                    // Check if BlobName is null
                    if (string.IsNullOrEmpty(attachment.BlobName))
                    {
                        // Skip files with no blob name
                        continue;
                    }

                    // Get the file content
                    var stream = await _blobContainer.GetAsync(attachment.BlobName);
                    if (stream == null)
                    {
                        // Skip files with no content
                        continue;
                    }

                    // Ensure unique file name in the zip
                    string fileName = attachment.FileName ?? "unknown_file";
                    if (fileNames.ContainsKey(fileName.ToLower()))
                    {
                        // If the file name already exists, add a number to make it unique
                        fileNames[fileName.ToLower()]++;
                        string extension = Path.GetExtension(fileName);
                        string nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                        fileName = $"{nameWithoutExtension}_{fileNames[fileName.ToLower()]}{extension}";
                    }
                    else
                    {
                        fileNames[fileName.ToLower()] = 1;
                    }

                    // Create an entry in the zip file
                    var entry = archive.CreateEntry(fileName, CompressionLevel.Optimal);

                    // Write the file content to the zip entry
                    using var entryStream = entry.Open();
                    await stream.CopyToAsync(entryStream);
                }
                catch (Exception ex)
                {
                    // Log the error but continue with other files
                    Logger.LogError(ex, "Error adding file {FileId} to zip archive", fileId);
                }
            }
        }

        // Reset the memory stream position
        memoryStream.Position = 0;

        // Generate a unique blob name for the zip file
        string zipFileName = !string.IsNullOrWhiteSpace(input.ZipFileName)
            ? $"{input.ZipFileName}_{DateTime.Now:yyyyMMdd_HHmmss}.zip"
            : $"attachments_{DateTime.Now:yyyyMMdd_HHmmss}.zip";

        string blobName = $"temp_zip_{Guid.NewGuid():N}_{zipFileName}";

        // Save the zip file to blob storage
        await _blobContainer.SaveAsync(blobName, memoryStream);

        // Create a temporary zip file record for cleanup
        var temporaryZipFileRepository = LazyServiceProvider.LazyGetRequiredService<ITemporaryZipFileRepository>();

        // Create a new temporary zip file entity
        var temporaryZipFile = new TemporaryZipFile(
            GuidGenerator.Create(),
            blobName,
            DateTime.UtcNow.AddMinutes(10) // Delete after 10 minutes
        );

        // Save the entity to the database
        await temporaryZipFileRepository.InsertAsync(temporaryZipFile);

        // Get the zip file content
        memoryStream.Position = 0;
        return memoryStream.ToArray();
    }

}