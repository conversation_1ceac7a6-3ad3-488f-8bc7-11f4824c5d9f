using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Models;

public class QueryParametersDto : PagedAndSortedResultRequestDto
{
    public int Page { get; set; } = 1;
    public List<SortInfo> Sort { get; set; } = [];
    public FilterGroup? FilterGroup { get; set; }

    public override int SkipCount => (Page - 1) * MaxResultCount;
    public override int MaxResultCount { get; set; } = 10;

    // Extension method or mapper to convert to domain object
    public QueryParameters ToDomain()
    {
        return new QueryParameters
        {
            Page = this.Page,
            PageSize = this.MaxResultCount,
            Sorting = this.Sorting,
            Sort = this.Sort,
            FilterGroup = this.FilterGroup
        };
    }
}