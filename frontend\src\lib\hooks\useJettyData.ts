import { useMutation } from '@tanstack/react-query';
import { postApiEkbJetty } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { FilterRequestDto } from '@/client/types.gen';

export const useJettyData = () => {
  return useMutation({
    mutationFn: async (filterRequest?: FilterRequestDto) => {
      try {
        const response = await postApiEkbJetty({ 
          body: filterRequest || {
            maxResultCount: 100,
            skipCount: 0
          }
        });
        return response.data || [];
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Jetty data';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('Jetty API Error:', error);
        toast({
          title: 'Error loading Jetty data',
          description: message,
          variant: 'destructive',
        });
        
        throw error; // Re-throw for mutation error handling
      }
    },
    onError: (error: unknown) => {
      console.error('Jetty mutation error:', error);
    }
  });
}; 