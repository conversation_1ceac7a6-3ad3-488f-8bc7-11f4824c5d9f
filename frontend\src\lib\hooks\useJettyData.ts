import { useMutation } from '@tanstack/react-query';
import { postApiEkbJetty, postApiIdjasApprovalSubmit } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { FilterRequestDto, SubmitApprovalDto } from '@/client/types.gen';

export const useJettyData = () => {
  return useMutation({
    mutationFn: async (filterRequest?: FilterRequestDto) => {
      try {
        const response = await postApiEkbJetty({
          body: filterRequest || {
            maxResultCount: 100,
            skipCount: 0
          }
        });
        return response.data || [];
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Jetty data';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }

        console.error('Jetty API Error:', error);
        toast({
          title: 'Error loading Jetty data',
          description: message,
          variant: 'destructive',
        });

        throw error; // Re-throw for mutation error handling
      }
    },
    onError: (error: unknown) => {
      console.error('Jetty mutation error:', error);
    }
  });
};

export const useSubmitForApproval = () => {
  return useMutation({
    mutationFn: async (submitData: SubmitApprovalDto) => {
      try {
        const response = await postApiIdjasApprovalSubmit({
          body: submitData
        });
        return response.data;
      } catch (error: unknown) {
        let message = 'Unknown error occurred while submitting for approval';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }

        console.error('Submit Approval API Error:', error);
        toast({
          title: 'Error submitting for approval',
          description: message,
          variant: 'destructive',
        });

        throw error; // Re-throw for mutation error handling
      }
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Successfully submitted for approval',
        variant: 'default',
      });
    },
    onError: (error: unknown) => {
      console.error('Submit approval mutation error:', error);
    }
  });
};