using Imip.JettyApproval.JettyRequests;
using Imip.JettyApproval.Mapping.Mappers;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Permissions.Apps;
using Imip.JettyApproval.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service for JettyRequestItem entity
/// </summary>
[Authorize(JettyApprovalPermission.PolicyJettyRequestItem.Default)]
public class JettyRequestItemAppService :
    CrudAppService<JettyRequestItem, JettyRequestItemDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateJettyRequestItemDto, CreateUpdateJettyRequestItemDto>,
    IJettyRequestItemAppService
{
    private readonly IJettyRequestItemRepository _jettyRequestItemRepository;
    private readonly JettyRequestItemMapper _mapper;
    private readonly ILogger<JettyRequestItemAppService> _logger;

    public JettyRequestItemAppService(
        IJettyRequestItemRepository jettyRequestItemRepository,
        JettyRequestItemMapper mapper,
        ILogger<JettyRequestItemAppService> logger)
        : base(jettyRequestItemRepository)
    {
        _jettyRequestItemRepository = jettyRequestItemRepository;
        _mapper = mapper;
        _logger = logger;
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequestItem.Create)]
    public override async Task<JettyRequestItemDto> CreateAsync(CreateUpdateJettyRequestItemDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _jettyRequestItemRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequestItem.Edit)]
    public override async Task<JettyRequestItemDto> UpdateAsync(Guid id, CreateUpdateJettyRequestItemDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _jettyRequestItemRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _jettyRequestItemRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequestItem.Delete)]
    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _jettyRequestItemRepository.GetAsync(id);

        await _jettyRequestItemRepository.DeleteAsync(entity, autoSave: true);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequestItem.View)]
    public override async Task<JettyRequestItemDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _jettyRequestItemRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequestItem.View)]
    public override async Task<PagedResultDto<JettyRequestItemDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _jettyRequestItemRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<JettyRequestItemDto>(totalCount, dtos);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequestItem.View)]
    public virtual async Task<PagedResultDto<JettyRequestItemDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _jettyRequestItemRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<JettyRequestItemDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<JettyRequestItem> ApplyDynamicQuery(IQueryable<JettyRequestItem> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<JettyRequestItem>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<JettyRequestItem>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}