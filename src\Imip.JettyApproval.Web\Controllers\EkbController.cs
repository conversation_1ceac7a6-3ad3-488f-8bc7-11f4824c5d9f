using System.Threading.Tasks;
using Imip.JettyApproval.Web.Services;
using Imip.JettyApproval.Web.Services.Dtos.ExternalVessel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using System;
using Volo.Abp;

namespace Imip.JettyApproval.Web.Controllers;

[Route("api/[controller]")]
public class EkbController : AbpController
{
    private readonly AppToAppService _appToAppService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<EkbController> _logger;

    public EkbController(AppToAppService appToAppService, IConfiguration configuration, ILogger<EkbController> logger)
    {
        _appToAppService = appToAppService;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpPost("vessel-header")]
    public async Task<PagedResultDto<VesselHeaderDto>> SearchVessel([FromBody] VesselListRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/vessel/vessel-headers";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<VesselHeaderDto>>(apiUrl, endpoint, request);
        return result;
    }

    [HttpPost("agent")]
    public async Task<PagedResultDto<AgentDto>> SearchAgent([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/master/agent/filter-list";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<AgentDto>>(apiUrl, endpoint, request);
        return result;
    }

    [HttpPost("tenant")]
    public async Task<PagedResultDto<MasterTenantDto>> SearchTenant([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/tenant/filter-list";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<MasterTenantDto>>(apiUrl, endpoint, request);
        return result;
    }

    [HttpPost("jetty")]
    public async Task<PagedResultDto<JettyDto>> SearchJetty([FromBody] FilterRequestDto request)
    {
        try
        {
            var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
            var endpoint = "api/ekb/jetty/filter-list";
            _logger.LogInformation("Calling EKBD API: {0}", apiUrl + endpoint);
            var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<JettyDto>>(apiUrl, endpoint, request);
            return result;
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Authentication error in SearchJetty: {Message}", ex.Message);

            // Return a proper error response that the frontend can handle
            throw new UserFriendlyException(
                "Authentication required",
                "Your session has expired. Please log in again.",
                details: ex.Message
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling EKBD API for jetty search");
            throw new UserFriendlyException(
                "External API Error",
                "Unable to retrieve jetty data. Please try again later.",
                details: ex.Message
            );
        }
    }

    [HttpPost("cargo")]
    public async Task<PagedResultDto<CargoDto>> SearchCargo([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/cargo/filter-list";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<CargoDto>>(apiUrl, endpoint, request);
        return result;
    }

    [HttpPost("business-partner")]
    public async Task<PagedResultDto<BusinessPartnerDto>> SearchBusinessPartner([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/business-partner/filter-list";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<BusinessPartnerDto>>(apiUrl, endpoint, request);
        return result;
    }

    [HttpPost("destination-port")]
    public async Task<PagedResultDto<DestinationPortDto>> SearchDestinationPort([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/destination-port/filter-list";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<DestinationPortDto>>(apiUrl, endpoint, request);
        return result;
    }

    [HttpPost("surveyor")]
    public async Task<PagedResultDto<SurveyorDto>> SearchSurveyor([FromBody] FilterRequestDto request)
    {
        var apiUrl = _configuration["ExternalApps:EkbdApi:BaseUrl"];
        var endpoint = "api/ekb/surveyor/filter-list";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<SurveyorDto>>(apiUrl, endpoint, request);
        return result;
    }
}
