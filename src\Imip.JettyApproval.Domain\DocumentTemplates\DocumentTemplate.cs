using System;
using Imip.JettyApproval.Documents;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.JettyApproval.DocumentTemplates;

/// <summary>
/// Entity for storing document template metadata
/// </summary>
public class DocumentTemplate : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// The name of the template
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// The document type
    /// </summary>
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// The attachment ID that contains the DOCX template
    /// </summary>
    public Guid AttachmentId { get; set; }

    /// <summary>
    /// Description of the template
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is the default template for the document type
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected DocumentTemplate()
    {
    }

    /// <summary>
    /// Creates a new DocumentTemplate
    /// </summary>
    public DocumentTemplate(
        Guid id,
        string name,
        DocumentType documentType,
        Guid attachmentId,
        string? description,
        bool isDefault = false)
        : base(id)
    {
        Name = name;
        DocumentType = documentType;
        AttachmentId = attachmentId;
        Description = description;
        IsDefault = isDefault;
    }
}

