﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class RemoveJettyRequestItemIdFromApprovalStage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalStages_JettyRequestItems_JettyRequestItemId",
                table: "ApprovalStages");

            migrationBuilder.DropIndex(
                name: "IX_ApprovalStages_JettyRequestItemId",
                table: "ApprovalStages");

            migrationBuilder.DropColumn(
                name: "JettyRequestItemId",
                table: "ApprovalStages");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "JettyRequestItemId",
                table: "ApprovalStages",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApprovalStages_JettyRequestItemId",
                table: "ApprovalStages",
                column: "JettyRequestItemId");

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalStages_JettyRequestItems_JettyRequestItemId",
                table: "ApprovalStages",
                column: "JettyRequestItemId",
                principalTable: "JettyRequestItems",
                principalColumn: "Id");
        }
    }
}
