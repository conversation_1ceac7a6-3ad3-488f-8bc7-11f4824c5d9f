using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.JettyRequests;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.JettyRequests;

/// <summary>
/// Repository implementation for JettyRequest entity
/// </summary>
public class JettyRequestRepository : EfCoreRepository<JettyApprovalDbContext, JettyRequest, Guid>, IJettyRequestRepository
{
    public JettyRequestRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<JettyRequest>> GetByVesselNameAsync(string vesselName)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequest>()
            .Where(x => x.VesselName != null && x.VesselName.Contains(vesselName))
            .ToListAsync();
    }

    public async Task<List<JettyRequest>> GetByJettyAsync(string jetty)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequest>()
            .Where(x => x.Jetty != null && x.Jetty.Contains(jetty))
            .ToListAsync();
    }

    public async Task<List<JettyRequest>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequest>()
            .Where(x => x.ArrivalDate >= startDate && x.ArrivalDate <= endDate)
            .ToListAsync();
    }

    public async Task<JettyRequest?> GetWithItemsAsync(Guid id)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequest>()
            .Include(x => x.Items)
            .FirstOrDefaultAsync(x => x.Id == id);
    }

    public async Task<List<JettyRequest>> GetListWithItemsAsync()
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequest>()
            .Include(x => x.Items)
            .ToListAsync();
    }
}