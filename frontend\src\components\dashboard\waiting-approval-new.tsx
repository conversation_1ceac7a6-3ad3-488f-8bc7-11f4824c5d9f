"use client"

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { usePendingApprovals } from '@/lib/hooks/usePendingApprovals'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'

export default function WaitingApproval() {
  const { data: pendingApprovals, isLoading, error } = usePendingApprovals(10)

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Waiting for Approval</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Waiting for Approval</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[200px] text-muted-foreground">
            Error loading pending approvals
          </div>
        </CardContent>
      </Card>
    )
  }

  const approvals = pendingApprovals?.items || []

  return (
    <Card>
      <CardHeader>
        <CardTitle>Waiting for Approval</CardTitle>
      </CardHeader>
      <CardContent>
        {approvals.length === 0 ? (
          <div className="flex items-center justify-center h-[200px] text-muted-foreground">
            No pending approvals
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Request ID</TableHead>
                <TableHead>Stage</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {approvals.slice(0, 5).map((approval) => (
                <TableRow key={approval.id}>
                  <TableCell className="font-medium">
                    {approval.id?.substring(0, 8)}...
                  </TableCell>
                  <TableCell>
                    {approval.approvalTemplate?.name || 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Badge variant={approval.status === 'Pending' ? 'secondary' : 'default'}>
                      {approval.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {approval.creationTime ? new Date(approval.creationTime).toLocaleDateString() : 'N/A'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
