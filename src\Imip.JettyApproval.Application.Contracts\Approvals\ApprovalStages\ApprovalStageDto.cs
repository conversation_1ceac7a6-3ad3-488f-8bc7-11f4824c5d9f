using System;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.JettyRequests;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// DTO for ApprovalStage entity
/// </summary>
public class ApprovalStageDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    public Guid ApprovalTemplateId { get; set; }

    /// <summary>
    /// ID of the approver for this stage
    /// </summary>
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Action date and time
    /// </summary>
    public DateTime? ActionDate { get; set; }

    /// <summary>
    /// Document ID for this stage
    /// </summary>
    public string? DocumentId { get; set; }

    /// <summary>
    /// ID of the requester
    /// </summary>
    public string? RequesterId { get; set; }

    /// <summary>
    /// Request date and time
    /// </summary>
    public DateTime? RequestDate { get; set; }

    /// <summary>
    /// Status of the approval stage
    /// </summary>
    public ApprovalStatus Status { get; set; }

    /// <summary>
    /// Additional notes for the stage
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Approval template information
    /// </summary>
    public ApprovalTemplateDto? ApprovalTemplate { get; set; }

    /// <summary>
    /// JettyRequestItem (document) information with header
    /// </summary>
    public JettyRequestItemDto? JettyRequestItem { get; set; }

    /// <summary>
    /// Approver user name
    /// </summary>
    public string? ApproverUserName { get; set; }

    /// <summary>
    /// Requester user name
    /// </summary>
    public string? RequesterUserName { get; set; }
}