using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// Repository interface for ApprovalStage entity
/// </summary>
public interface IApprovalStageRepository : IRepository<ApprovalStage, Guid>
{
    /// <summary>
    /// Gets stages by approval template ID
    /// </summary>
    Task<List<ApprovalStage>> GetByApprovalTemplateIdAsync(Guid approvalTemplateId);

    /// <summary>
    /// Gets stages by approver ID
    /// </summary>
    Task<List<ApprovalStage>> GetByApproverIdAsync(Guid approverId);

    /// <summary>
    /// Gets stages by status
    /// </summary>
    Task<List<ApprovalStage>> GetByStatusAsync(ApprovalStatus status);

    /// <summary>
    /// Gets stages by requester ID
    /// </summary>
    Task<List<ApprovalStage>> GetByRequesterIdAsync(string requesterId);
}