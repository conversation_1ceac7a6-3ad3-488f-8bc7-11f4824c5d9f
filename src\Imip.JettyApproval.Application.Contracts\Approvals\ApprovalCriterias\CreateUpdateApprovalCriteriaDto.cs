using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// DTO for creating and updating ApprovalCriteria entity
/// </summary>
public class CreateUpdateApprovalCriteriaDto
{
    /// <summary>
    /// ID of the approval criteria (empty for new records, existing ID for updates)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    // [Required]
    public Guid? ApprovalId { get; set; }

    /// <summary>
    /// Type of document for approval criteria
    /// </summary>
    [StringLength(100)]
    public string? DocumentType { get; set; }
}