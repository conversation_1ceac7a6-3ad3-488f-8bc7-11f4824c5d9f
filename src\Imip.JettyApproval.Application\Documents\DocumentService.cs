using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.DocumentTemplates;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;
using Imip.JettyApproval.Mapping.Mappers;
using Volo.Abp.Application.Dtos;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Services;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// Service for document operations
/// </summary>
[Authorize]
public class DocumentService : ApplicationService, IDocumentService
{
    private readonly IDocumentTemplateRepository _documentTemplateRepository;
    private readonly IDocumentTemplateRepository _documentTemplateCustomRepository;
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly IBlobContainer _blobContainer;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ISyncfusionDocxToPdfService _syncfusionDocxToPdfService;
    private readonly DocumentTemplateMapper _documentTemplateMapper;


    public DocumentService(
        IDocumentTemplateRepository documentTemplateRepository,
        IDocumentTemplateRepository documentTemplateCustomRepository,
        IAttachmentRepository attachmentRepository,
        IBlobContainer blobContainer,
        IAttachmentAppService attachmentAppService,
        IUnitOfWorkManager unitOfWorkManager,
        ISyncfusionDocxToPdfService syncfusionDocxToPdfService,
        DocumentTemplateMapper documentTemplateMapper)
    {
        _documentTemplateRepository = documentTemplateRepository;
        _documentTemplateCustomRepository = documentTemplateCustomRepository;
        _attachmentRepository = attachmentRepository;
        _blobContainer = blobContainer;
        _attachmentAppService = attachmentAppService;
        _unitOfWorkManager = unitOfWorkManager;
        _syncfusionDocxToPdfService = syncfusionDocxToPdfService;
        _documentTemplateMapper = documentTemplateMapper;
    }

    /// <summary>
    /// Gets all document templates
    /// </summary>
    public async Task<List<DocumentTemplateDto>> GetAllTemplatesAsync()
    {
        var templates = await _documentTemplateRepository.GetListAsync();
        return _documentTemplateMapper.MapToDtoList(templates);
    }

    /// <summary>
    /// Gets document templates by document type
    /// </summary>
    public async Task<List<DocumentTemplateDto>> GetTemplatesByTypeAsync(DocumentType documentType)
    {
        var templates = await _documentTemplateCustomRepository.GetByDocumentTypeAsync(documentType);
        return _documentTemplateMapper.MapToDtoList(templates);
    }

    /// <summary>
    /// Gets a document template by ID
    /// </summary>
    public async Task<DocumentTemplateDto> GetTemplateByIdAsync(Guid id)
    {
        var template = await _documentTemplateRepository.GetAsync(id);
        var dto = _documentTemplateMapper.MapToDto(template);
        dto.DocumentTypeName = L[$"DocumentType:{template.DocumentType}"];
        return dto;
    }

    /// <summary>
    /// Creates a new document template
    /// </summary>
    public async Task<DocumentTemplateDto> CreateTemplateAsync(CreateDocumentTemplateDto input)
    {
        // Verify that the attachment exists and is a DOCX file
        var attachment = await _attachmentRepository.GetAsync(input.AttachmentId);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["AttachmentNotFound"]);
        }

        if (attachment.ContentType == null || !attachment.ContentType.Equals(
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                StringComparison.OrdinalIgnoreCase))
        {
            throw new UserFriendlyException(L["AttachmentMustBeDocx"]);
        }

        // If this is set as the default template, unset any existing default templates for this document type
        if (input.IsDefault)
        {
            var existingDefaultTemplates =
                await _documentTemplateCustomRepository.GetByDocumentTypeAsync(input.DocumentType);
            foreach (var existingTemplate in existingDefaultTemplates.Where(t => t.IsDefault))
            {
                existingTemplate.IsDefault = false;
                await _documentTemplateRepository.UpdateAsync(existingTemplate);
            }
        }

        // Create the new template
        var template = new DocumentTemplate(
            GuidGenerator.Create(),
            input.Name,
            input.DocumentType,
            input.AttachmentId,
            input.Description,
            input.IsDefault
        );

        await _documentTemplateRepository.InsertAsync(template);
        var dto = _documentTemplateMapper.MapToDto(template);
        dto.DocumentTypeName = L[$"DocumentType:{template.DocumentType}"];
        return dto;
    }

    /// <summary>
    /// Uploads a new document template with file
    /// </summary>
    public async Task<DocumentTemplateDto> UploadTemplateAsync(UploadDocumentTemplateDto input)
    {
        // Validate file
        if (input.File == null || input.File.Length == 0)
        {
            throw new UserFriendlyException(L["FileIsRequired"]);
        }

        if (!input.File.ContentType.Equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                StringComparison.OrdinalIgnoreCase))
        {
            throw new UserFriendlyException(L["FileMustBeDocx"]);
        }

        // Use a single unit of work for all operations to ensure transaction consistency
        using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
        {
            try
            {
                // First, upload the file to blob storage and create an attachment record
                using var memoryStream = new MemoryStream();
                await input.File.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();

                // Create attachment through the attachment app service
                var fileUploadDto = new FileUploadDto
                {
                    Description = input.Description,
                    ReferenceType = "DocumentTemplate"
                };

                var uploadResult = await _attachmentAppService.UploadFileAsync(
                    fileUploadDto,
                    input.File.FileName,
                    input.File.ContentType,
                    fileBytes);

                // If this is set as the default template, unset any existing default templates for this document type
                if (input.IsDefault)
                {
                    var existingDefaultTemplates =
                        await _documentTemplateCustomRepository.GetByDocumentTypeAsync(input.DocumentType);
                    foreach (var existingTemplate in existingDefaultTemplates.Where(t => t.IsDefault))
                    {
                        existingTemplate.IsDefault = false;
                        await _documentTemplateRepository.UpdateAsync(existingTemplate);
                    }
                }

                // Create the new template with the uploaded attachment
                var template = new DocumentTemplate(
                    GuidGenerator.Create(),
                    input.Name,
                    input.DocumentType,
                    uploadResult.Id,
                    input.Description,
                    input.IsDefault
                );

                await _documentTemplateRepository.InsertAsync(template);

                // Update the attachment's reference ID now that we have the template ID
                // Use direct repository access to avoid entity tracking issues
                var attachment = await _attachmentRepository.FindAsync(uploadResult.Id);
                if (attachment != null)
                {
                    attachment.ReferenceId = template.Id;
                    await _attachmentRepository.UpdateAsync(attachment);
                }

                await uow.CompleteAsync();
                var dto = _documentTemplateMapper.MapToDto(template);
                dto.DocumentTypeName = L[$"DocumentType:{template.DocumentType}"];
                return dto;
            }
            catch (UserFriendlyException)
            {
                throw;
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                Logger.LogError(ex, "Error uploading document template: {Message}", ex.Message);
                throw new UserFriendlyException(L["FailedToUploadTemplate"], ex.Message);
            }
        }
    }


    /// <summary>
    /// Updates a document template
    /// </summary>
    public async Task<DocumentTemplateDto> UpdateTemplateAsync(Guid id, UpdateDocumentTemplateDto input)
    {
        var template = await _documentTemplateRepository.GetAsync(id);

        // If this is set as the default template, unset any existing default templates for this document type
        if (input.IsDefault && !template.IsDefault)
        {
            var existingDefaultTemplates =
                await _documentTemplateCustomRepository.GetByDocumentTypeAsync(input.DocumentType);
            foreach (var existingTemplate in existingDefaultTemplates.Where(t => t.IsDefault))
            {
                existingTemplate.IsDefault = false;
                await _documentTemplateRepository.UpdateAsync(existingTemplate);
            }
        }

        // Update the template
        _documentTemplateMapper.MapToEntity(input, template);
        await _documentTemplateRepository.UpdateAsync(template);
        var dto = _documentTemplateMapper.MapToDto(template);
        dto.DocumentTypeName = L[$"DocumentType:{template.DocumentType}"];
        return dto;
    }

    public virtual async Task<PagedResultDto<DocumentTemplateDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _documentTemplateRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_documentTemplateMapper.MapToDto).ToList();
        return new PagedResultDto<DocumentTemplateDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<DocumentTemplate> ApplyDynamicQuery(IQueryable<DocumentTemplate> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<DocumentTemplate>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<DocumentTemplate>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }

    /// <summary>
    /// Deletes a document template
    /// </summary>
    public async Task DeleteTemplateAsync(Guid id)
    {
        await _documentTemplateRepository.DeleteAsync(id);
    }

    public Task<FileDto> ConvertDocxToPdfAsync(DocxToPdfConversionDto input)
    {
        throw new NotImplementedException();
    }
}