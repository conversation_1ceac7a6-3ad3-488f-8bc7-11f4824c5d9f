// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-fetch';
import type { GetApiAbpApplicationConfigurationData, GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, GetApiAbpMultiTenancyTenantsByNameByNameData, GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, GetApiAbpMultiTenancyTenantsByIdByIdData, GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, GetAccountExternalLoginData, GetAccountExternalLoginResponses, GetAccountExternalLoginCallbackData, GetAccountExternalLoginCallbackResponses, GetAccountLoginData, GetAccountLoginResponses, GetAccountLogoutData, GetAccountLogoutResponses, GetAccountLogoutRedirectData, GetAccountLogoutRedirectResponses, GetAccountSignoutCallbackOidcData, GetAccountSignoutCallbackOidcResponses, PostApiAccountRegisterData, PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, PostApiAccountSendPasswordResetCodeData, PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, PostApiAccountVerifyPasswordResetTokenData, PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, PostApiAccountResetPasswordData, PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, GetData, GetResponses, GetAdminData, GetAdminResponses, GetApplicationData, GetApplicationResponses, GetApplicationCreateData, GetApplicationCreateResponses, GetApplicationStatusData, GetApplicationStatusResponses, GetApplicationListData, GetApplicationListResponses, GetApplicationDraftData, GetApplicationDraftResponses, GetApplicationByIdEditData, GetApplicationByIdEditResponses, GetApprovalData, GetApprovalResponses, GetApprovalHistoryData, GetApprovalHistoryResponses, GetJettyData, GetJettyResponses, GetJettyScheduleData, GetJettyScheduleResponses, GetJettyDockedVesselData, GetJettyDockedVesselResponses, GetSettingsData, GetSettingsResponses, GetReportData, GetReportResponses, GetApprovalTemplateData, GetApprovalTemplateResponses, GetDocumentTemplateData, GetDocumentTemplateResponses, PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentData, PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentResponses, PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentErrors, PostApiIdjasApplicationDocumentGenerateApplicationDocumentData, PostApiIdjasApplicationDocumentGenerateApplicationDocumentResponses, PostApiIdjasApplicationDocumentGenerateApplicationDocumentErrors, PostApiIdjasApprovalSubmitData, PostApiIdjasApprovalSubmitResponses, PostApiIdjasApprovalApproveByIdData, PostApiIdjasApprovalApproveByIdResponses, PostApiIdjasApprovalRejectByIdData, PostApiIdjasApprovalRejectByIdResponses, GetApiIdjasApprovalApproverData, GetApiIdjasApprovalApproverResponses, GetApiIdjasApprovalApproverErrors, PostApiIdjasApprovalApproverData, PostApiIdjasApprovalApproverResponses, PostApiIdjasApprovalApproverErrors, DeleteApiIdjasApprovalApproverByIdData, DeleteApiIdjasApprovalApproverByIdResponses, DeleteApiIdjasApprovalApproverByIdErrors, GetApiIdjasApprovalApproverByIdData, GetApiIdjasApprovalApproverByIdResponses, GetApiIdjasApprovalApproverByIdErrors, PutApiIdjasApprovalApproverByIdData, PutApiIdjasApprovalApproverByIdResponses, PutApiIdjasApprovalApproverByIdErrors, PostApiIdjasApprovalApproverFilterListData, PostApiIdjasApprovalApproverFilterListResponses, PostApiIdjasApprovalApproverFilterListErrors, GetApiIdjasApprovalCriteriaData, GetApiIdjasApprovalCriteriaResponses, GetApiIdjasApprovalCriteriaErrors, PostApiIdjasApprovalCriteriaData, PostApiIdjasApprovalCriteriaResponses, PostApiIdjasApprovalCriteriaErrors, DeleteApiIdjasApprovalCriteriaByIdData, DeleteApiIdjasApprovalCriteriaByIdResponses, DeleteApiIdjasApprovalCriteriaByIdErrors, GetApiIdjasApprovalCriteriaByIdData, GetApiIdjasApprovalCriteriaByIdResponses, GetApiIdjasApprovalCriteriaByIdErrors, PutApiIdjasApprovalCriteriaByIdData, PutApiIdjasApprovalCriteriaByIdResponses, PutApiIdjasApprovalCriteriaByIdErrors, PostApiIdjasApprovalCriteriaFilterListData, PostApiIdjasApprovalCriteriaFilterListResponses, PostApiIdjasApprovalCriteriaFilterListErrors, GetApiIdjasApprovalDelegationData, GetApiIdjasApprovalDelegationResponses, GetApiIdjasApprovalDelegationErrors, PostApiIdjasApprovalDelegationData, PostApiIdjasApprovalDelegationResponses, PostApiIdjasApprovalDelegationErrors, DeleteApiIdjasApprovalDelegationByIdData, DeleteApiIdjasApprovalDelegationByIdResponses, DeleteApiIdjasApprovalDelegationByIdErrors, GetApiIdjasApprovalDelegationByIdData, GetApiIdjasApprovalDelegationByIdResponses, GetApiIdjasApprovalDelegationByIdErrors, PutApiIdjasApprovalDelegationByIdData, PutApiIdjasApprovalDelegationByIdResponses, PutApiIdjasApprovalDelegationByIdErrors, PostApiIdjasApprovalDelegationFilterListData, PostApiIdjasApprovalDelegationFilterListResponses, PostApiIdjasApprovalDelegationFilterListErrors, GetApiIdjasApprovalStageData, GetApiIdjasApprovalStageResponses, GetApiIdjasApprovalStageErrors, PostApiIdjasApprovalStageData, PostApiIdjasApprovalStageResponses, PostApiIdjasApprovalStageErrors, DeleteApiIdjasApprovalStageByIdData, DeleteApiIdjasApprovalStageByIdResponses, DeleteApiIdjasApprovalStageByIdErrors, GetApiIdjasApprovalStageByIdData, GetApiIdjasApprovalStageByIdResponses, GetApiIdjasApprovalStageByIdErrors, PutApiIdjasApprovalStageByIdData, PutApiIdjasApprovalStageByIdResponses, PutApiIdjasApprovalStageByIdErrors, PostApiIdjasApprovalStageFilterListData, PostApiIdjasApprovalStageFilterListResponses, PostApiIdjasApprovalStageFilterListErrors, PostApiIdjasApprovalStageSubmitForApprovalData, PostApiIdjasApprovalStageSubmitForApprovalResponses, PostApiIdjasApprovalStageSubmitForApprovalErrors, GetApiIdjasApprovalTemplateData, GetApiIdjasApprovalTemplateResponses, GetApiIdjasApprovalTemplateErrors, PostApiIdjasApprovalTemplateData, PostApiIdjasApprovalTemplateResponses, PostApiIdjasApprovalTemplateErrors, DeleteApiIdjasApprovalTemplateByIdData, DeleteApiIdjasApprovalTemplateByIdResponses, DeleteApiIdjasApprovalTemplateByIdErrors, GetApiIdjasApprovalTemplateByIdData, GetApiIdjasApprovalTemplateByIdResponses, GetApiIdjasApprovalTemplateByIdErrors, PutApiIdjasApprovalTemplateByIdData, PutApiIdjasApprovalTemplateByIdResponses, PutApiIdjasApprovalTemplateByIdErrors, PostApiIdjasApprovalTemplateFilterListData, PostApiIdjasApprovalTemplateFilterListResponses, PostApiIdjasApprovalTemplateFilterListErrors, GetApiAppToAppDataData, GetApiAppToAppDataResponses, PostApiAppToAppDataData, PostApiAppToAppDataResponses, GetApiAppToAppCustomData, GetApiAppToAppCustomResponses, GetApiAttachmentDownloadByIdData, GetApiAttachmentDownloadByIdErrors, GetApiAttachmentStreamByIdData, GetApiAttachmentStreamByIdErrors, PostApiAttachmentUploadData, PostApiAttachmentUploadResponses, PostApiAttachmentUploadErrors, GetApiAttachmentByReferenceData, GetApiAttachmentByReferenceResponses, GetApiAttachmentByReferenceErrors, DeleteApiAttachmentByIdData, DeleteApiAttachmentByIdResponses, DeleteApiAttachmentByIdErrors, PostApiAttachmentBulkDownloadData, PostApiAttachmentBulkDownloadErrors, PostApiAttachmentUploadFormData, PostApiAttachmentUploadFormResponses, PostApiAttachmentUploadFormErrors, GetApiDashboardStatisticsData, GetApiDashboardStatisticsResponses, GetApiDashboardPendingApprovalsData, GetApiDashboardPendingApprovalsResponses, GetApiDashboardJettyStatusData, GetApiDashboardJettyStatusResponses, GetApiDebugCurrentUserData, GetApiDebugCurrentUserResponses, PostApiDebugClearUserCacheData, PostApiDebugClearUserCacheResponses, PostApiDebugUpdateUserNameData, PostApiDebugUpdateUserNameResponses, PostApiDebugForceUserSyncData, PostApiDebugForceUserSyncResponses, PostApiIdjasDocumentTemplatesUploadData, PostApiIdjasDocumentTemplatesUploadResponses, PostApiIdjasDocumentTemplatesUploadErrors, PostApiIdjasDocumentGenerateApplicationData, PostApiIdjasDocumentGenerateApplicationErrors, GetApiIdjasDocumentTemplatesData, GetApiIdjasDocumentTemplatesResponses, GetApiIdjasDocumentTemplatesErrors, GetApiIdjasDocumentTemplatesByTypeData, GetApiIdjasDocumentTemplatesByTypeResponses, GetApiIdjasDocumentTemplatesByTypeErrors, GetApiIdjasDocumentByIdTemplateByIdData, GetApiIdjasDocumentByIdTemplateByIdResponses, GetApiIdjasDocumentByIdTemplateByIdErrors, PostApiIdjasDocumentTemplateData, PostApiIdjasDocumentTemplateResponses, PostApiIdjasDocumentTemplateErrors, PostApiIdjasDocumentUploadTemplateData, PostApiIdjasDocumentUploadTemplateResponses, PostApiIdjasDocumentUploadTemplateErrors, DeleteApiIdjasDocumentByIdTemplateData, DeleteApiIdjasDocumentByIdTemplateResponses, DeleteApiIdjasDocumentByIdTemplateErrors, PutApiIdjasDocumentByIdTemplateData, PutApiIdjasDocumentByIdTemplateResponses, PutApiIdjasDocumentByIdTemplateErrors, PostApiIdjasDocumentFilterListData, PostApiIdjasDocumentFilterListResponses, PostApiIdjasDocumentFilterListErrors, PostApiIdjasDocumentConvertDocxToPdfData, PostApiIdjasDocumentConvertDocxToPdfResponses, PostApiIdjasDocumentConvertDocxToPdfErrors, PostApiAccountDynamicClaimsRefreshData, PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, PostApiEkbVesselHeaderData, PostApiEkbVesselHeaderResponses, PostApiEkbAgentData, PostApiEkbAgentResponses, PostApiEkbTenantData, PostApiEkbTenantResponses, PostApiEkbJettyData, PostApiEkbJettyResponses, PostApiEkbCargoData, PostApiEkbCargoResponses, PostApiEkbBusinessPartnerData, PostApiEkbBusinessPartnerResponses, PostApiEkbDestinationPortData, PostApiEkbDestinationPortResponses, PostApiEkbSurveyorData, PostApiEkbSurveyorResponses, PostApiExternalVesselSearchVesselsData, PostApiExternalVesselSearchVesselsResponses, PostApiIdentityServerUserQueryData, PostApiIdentityServerUserQueryResponses, PostApiIdjasJettyRequestGenerateNextDocNumData, PostApiIdjasJettyRequestGenerateNextDocNumResponses, PostApiIdjasJettyRequestGenerateNextDocNumErrors, GetApiIdjasJettyRequestData, GetApiIdjasJettyRequestResponses, GetApiIdjasJettyRequestErrors, PostApiIdjasJettyRequestData, PostApiIdjasJettyRequestResponses, PostApiIdjasJettyRequestErrors, DeleteApiIdjasJettyRequestByIdData, DeleteApiIdjasJettyRequestByIdResponses, DeleteApiIdjasJettyRequestByIdErrors, GetApiIdjasJettyRequestByIdData, GetApiIdjasJettyRequestByIdResponses, GetApiIdjasJettyRequestByIdErrors, PutApiIdjasJettyRequestByIdData, PutApiIdjasJettyRequestByIdResponses, PutApiIdjasJettyRequestByIdErrors, PostApiIdjasJettyRequestFilterListData, PostApiIdjasJettyRequestFilterListResponses, PostApiIdjasJettyRequestFilterListErrors, GetApiIdjasJettyRequestItemData, GetApiIdjasJettyRequestItemResponses, GetApiIdjasJettyRequestItemErrors, PostApiIdjasJettyRequestItemData, PostApiIdjasJettyRequestItemResponses, PostApiIdjasJettyRequestItemErrors, DeleteApiIdjasJettyRequestItemByIdData, DeleteApiIdjasJettyRequestItemByIdResponses, DeleteApiIdjasJettyRequestItemByIdErrors, GetApiIdjasJettyRequestItemByIdData, GetApiIdjasJettyRequestItemByIdResponses, GetApiIdjasJettyRequestItemByIdErrors, PutApiIdjasJettyRequestItemByIdData, PutApiIdjasJettyRequestItemByIdResponses, PutApiIdjasJettyRequestItemByIdErrors, PostApiIdjasJettyRequestItemFilterListData, PostApiIdjasJettyRequestItemFilterListResponses, PostApiIdjasJettyRequestItemFilterListErrors, PostApiAccountLoginData, PostApiAccountLoginResponses, PostApiAccountLoginErrors, GetApiAccountLogoutData, GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, PostApiAccountCheckPasswordData, PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, GetApiAccountMyProfileData, GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, PutApiAccountMyProfileData, PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, PostApiAccountMyProfileChangePasswordData, PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, DeleteApiMultiTenancyTenantsByIdData, DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsByIdData, GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, PutApiMultiTenancyTenantsByIdData, PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsData, GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, PostApiMultiTenancyTenantsData, PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiTokenAccessData, GetApiTokenAccessResponses, GetApiTokenRefreshData, GetApiTokenRefreshResponses, GetApiTokenStatusData, GetApiTokenStatusResponses, GetApiTokenValidData, GetApiTokenValidResponses, GetApiTokenExpirationData, GetApiTokenExpirationResponses, GetApiTokenDebugData, GetApiTokenDebugResponses, PostApiTokenValidationValidateData, PostApiTokenValidationValidateResponses, PostApiTokenValidationIsValidData, PostApiTokenValidationIsValidResponses, GetApiTokenValidationProtectedData, GetApiTokenValidationProtectedResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiAbpApplicationConfiguration = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, ThrowOnError>({
        url: '/api/abp/application-configuration',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByNameByName = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-name/{name}',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-id/{id}',
        ...options
    });
};

export const getAccountExternalLogin = <ThrowOnError extends boolean = false>(options?: Options<GetAccountExternalLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAccountExternalLoginResponses, unknown, ThrowOnError>({
        url: '/Account/ExternalLogin',
        ...options
    });
};

export const getAccountExternalLoginCallback = <ThrowOnError extends boolean = false>(options?: Options<GetAccountExternalLoginCallbackData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAccountExternalLoginCallbackResponses, unknown, ThrowOnError>({
        url: '/Account/ExternalLoginCallback',
        ...options
    });
};

export const getAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<GetAccountLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAccountLoginResponses, unknown, ThrowOnError>({
        url: '/Account/Login',
        ...options
    });
};

export const getAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAccountLogoutResponses, unknown, ThrowOnError>({
        url: '/Account/Logout',
        ...options
    });
};

export const getAccountLogoutRedirect = <ThrowOnError extends boolean = false>(options?: Options<GetAccountLogoutRedirectData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAccountLogoutRedirectResponses, unknown, ThrowOnError>({
        url: '/Account/LogoutRedirect',
        ...options
    });
};

export const getAccountSignoutCallbackOidc = <ThrowOnError extends boolean = false>(options?: Options<GetAccountSignoutCallbackOidcData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAccountSignoutCallbackOidcResponses, unknown, ThrowOnError>({
        url: '/Account/signout-callback-oidc',
        ...options
    });
};

export const postApiAccountRegister = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountRegisterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, ThrowOnError>({
        url: '/api/account/register',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountSendPasswordResetCode = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, ThrowOnError>({
        url: '/api/account/send-password-reset-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountVerifyPasswordResetToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, ThrowOnError>({
        url: '/api/account/verify-password-reset-token',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, ThrowOnError>({
        url: '/api/account/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const get = <ThrowOnError extends boolean = false>(options?: Options<GetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetResponses, unknown, ThrowOnError>({
        url: '/',
        ...options
    });
};

export const getAdmin = <ThrowOnError extends boolean = false>(options?: Options<GetAdminData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetAdminResponses, unknown, ThrowOnError>({
        url: '/admin',
        ...options
    });
};

export const getApplication = <ThrowOnError extends boolean = false>(options?: Options<GetApplicationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApplicationResponses, unknown, ThrowOnError>({
        url: '/application',
        ...options
    });
};

export const getApplicationCreate = <ThrowOnError extends boolean = false>(options?: Options<GetApplicationCreateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApplicationCreateResponses, unknown, ThrowOnError>({
        url: '/application/create',
        ...options
    });
};

export const getApplicationStatus = <ThrowOnError extends boolean = false>(options?: Options<GetApplicationStatusData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApplicationStatusResponses, unknown, ThrowOnError>({
        url: '/application/status',
        ...options
    });
};

export const getApplicationList = <ThrowOnError extends boolean = false>(options?: Options<GetApplicationListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApplicationListResponses, unknown, ThrowOnError>({
        url: '/application/list',
        ...options
    });
};

export const getApplicationDraft = <ThrowOnError extends boolean = false>(options?: Options<GetApplicationDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApplicationDraftResponses, unknown, ThrowOnError>({
        url: '/application/draft',
        ...options
    });
};

export const getApplicationByIdEdit = <ThrowOnError extends boolean = false>(options: Options<GetApplicationByIdEditData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApplicationByIdEditResponses, unknown, ThrowOnError>({
        url: '/application/{id}/edit',
        ...options
    });
};

export const getApproval = <ThrowOnError extends boolean = false>(options?: Options<GetApprovalData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApprovalResponses, unknown, ThrowOnError>({
        url: '/approval',
        ...options
    });
};

export const getApprovalHistory = <ThrowOnError extends boolean = false>(options?: Options<GetApprovalHistoryData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApprovalHistoryResponses, unknown, ThrowOnError>({
        url: '/approval/history',
        ...options
    });
};

export const getJetty = <ThrowOnError extends boolean = false>(options?: Options<GetJettyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetJettyResponses, unknown, ThrowOnError>({
        url: '/jetty',
        ...options
    });
};

export const getJettySchedule = <ThrowOnError extends boolean = false>(options?: Options<GetJettyScheduleData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetJettyScheduleResponses, unknown, ThrowOnError>({
        url: '/jetty/schedule',
        ...options
    });
};

export const getJettyDockedVessel = <ThrowOnError extends boolean = false>(options?: Options<GetJettyDockedVesselData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetJettyDockedVesselResponses, unknown, ThrowOnError>({
        url: '/jetty/docked-vessel',
        ...options
    });
};

export const getSettings = <ThrowOnError extends boolean = false>(options?: Options<GetSettingsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetSettingsResponses, unknown, ThrowOnError>({
        url: '/settings',
        ...options
    });
};

export const getReport = <ThrowOnError extends boolean = false>(options?: Options<GetReportData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetReportResponses, unknown, ThrowOnError>({
        url: '/report',
        ...options
    });
};

export const getApprovalTemplate = <ThrowOnError extends boolean = false>(options?: Options<GetApprovalTemplateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApprovalTemplateResponses, unknown, ThrowOnError>({
        url: '/approval-template',
        ...options
    });
};

export const getDocumentTemplate = <ThrowOnError extends boolean = false>(options?: Options<GetDocumentTemplateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetDocumentTemplateResponses, unknown, ThrowOnError>({
        url: '/document-template',
        ...options
    });
};

export const postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentResponses, PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentErrors, ThrowOnError>({
        url: '/api/idjas/application-document/generate-application-document-as-attachment',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasApplicationDocumentGenerateApplicationDocument = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApplicationDocumentGenerateApplicationDocumentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApplicationDocumentGenerateApplicationDocumentResponses, PostApiIdjasApplicationDocumentGenerateApplicationDocumentErrors, ThrowOnError>({
        url: '/api/idjas/application-document/generate-application-document',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasApprovalSubmit = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalSubmitData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalSubmitResponses, unknown, ThrowOnError>({
        url: '/api/idjas/approval/submit',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasApprovalApproveById = <ThrowOnError extends boolean = false>(options: Options<PostApiIdjasApprovalApproveByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiIdjasApprovalApproveByIdResponses, unknown, ThrowOnError>({
        url: '/api/idjas/approval/approve/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasApprovalRejectById = <ThrowOnError extends boolean = false>(options: Options<PostApiIdjasApprovalRejectByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiIdjasApprovalRejectByIdResponses, unknown, ThrowOnError>({
        url: '/api/idjas/approval/reject/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiIdjasApprovalApprover = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasApprovalApproverData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasApprovalApproverResponses, GetApiIdjasApprovalApproverErrors, ThrowOnError>({
        url: '/api/idjas/approval-approver',
        ...options
    });
};

export const postApiIdjasApprovalApprover = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalApproverData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalApproverResponses, PostApiIdjasApprovalApproverErrors, ThrowOnError>({
        url: '/api/idjas/approval-approver',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasApprovalApproverById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasApprovalApproverByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasApprovalApproverByIdResponses, DeleteApiIdjasApprovalApproverByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-approver/{id}',
        ...options
    });
};

export const getApiIdjasApprovalApproverById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasApprovalApproverByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasApprovalApproverByIdResponses, GetApiIdjasApprovalApproverByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-approver/{id}',
        ...options
    });
};

export const putApiIdjasApprovalApproverById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasApprovalApproverByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasApprovalApproverByIdResponses, PutApiIdjasApprovalApproverByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-approver/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasApprovalApproverFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalApproverFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalApproverFilterListResponses, PostApiIdjasApprovalApproverFilterListErrors, ThrowOnError>({
        url: '/api/idjas/approval-approver/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdjasApprovalCriteria = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasApprovalCriteriaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasApprovalCriteriaResponses, GetApiIdjasApprovalCriteriaErrors, ThrowOnError>({
        url: '/api/idjas/approval-criteria',
        ...options
    });
};

export const postApiIdjasApprovalCriteria = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalCriteriaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalCriteriaResponses, PostApiIdjasApprovalCriteriaErrors, ThrowOnError>({
        url: '/api/idjas/approval-criteria',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasApprovalCriteriaById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasApprovalCriteriaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasApprovalCriteriaByIdResponses, DeleteApiIdjasApprovalCriteriaByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-criteria/{id}',
        ...options
    });
};

export const getApiIdjasApprovalCriteriaById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasApprovalCriteriaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasApprovalCriteriaByIdResponses, GetApiIdjasApprovalCriteriaByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-criteria/{id}',
        ...options
    });
};

export const putApiIdjasApprovalCriteriaById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasApprovalCriteriaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasApprovalCriteriaByIdResponses, PutApiIdjasApprovalCriteriaByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-criteria/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasApprovalCriteriaFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalCriteriaFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalCriteriaFilterListResponses, PostApiIdjasApprovalCriteriaFilterListErrors, ThrowOnError>({
        url: '/api/idjas/approval-criteria/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdjasApprovalDelegation = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasApprovalDelegationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasApprovalDelegationResponses, GetApiIdjasApprovalDelegationErrors, ThrowOnError>({
        url: '/api/idjas/approval-delegation',
        ...options
    });
};

export const postApiIdjasApprovalDelegation = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalDelegationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalDelegationResponses, PostApiIdjasApprovalDelegationErrors, ThrowOnError>({
        url: '/api/idjas/approval-delegation',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasApprovalDelegationById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasApprovalDelegationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasApprovalDelegationByIdResponses, DeleteApiIdjasApprovalDelegationByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-delegation/{id}',
        ...options
    });
};

export const getApiIdjasApprovalDelegationById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasApprovalDelegationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasApprovalDelegationByIdResponses, GetApiIdjasApprovalDelegationByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-delegation/{id}',
        ...options
    });
};

export const putApiIdjasApprovalDelegationById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasApprovalDelegationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasApprovalDelegationByIdResponses, PutApiIdjasApprovalDelegationByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-delegation/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasApprovalDelegationFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalDelegationFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalDelegationFilterListResponses, PostApiIdjasApprovalDelegationFilterListErrors, ThrowOnError>({
        url: '/api/idjas/approval-delegation/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdjasApprovalStage = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasApprovalStageData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasApprovalStageResponses, GetApiIdjasApprovalStageErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage',
        ...options
    });
};

export const postApiIdjasApprovalStage = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalStageData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalStageResponses, PostApiIdjasApprovalStageErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasApprovalStageById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasApprovalStageByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasApprovalStageByIdResponses, DeleteApiIdjasApprovalStageByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage/{id}',
        ...options
    });
};

export const getApiIdjasApprovalStageById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasApprovalStageByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasApprovalStageByIdResponses, GetApiIdjasApprovalStageByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage/{id}',
        ...options
    });
};

export const putApiIdjasApprovalStageById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasApprovalStageByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasApprovalStageByIdResponses, PutApiIdjasApprovalStageByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasApprovalStageFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalStageFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalStageFilterListResponses, PostApiIdjasApprovalStageFilterListErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasApprovalStageSubmitForApproval = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalStageSubmitForApprovalData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalStageSubmitForApprovalResponses, PostApiIdjasApprovalStageSubmitForApprovalErrors, ThrowOnError>({
        url: '/api/idjas/approval-stage/submit-for-approval',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdjasApprovalTemplate = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasApprovalTemplateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasApprovalTemplateResponses, GetApiIdjasApprovalTemplateErrors, ThrowOnError>({
        url: '/api/idjas/approval-template',
        ...options
    });
};

export const postApiIdjasApprovalTemplate = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalTemplateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalTemplateResponses, PostApiIdjasApprovalTemplateErrors, ThrowOnError>({
        url: '/api/idjas/approval-template',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasApprovalTemplateById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasApprovalTemplateByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasApprovalTemplateByIdResponses, DeleteApiIdjasApprovalTemplateByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-template/{id}',
        ...options
    });
};

export const getApiIdjasApprovalTemplateById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasApprovalTemplateByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasApprovalTemplateByIdResponses, GetApiIdjasApprovalTemplateByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-template/{id}',
        ...options
    });
};

export const putApiIdjasApprovalTemplateById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasApprovalTemplateByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasApprovalTemplateByIdResponses, PutApiIdjasApprovalTemplateByIdErrors, ThrowOnError>({
        url: '/api/idjas/approval-template/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasApprovalTemplateFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasApprovalTemplateFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasApprovalTemplateFilterListResponses, PostApiIdjasApprovalTemplateFilterListErrors, ThrowOnError>({
        url: '/api/idjas/approval-template/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAppToAppData = <ThrowOnError extends boolean = false>(options?: Options<GetApiAppToAppDataData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAppToAppDataResponses, unknown, ThrowOnError>({
        url: '/api/app-to-app/data',
        ...options
    });
};

export const postApiAppToAppData = <ThrowOnError extends boolean = false>(options?: Options<PostApiAppToAppDataData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAppToAppDataResponses, unknown, ThrowOnError>({
        url: '/api/app-to-app/data',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAppToAppCustom = <ThrowOnError extends boolean = false>(options?: Options<GetApiAppToAppCustomData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAppToAppCustomResponses, unknown, ThrowOnError>({
        url: '/api/app-to-app/custom',
        ...options
    });
};

export const getApiAttachmentDownloadById = <ThrowOnError extends boolean = false>(options: Options<GetApiAttachmentDownloadByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiAttachmentDownloadByIdErrors, ThrowOnError>({
        url: '/api/attachment/download/{id}',
        ...options
    });
};

export const getApiAttachmentStreamById = <ThrowOnError extends boolean = false>(options: Options<GetApiAttachmentStreamByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiAttachmentStreamByIdErrors, ThrowOnError>({
        url: '/api/attachment/stream/{id}',
        ...options
    });
};

export const postApiAttachmentUpload = <ThrowOnError extends boolean = false>(options?: Options<PostApiAttachmentUploadData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAttachmentUploadResponses, PostApiAttachmentUploadErrors, ThrowOnError>({
        url: '/api/attachment/upload',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAttachmentByReference = <ThrowOnError extends boolean = false>(options?: Options<GetApiAttachmentByReferenceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAttachmentByReferenceResponses, GetApiAttachmentByReferenceErrors, ThrowOnError>({
        url: '/api/attachment/by-reference',
        ...options
    });
};

export const deleteApiAttachmentById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiAttachmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiAttachmentByIdResponses, DeleteApiAttachmentByIdErrors, ThrowOnError>({
        url: '/api/attachment/{id}',
        ...options
    });
};

export const postApiAttachmentBulkDownload = <ThrowOnError extends boolean = false>(options?: Options<PostApiAttachmentBulkDownloadData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, PostApiAttachmentBulkDownloadErrors, ThrowOnError>({
        url: '/api/attachment/bulk-download',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAttachmentUploadForm = <ThrowOnError extends boolean = false>(options?: Options<PostApiAttachmentUploadFormData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAttachmentUploadFormResponses, PostApiAttachmentUploadFormErrors, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/attachment/upload-form',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

export const getApiDashboardStatistics = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardStatisticsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardStatisticsResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/statistics',
        ...options
    });
};

export const getApiDashboardPendingApprovals = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardPendingApprovalsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardPendingApprovalsResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/pending-approvals',
        ...options
    });
};

export const getApiDashboardJettyStatus = <ThrowOnError extends boolean = false>(options?: Options<GetApiDashboardJettyStatusData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDashboardJettyStatusResponses, unknown, ThrowOnError>({
        url: '/api/dashboard/jetty-status',
        ...options
    });
};

export const getApiDebugCurrentUser = <ThrowOnError extends boolean = false>(options?: Options<GetApiDebugCurrentUserData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDebugCurrentUserResponses, unknown, ThrowOnError>({
        url: '/api/debug/current-user',
        ...options
    });
};

export const postApiDebugClearUserCache = <ThrowOnError extends boolean = false>(options?: Options<PostApiDebugClearUserCacheData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDebugClearUserCacheResponses, unknown, ThrowOnError>({
        url: '/api/debug/clear-user-cache',
        ...options
    });
};

export const postApiDebugUpdateUserName = <ThrowOnError extends boolean = false>(options?: Options<PostApiDebugUpdateUserNameData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDebugUpdateUserNameResponses, unknown, ThrowOnError>({
        url: '/api/debug/update-user-name',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiDebugForceUserSync = <ThrowOnError extends boolean = false>(options?: Options<PostApiDebugForceUserSyncData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDebugForceUserSyncResponses, unknown, ThrowOnError>({
        url: '/api/debug/force-user-sync',
        ...options
    });
};

export const postApiIdjasDocumentTemplatesUpload = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasDocumentTemplatesUploadData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasDocumentTemplatesUploadResponses, PostApiIdjasDocumentTemplatesUploadErrors, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/idjas/document/templates/upload',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

export const postApiIdjasDocumentGenerateApplication = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasDocumentGenerateApplicationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, PostApiIdjasDocumentGenerateApplicationErrors, ThrowOnError>({
        url: '/api/idjas/document/generate-application',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdjasDocumentTemplates = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasDocumentTemplatesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasDocumentTemplatesResponses, GetApiIdjasDocumentTemplatesErrors, ThrowOnError>({
        url: '/api/idjas/document/templates',
        ...options
    });
};

export const getApiIdjasDocumentTemplatesByType = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasDocumentTemplatesByTypeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasDocumentTemplatesByTypeResponses, GetApiIdjasDocumentTemplatesByTypeErrors, ThrowOnError>({
        url: '/api/idjas/document/templates-by-type',
        ...options
    });
};

export const getApiIdjasDocumentByIdTemplateById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasDocumentByIdTemplateByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasDocumentByIdTemplateByIdResponses, GetApiIdjasDocumentByIdTemplateByIdErrors, ThrowOnError>({
        url: '/api/idjas/document/{id}/template-by-id',
        ...options
    });
};

export const postApiIdjasDocumentTemplate = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasDocumentTemplateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasDocumentTemplateResponses, PostApiIdjasDocumentTemplateErrors, ThrowOnError>({
        url: '/api/idjas/document/template',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasDocumentUploadTemplate = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasDocumentUploadTemplateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasDocumentUploadTemplateResponses, PostApiIdjasDocumentUploadTemplateErrors, ThrowOnError>({
        url: '/api/idjas/document/upload-template',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasDocumentByIdTemplate = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasDocumentByIdTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasDocumentByIdTemplateResponses, DeleteApiIdjasDocumentByIdTemplateErrors, ThrowOnError>({
        url: '/api/idjas/document/{id}/template',
        ...options
    });
};

export const putApiIdjasDocumentByIdTemplate = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasDocumentByIdTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasDocumentByIdTemplateResponses, PutApiIdjasDocumentByIdTemplateErrors, ThrowOnError>({
        url: '/api/idjas/document/{id}/template',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasDocumentFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasDocumentFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasDocumentFilterListResponses, PostApiIdjasDocumentFilterListErrors, ThrowOnError>({
        url: '/api/idjas/document/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasDocumentConvertDocxToPdf = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasDocumentConvertDocxToPdfData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasDocumentConvertDocxToPdfResponses, PostApiIdjasDocumentConvertDocxToPdfErrors, ThrowOnError>({
        url: '/api/idjas/document/convert-docx-to-pdf',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountDynamicClaimsRefresh = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, ThrowOnError>({
        url: '/api/account/dynamic-claims/refresh',
        ...options
    });
};

export const postApiEkbVesselHeader = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselHeaderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselHeaderResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/vessel-header',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbAgent = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbAgentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbAgentResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/agent',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/tenant',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/jetty',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/cargo',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/business-partner',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/destination-port',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorResponses, unknown, ThrowOnError>({
        url: '/api/Ekb/surveyor',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiExternalVesselSearchVessels = <ThrowOnError extends boolean = false>(options?: Options<PostApiExternalVesselSearchVesselsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiExternalVesselSearchVesselsResponses, unknown, ThrowOnError>({
        url: '/api/ExternalVessel/search-vessels',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdentityServerUserQuery = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdentityServerUserQueryData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdentityServerUserQueryResponses, unknown, ThrowOnError>({
        url: '/api/IdentityServer/user-query',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiIdjasJettyRequestGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasJettyRequestGenerateNextDocNumData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasJettyRequestGenerateNextDocNumResponses, PostApiIdjasJettyRequestGenerateNextDocNumErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request/generate-next-doc-num',
        ...options
    });
};

export const getApiIdjasJettyRequest = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasJettyRequestData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasJettyRequestResponses, GetApiIdjasJettyRequestErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request',
        ...options
    });
};

export const postApiIdjasJettyRequest = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasJettyRequestData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasJettyRequestResponses, PostApiIdjasJettyRequestErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasJettyRequestById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasJettyRequestByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasJettyRequestByIdResponses, DeleteApiIdjasJettyRequestByIdErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request/{id}',
        ...options
    });
};

export const getApiIdjasJettyRequestById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasJettyRequestByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasJettyRequestByIdResponses, GetApiIdjasJettyRequestByIdErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request/{id}',
        ...options
    });
};

export const putApiIdjasJettyRequestById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasJettyRequestByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasJettyRequestByIdResponses, PutApiIdjasJettyRequestByIdErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasJettyRequestFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasJettyRequestFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasJettyRequestFilterListResponses, PostApiIdjasJettyRequestFilterListErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiIdjasJettyRequestItem = <ThrowOnError extends boolean = false>(options?: Options<GetApiIdjasJettyRequestItemData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiIdjasJettyRequestItemResponses, GetApiIdjasJettyRequestItemErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request-item',
        ...options
    });
};

export const postApiIdjasJettyRequestItem = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasJettyRequestItemData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasJettyRequestItemResponses, PostApiIdjasJettyRequestItemErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request-item',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiIdjasJettyRequestItemById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiIdjasJettyRequestItemByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiIdjasJettyRequestItemByIdResponses, DeleteApiIdjasJettyRequestItemByIdErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request-item/{id}',
        ...options
    });
};

export const getApiIdjasJettyRequestItemById = <ThrowOnError extends boolean = false>(options: Options<GetApiIdjasJettyRequestItemByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiIdjasJettyRequestItemByIdResponses, GetApiIdjasJettyRequestItemByIdErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request-item/{id}',
        ...options
    });
};

export const putApiIdjasJettyRequestItemById = <ThrowOnError extends boolean = false>(options: Options<PutApiIdjasJettyRequestItemByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiIdjasJettyRequestItemByIdResponses, PutApiIdjasJettyRequestItemByIdErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request-item/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiIdjasJettyRequestItemFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiIdjasJettyRequestItemFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiIdjasJettyRequestItemFilterListResponses, PostApiIdjasJettyRequestItemFilterListErrors, ThrowOnError>({
        url: '/api/idjas/jetty-request-item/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountLoginResponses, PostApiAccountLoginErrors, ThrowOnError>({
        url: '/api/account/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, ThrowOnError>({
        url: '/api/account/logout',
        ...options
    });
};

export const postApiAccountCheckPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, ThrowOnError>({
        url: '/api/account/check-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options
    });
};

export const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<PutApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountMyProfileChangePassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, ThrowOnError>({
        url: '/api/account/my-profile/change-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const getApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const putApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options
    });
};

export const postApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const putApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiTokenAccess = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenAccessData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenAccessResponses, unknown, ThrowOnError>({
        url: '/api/token/access',
        ...options
    });
};

export const getApiTokenRefresh = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenRefreshData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenRefreshResponses, unknown, ThrowOnError>({
        url: '/api/token/refresh',
        ...options
    });
};

export const getApiTokenStatus = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenStatusData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenStatusResponses, unknown, ThrowOnError>({
        url: '/api/token/status',
        ...options
    });
};

export const getApiTokenValid = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenValidData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenValidResponses, unknown, ThrowOnError>({
        url: '/api/token/valid',
        ...options
    });
};

export const getApiTokenExpiration = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenExpirationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenExpirationResponses, unknown, ThrowOnError>({
        url: '/api/token/expiration',
        ...options
    });
};

export const getApiTokenDebug = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenDebugData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenDebugResponses, unknown, ThrowOnError>({
        url: '/api/token/debug',
        ...options
    });
};

export const postApiTokenValidationValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiTokenValidationValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTokenValidationValidateResponses, unknown, ThrowOnError>({
        url: '/api/token-validation/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiTokenValidationIsValid = <ThrowOnError extends boolean = false>(options?: Options<PostApiTokenValidationIsValidData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTokenValidationIsValidResponses, unknown, ThrowOnError>({
        url: '/api/token-validation/is-valid',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTokenValidationProtected = <ThrowOnError extends boolean = false>(options?: Options<GetApiTokenValidationProtectedData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTokenValidationProtectedResponses, unknown, ThrowOnError>({
        url: '/api/token-validation/protected',
        ...options
    });
};