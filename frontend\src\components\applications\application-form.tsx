import type { FilterRequestDto, VesselHeaderDto } from '@/client/types.gen';
import { FormField, FormSection } from '@/components/ui/FormField';
import { Input } from '@/components/ui/input';
import { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDebounce } from '@/lib/hooks/useDebounce';
import { useJettyDataWithFilter } from '@/lib/hooks/useJettyDataWithFilter';
import React from 'react';
import type { ApplicationFormProps } from './types';
import VesselTable from './vessel-table';

// JettySelect component using MultiSelect in single mode
interface JettySelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const JettySelect: React.FC<JettySelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select jetty...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const [jettyOptions, setJettyOptions] = React.useState<MultiSelectOption[]>([]);

  // Debounce search to avoid too many API calls
  const debouncedSearchValue = useDebounce(searchValue, 300);

  // Fetch jetty data with filter
  const { mutate: fetchJettyData, isPending: isLoading } = useJettyDataWithFilter();

  // Fetch initial jetty data and when search changes
  React.useEffect(() => {
    const filterRequest: FilterRequestDto = {
      maxResultCount: 50,
      skipCount: 0,
      filterGroup: debouncedSearchValue ? {
        operator: 'Or',
        conditions: [
          {
            fieldName: 'name',
            operator: 'Contains',
            value: debouncedSearchValue
          },
          {
            fieldName: 'alias',
            operator: 'Contains',
            value: debouncedSearchValue
          },
          {
            fieldName: 'port',
            operator: 'Contains',
            value: debouncedSearchValue
          }
        ]
      } : undefined
    };

    fetchJettyData(filterRequest, {
      onSuccess: (data) => {
        const options: MultiSelectOption[] = data.map(jetty => ({
          value: jetty.id || '',
          label: jetty.name || jetty.alias || 'Unknown Jetty',
          description: jetty.port ? `Port: ${jetty.port}` : undefined,
          data: jetty
        }));
        setJettyOptions(options);
      }
    });
  }, [debouncedSearchValue, fetchJettyData]);

  // Fetch specific jetty when value is set but not in current options
  React.useEffect(() => {
    if (value && !jettyOptions.find(option => option.value === value)) {
      console.log('Fetching specific jetty for value:', value);
      const filterRequest: FilterRequestDto = {
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [
            {
              fieldName: 'id',
              operator: 'Equals',
              value: value
            }
          ]
        }
      };

      fetchJettyData(filterRequest, {
        onSuccess: (data) => {
          if (data.length > 0) {
            const newOption: MultiSelectOption = {
              value: data[0].id || '',
              label: data[0].name || data[0].alias || 'Unknown Jetty',
              description: data[0].port ? `Port: ${data[0].port}` : undefined,
              data: data[0]
            };

            // Add the new option to existing options if not already present
            setJettyOptions(prev => {
              const exists = prev.find(option => option.value === newOption.value);
              if (!exists) {
                console.log('Adding specific jetty option:', newOption);
                return [newOption, ...prev];
              }
              return prev;
            });
          }
        }
      });
    }
  }, [value, jettyOptions, fetchJettyData]);

  const handleChange = (values: string[]) => {
    // For single select, take the first value
    const newValue = values[0] || '';
    console.log('JettySelect handleChange called with:', values, 'setting value to:', newValue);
    onValueChange(newValue);
  };

  // Debug: Log when value prop changes
  React.useEffect(() => {
    console.log('JettySelect value prop changed to:', value);
  }, [value]);

  return (
    <MultiSelect
      options={jettyOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      isLoading={isLoading}
      loadingText="Loading jetties..."
      emptyText="No jetties found"
      showDescription={true}
    />
  );
};

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  docNum,
  vesselType,
  vessel,
  voyage,
  jetty,
  arrivalDate,
  departureDate,
  asideDate,
  castOfDate,
  postDate,
  portOrigin,
  destinationPort,
  barge,
  onDocNumChange,
  onVesselTypeChange,
  onVesselChange,
  onVoyageChange,
  onJettyChange,
  onArrivalDateChange,
  onDepartureDateChange,
  onAsideDateChange,
  onCastOfDateChange,
  onPostDateChange,
  onPortOriginChange,
  onDestinationPortChange,
  onBargeChange,
  errors = {},
  register,
  title = 'Create Application',
}) => {
  // Enhanced vessel change handler that also updates jetty
  const handleVesselChange = (selectedVessel: VesselHeaderDto | null) => {
    // console.log('handleVesselChange called with:', selectedVessel);
    onVesselChange(selectedVessel);

    // Auto-update jetty when vessel is selected
    if (selectedVessel?.jetty?.id) {
      // console.log('Setting jetty to:', selectedVessel.jetty.id);
      onJettyChange(selectedVessel.jetty.id);
    } else {
      // console.log('No jetty found in vessel data or vessel is null');
      // Clear jetty if vessel has no jetty or vessel is cleared
      onJettyChange('');
    }
  };

  return (
    <>
      <div className="mb-6">
        <h2 className="text-lg font-bold text-gray-800 dark:text-white">{title}</h2>
        <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6 mb-2">
        <FormSection>
          <FormField label="DocNum" labelWidth='100px'>
            {register ? (
              <Input id="docNum" {...register('docNum')} />
            ) : (
              <Input id="docNum" value={docNum} onChange={(e) => onDocNumChange(e.target.value)} />
            )}
            {errors.docNum && (
              <span className="text-red-500 text-xs">{errors.docNum.message as string}</span>
            )}
          </FormField>
          <FormField label="Vessel Type" labelWidth='100px'>
            <Select value={vesselType} onValueChange={onVesselTypeChange}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select Vessel Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Import">Import</SelectItem>
                <SelectItem value="Export">Export</SelectItem>
                <SelectItem value="LocalIn">Local In</SelectItem>
                <SelectItem value="LocalOut">Local Out</SelectItem>
              </SelectContent>
            </Select>
            {errors.vesselType && (
              <span className="text-red-500 text-xs">{errors.vesselType.message as string}</span>
            )}
          </FormField>
          <FormField label="Vessel" labelWidth='100px'>
            <VesselTable
              vesselType={vesselType}
              selectedVessel={vessel}
              onVesselSelect={handleVesselChange}
            />
            {errors.vesselName && (
              <span className="text-red-500 text-xs">{errors.vesselName.message as string}</span>
            )}
          </FormField>
          <FormField label="Voyage" labelWidth='100px'>
            {register ? (
              <Input id="voyage" {...register('voyage')} />
            ) : (
              <Input id="voyage" value={voyage} onChange={(e) => onVoyageChange(e.target.value)} />
            )}
            {errors.voyage && (
              <span className="text-red-500 text-xs">{errors.voyage.message as string}</span>
            )}
          </FormField>
          <FormField label="Jetty" labelWidth='100px'>
            <JettySelect
              value={jetty}
              onValueChange={onJettyChange}
              placeholder="Select Jetty"
            />
            {errors.jetty && (
              <span className="text-red-500 text-xs">{errors.jetty.message as string}</span>
            )}
          </FormField>
        </FormSection>

        <FormSection>
          <FormField label="Arrival Date" labelWidth='100px'>
            {register ? (
              <Input id="arrivalDate" type="datetime-local" {...register('arrivalDate')} />
            ) : (
              <Input id="arrivalDate" type="datetime-local" value={arrivalDate} onChange={e => onArrivalDateChange(e.target.value)} />
            )}
            {errors.arrivalDate && (
              <span className="text-red-500 text-xs">{errors.arrivalDate.message as string}</span>
            )}
          </FormField>
          <FormField label="Departure Date" labelWidth='100px'>
            {register ? (
              <Input id="departureDate" type="datetime-local" {...register('departureDate')} />
            ) : (
              <Input id="departureDate" type="datetime-local" value={departureDate} onChange={e => onDepartureDateChange(e.target.value)} />
            )}
            {errors.departureDate && (
              <span className="text-red-500 text-xs">{errors.departureDate.message as string}</span>
            )}
          </FormField>
          <FormField label="A/Side Date" labelWidth='100px'>
            <Input id="asideDate" type="datetime-local" value={asideDate} onChange={e => onAsideDateChange(e.target.value)} />
          </FormField>
          <FormField label="Cast Of Date" labelWidth='100px'>
            <Input id="castOfDate" type="datetime-local" value={castOfDate} onChange={e => onCastOfDateChange(e.target.value)} />
          </FormField>
          <FormField label="Posting Date" labelWidth='100px'>
            <Input id="postingDate" type="date" value={postDate} onChange={e => onPostDateChange(e.target.value)} />
          </FormField>
        </FormSection>

        <FormSection>
          <FormField label="Port Origin" labelWidth='100px'>
            {register ? (
              <Input id="portOrigin" {...register('portOrigin')} />
            ) : (
              <Input id="portOrigin" value={portOrigin} onChange={(e) => onPortOriginChange(e.target.value)} />
            )}
            {errors.portOrigin && (
              <span className="text-red-500 text-xs">{errors.portOrigin.message as string}</span>
            )}
          </FormField>
          <FormField label="Destination Port" labelWidth='100px'>
            {register ? (
              <Input id="destinationPort" {...register('destinationPort')} />
            ) : (
              <Input id="destinationPort" value={destinationPort} onChange={(e) => onDestinationPortChange(e.target.value)} />
            )}
            {errors.destinationPort && (
              <span className="text-red-500 text-xs">{errors.destinationPort.message as string}</span>
            )}
          </FormField>
          <FormField label="Barge" labelWidth='100px'>
            {register ? (
              <Input id="barge" {...register('barge')} />
            ) : (
              <Input id="barge" value={barge} onChange={(e) => onBargeChange(e.target.value)} />
            )}
            {errors.barge && (
              <span className="text-red-500 text-xs">{errors.barge.message as string}</span>
            )}
          </FormField>
        </FormSection>
      </div>
    </>
  );
};

export default ApplicationForm;