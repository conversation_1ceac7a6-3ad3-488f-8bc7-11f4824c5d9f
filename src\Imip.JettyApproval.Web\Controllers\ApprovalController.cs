using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.JettyApproval.Approvals;
using Imip.JettyApproval.Approvals.ApprovalStages;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

/// <summary>
/// Controller for approval operations
/// </summary>
[ApiController]
[Route("api/idjas/approval")]
public class ApprovalController : AbpControllerBase
{
    private readonly IApprovalStageAppService _approvalStageAppService;
    private readonly ILogger<ApprovalController> _logger;

    public ApprovalController(
        IApprovalStageAppService approvalStageAppService,
        ILogger<ApprovalController> logger)
    {
        _approvalStageAppService = approvalStageAppService;
        _logger = logger;
    }

    /// <summary>
    /// Submits a document for approval
    /// </summary>
    [HttpPost]
    [Route("submit")]
    public async Task<IActionResult> SubmitForApprovalAsync([FromBody] SubmitApprovalDto input)
    {
        try
        {
            var approvalStages = await _approvalStageAppService.SubmitForApprovalAsync(input);

            return Ok(new
            {
                success = true,
                message = $"Document submitted for approval. Created {approvalStages.Count} approval stages.",
                approvalStages = approvalStages
            });
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "User friendly error during approval submission: {Message}", ex.Message);
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting document for approval");
            return StatusCode(500, new { error = "An error occurred while submitting for approval" });
        }
    }

    /// <summary>
    /// Approves an approval stage
    /// </summary>
    [HttpPost]
    [Route("approve/{id}")]
    public async Task<IActionResult> ApproveAsync(Guid id, [FromBody] ApprovalActionDto input)
    {
        try
        {
            var approvalStage = await _approvalStageAppService.GetAsync(id);

            var updateDto = new CreateUpdateApprovalStageDto
            {
                Id = approvalStage.Id,
                ApprovalTemplateId = approvalStage.ApprovalTemplateId,
                ApproverId = approvalStage.ApproverId,
                ActionDate = DateTime.UtcNow,
                DocumentId = approvalStage.DocumentId,
                RequesterId = approvalStage.RequesterId,
                RequestDate = approvalStage.RequestDate,
                Status = ApprovalStatus.Approved,
                Notes = input.Notes
            };

            var updatedStage = await _approvalStageAppService.UpdateAsync(id, updateDto);

            return Ok(new
            {
                success = true,
                message = "Approval stage approved successfully",
                approvalStage = updatedStage
            });
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "User friendly error during approval: {Message}", ex.Message);
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during approval");
            return StatusCode(500, new { error = "An unexpected error occurred", details = ex.Message });
        }
    }

    /// <summary>
    /// Rejects an approval stage
    /// </summary>
    [HttpPost]
    [Route("reject/{id}")]
    public async Task<IActionResult> RejectAsync(Guid id, [FromBody] ApprovalActionDto input)
    {
        try
        {
            var approvalStage = await _approvalStageAppService.GetAsync(id);

            var updateDto = new CreateUpdateApprovalStageDto
            {
                Id = approvalStage.Id,
                ApprovalTemplateId = approvalStage.ApprovalTemplateId,
                ApproverId = approvalStage.ApproverId,
                ActionDate = DateTime.UtcNow,
                DocumentId = approvalStage.DocumentId,
                RequesterId = approvalStage.RequesterId,
                RequestDate = approvalStage.RequestDate,
                Status = ApprovalStatus.Rejected,
                Notes = input.Notes
            };

            var updatedStage = await _approvalStageAppService.UpdateAsync(id, updateDto);

            return Ok(new
            {
                success = true,
                message = "Approval stage rejected successfully",
                approvalStage = updatedStage
            });
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "User friendly error during rejection: {Message}", ex.Message);
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during rejection");
            return StatusCode(500, new { error = "An unexpected error occurred", details = ex.Message });
        }
    }
}
