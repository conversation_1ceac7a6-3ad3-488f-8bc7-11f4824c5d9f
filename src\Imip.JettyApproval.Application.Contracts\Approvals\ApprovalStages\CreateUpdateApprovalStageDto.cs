using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// DTO for creating and updating ApprovalStage entity
/// </summary>
public class CreateUpdateApprovalStageDto
{
    /// <summary>
    /// ID of the approval stage (empty for new records, existing ID for updates)
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    [Required]
    public Guid ApprovalTemplateId { get; set; }

    /// <summary>
    /// ID of the approver for this stage
    /// </summary>
    [Required]
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Action date and time
    /// </summary>
    public DateTime? ActionDate { get; set; }

    /// <summary>
    /// Document ID for this stage
    /// </summary>
    [StringLength(100)]
    public string? DocumentId { get; set; }

    /// <summary>
    /// ID of the requester
    /// </summary>
    [StringLength(100)]
    public string? RequesterId { get; set; }

    /// <summary>
    /// Request date and time
    /// </summary>
    public DateTime? RequestDate { get; set; }

    /// <summary>
    /// Status of the approval stage
    /// </summary>
    public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;

    /// <summary>
    /// Additional notes for the stage
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }
}