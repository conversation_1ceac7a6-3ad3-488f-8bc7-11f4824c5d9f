using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// DTO for creating and updating JettyRequest entity
/// </summary>
public class CreateUpdateJettyRequestDto
{
    /// <summary>
    /// Document number for the jetty request
    /// </summary>
    [Required]
    public int DocNum { get; set; }

    /// <summary>
    /// Type of vessel
    /// </summary>
    [StringLength(100)]
    [Required]
    public string? VesselType { get; set; }

    /// <summary>
    /// Reference ID for the request
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Name of the vessel
    /// </summary>
    [Required]
    [StringLength(200)]
    public string VesselName { get; set; } = string.Empty;

    /// <summary>
    /// Voyage information
    /// </summary>
    [StringLength(100)]
    public string? Voyage { get; set; }

    /// <summary>
    /// Jetty name/location
    /// </summary>
    [StringLength(200)]
    public string? Jetty { get; set; }

    /// <summary>
    /// Arrival date and time
    /// </summary>
    public DateTime? ArrivalDate { get; set; }

    /// <summary>
    /// Departure date and time
    /// </summary>
    public DateTime? DepartureDate { get; set; }

    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }

    /// <summary>
    /// Post date and time
    /// </summary>
    public DateTime? PostDate { get; set; }

    /// <summary>
    /// Barge information
    /// </summary>
    [StringLength(200)]
    public string? Barge { get; set; }

    /// <summary>
    /// Port of origin
    /// </summary>
    [StringLength(200)]
    public string? PortOrigin { get; set; }

    /// <summary>
    /// Destination port
    /// </summary>
    [StringLength(200)]
    public string? DestinationPort { get; set; }

    /// <summary>
    /// Collection of jetty request items
    /// </summary>
    public List<CreateUpdateJettyRequestItemDto> Items { get; set; } = new List<CreateUpdateJettyRequestItemDto>();
}