using System;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Approvals;

public class ApprovalTemplateRepository : EfCoreRepository<JettyApprovalDbContext, ApprovalTemplate, Guid>, IApprovalTemplateRepository
{
    public ApprovalTemplateRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }
}