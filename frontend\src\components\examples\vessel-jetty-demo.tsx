'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import VesselTable from '@/components/applications/vessel-table'
import type { VesselHeaderDto } from '@/client/types.gen'

// JettySelect component using MultiSelect in single mode (same as in ApplicationForm)
import { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select'
import { useJettyDataWithFilter } from '@/lib/hooks/useJettyDataWithFilter'
import { useDebounce } from '@/lib/hooks/useDebounce'
import type { FilterRequestDto } from '@/client/types.gen'

interface JettySelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const JettySelect: React.FC<JettySelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select jetty...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const [jettyOptions, setJettyOptions] = React.useState<MultiSelectOption[]>([]);
  
  // Debounce search to avoid too many API calls
  const debouncedSearchValue = useDebounce(searchValue, 300);
  
  // Fetch jetty data with filter
  const { mutate: fetchJettyData, isPending: isLoading } = useJettyDataWithFilter();

  // Fetch jetty data when debounced search changes
  React.useEffect(() => {
    const filterRequest: FilterRequestDto = {
      maxResultCount: 50,
      skipCount: 0,
      filterGroup: debouncedSearchValue ? {
        operator: 'Or',
        conditions: [
          {
            fieldName: 'name',
            operator: 'Contains',
            value: debouncedSearchValue
          },
          {
            fieldName: 'alias',
            operator: 'Contains',
            value: debouncedSearchValue
          },
          {
            fieldName: 'port',
            operator: 'Contains',
            value: debouncedSearchValue
          }
        ]
      } : undefined
    };

    fetchJettyData(filterRequest, {
      onSuccess: (data) => {
        const options: MultiSelectOption[] = data.map(jetty => ({
          value: jetty.id || '',
          label: jetty.name || jetty.alias || 'Unknown Jetty',
          description: jetty.port ? `Port: ${jetty.port}` : undefined,
          data: jetty
        }));
        setJettyOptions(options);
      }
    });
  }, [debouncedSearchValue, fetchJettyData]);

  const handleChange = (values: string[]) => {
    // For single select, take the first value
    onValueChange(values[0] || '');
  };

  return (
    <MultiSelect
      options={jettyOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      isLoading={isLoading}
      loadingText="Loading jetties..."
      emptyText="No jetties found"
      showDescription={true}
    />
  );
};

export function VesselJettyDemo() {
  const [selectedVessel, setSelectedVessel] = React.useState<VesselHeaderDto | null>(null)
  const [jettyValue, setJettyValue] = React.useState<string>('')
  const [vesselType] = React.useState<string>('Import')

  // Enhanced vessel change handler that also updates jetty (same as in ApplicationForm)
  const handleVesselChange = (vessel: VesselHeaderDto | null) => {
    setSelectedVessel(vessel);
    
    // Auto-update jetty when vessel is selected
    if (vessel?.jetty?.id) {
      setJettyValue(vessel.jetty.id);
    } else {
      // Clear jetty if vessel has no jetty or vessel is cleared
      setJettyValue('');
    }
  };

  const handleJettyChange = (jettyId: string) => {
    setJettyValue(jettyId);
  };

  const clearAll = () => {
    setSelectedVessel(null);
    setJettyValue('');
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Vessel & Jetty Auto-Selection Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Vessel:</label>
              <VesselTable
                vesselType={vesselType}
                selectedVessel={selectedVessel}
                onVesselSelect={handleVesselChange}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Select Jetty:</label>
              <JettySelect
                value={jettyValue}
                onValueChange={handleJettyChange}
                placeholder="Search and select a jetty..."
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={clearAll} variant="outline">
              Clear All
            </Button>
          </div>

          {selectedVessel && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Selected Vessel Details:</h3>
              <div className="p-3 border rounded-lg space-y-2">
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">
                    {selectedVessel.vesselName} {selectedVessel.voyage}
                  </Badge>
                  <Badge variant="outline">
                    Type: {selectedVessel.vesselType}
                  </Badge>
                </div>
                {selectedVessel.jetty && (
                  <div className="text-sm">
                    <strong>Auto-selected Jetty:</strong> {selectedVessel.jetty.name || selectedVessel.jetty.id}
                    {selectedVessel.jetty.id && (
                      <Badge variant="default" className="ml-2">
                        ID: {selectedVessel.jetty.id}
                      </Badge>
                    )}
                  </div>
                )}
                {selectedVessel.vesselArrival && (
                  <div className="text-sm">
                    <strong>Arrival:</strong> {new Date(selectedVessel.vesselArrival).toLocaleDateString()}
                  </div>
                )}
                {selectedVessel.vesselDeparture && (
                  <div className="text-sm">
                    <strong>Departure:</strong> {new Date(selectedVessel.vesselDeparture).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          )}

          {jettyValue && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Current Jetty Selection:</h3>
              <div className="p-3 border rounded-lg">
                <Badge variant="default">
                  Jetty ID: {jettyValue}
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>How It Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p><strong>Auto-Selection Feature:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>✅ When you select a vessel, the jetty field automatically updates</li>
            <li>✅ Uses the vessel's associated jetty from the vessel data</li>
            <li>✅ If vessel has no jetty, the jetty field remains empty</li>
            <li>✅ You can still manually change the jetty after auto-selection</li>
            <li>✅ Clearing the vessel also clears the jetty</li>
          </ul>
          
          <p className="mt-4"><strong>Implementation Details:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Enhanced <code>handleVesselChange</code> function in ApplicationForm</li>
            <li>Checks for <code>selectedVessel?.jetty?.id</code> property</li>
            <li>Automatically calls <code>onJettyChange(vessel.jetty.id)</code></li>
            <li>Uses the existing MultiSelect component in single-select mode</li>
            <li>Supports filtering and search for jetty selection</li>
          </ul>

          <p className="mt-4"><strong>Benefits:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Reduces manual data entry</li>
            <li>Ensures consistency between vessel and jetty</li>
            <li>Improves user experience</li>
            <li>Maintains flexibility for manual overrides</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
