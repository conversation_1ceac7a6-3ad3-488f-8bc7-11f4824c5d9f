import{r as Pa,g as Aa,a as i,j as m,$ as Te,R as Ao,b as Ma}from"./vendor-BVmWhBab.js";var nt=Pa();const _a=Aa(nt);function eo(e,[t,n]){return Math.min(n,Math.max(t,e))}function R(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}function Ia(e,t){const n=i.createContext(t),o=s=>{const{children:a,...c}=s,l=i.useMemo(()=>c,Object.values(c));return m.jsx(n.Provider,{value:l,children:a})};o.displayName=e+"Provider";function r(s){const a=i.useContext(n);if(a)return a;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[o,r]}function se(e,t=[]){let n=[];function o(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];const u=d=>{const{scope:v,children:h,...w}=d,p=v?.[e]?.[l]||c,g=i.useMemo(()=>w,Object.values(w));return m.jsx(p.Provider,{value:g,children:h})};u.displayName=s+"Provider";function f(d,v){const h=v?.[e]?.[l]||c,w=i.useContext(h);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,f]}const r=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=c?.[e]||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return r.scopeName=e,[o,Ta(r,...t)]}function Ta(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const a=o.reduce((c,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...c,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function to(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Mt(...e){return t=>{let n=!1;const o=e.map(r=>{const s=to(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():to(e[r],null)}}}}function F(...e){return i.useCallback(Mt(...e),e)}function Ee(e){const t=Oa(e),n=i.forwardRef((o,r)=>{const{children:s,...a}=o,c=i.Children.toArray(s),l=c.find(Na);if(l){const u=l.props.children,f=c.map(d=>d===l?i.Children.count(u)>1?i.Children.only(null):i.isValidElement(u)?u.props.children:null:d);return m.jsx(t,{...a,ref:r,children:i.isValidElement(u)?i.cloneElement(u,void 0,f):null})}return m.jsx(t,{...a,ref:r,children:s})});return n.displayName=`${e}.Slot`,n}var Ad=Ee("Slot");function Oa(e){const t=i.forwardRef((n,o)=>{const{children:r,...s}=n;if(i.isValidElement(r)){const a=ka(r),c=Da(s,r.props);return r.type!==i.Fragment&&(c.ref=o?Mt(o,a):a),i.cloneElement(r,c)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Mo=Symbol("radix.slottable");function Md(e){const t=({children:n})=>m.jsx(m.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Mo,t}function Na(e){return i.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Mo}function Da(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...c)=>{const l=s(...c);return r(...c),l}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function ka(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Sn(e){const t=e+"CollectionProvider",[n,o]=se(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=p=>{const{scope:g,children:y}=p,x=Te.useRef(null),C=Te.useRef(new Map).current;return m.jsx(r,{scope:g,itemMap:C,collectionRef:x,children:y})};a.displayName=t;const c=e+"CollectionSlot",l=Ee(c),u=Te.forwardRef((p,g)=>{const{scope:y,children:x}=p,C=s(c,y),b=F(g,C.collectionRef);return m.jsx(l,{ref:b,children:x})});u.displayName=c;const f=e+"CollectionItemSlot",d="data-radix-collection-item",v=Ee(f),h=Te.forwardRef((p,g)=>{const{scope:y,children:x,...C}=p,b=Te.useRef(null),S=F(g,b),_=s(f,y);return Te.useEffect(()=>(_.itemMap.set(b,{ref:b,...C}),()=>void _.itemMap.delete(b))),m.jsx(v,{[d]:"",ref:S,children:x})});h.displayName=f;function w(p){const g=s(e+"CollectionConsumer",p);return Te.useCallback(()=>{const x=g.collectionRef.current;if(!x)return[];const C=Array.from(x.querySelectorAll(`[${d}]`));return Array.from(g.itemMap.values()).sort((_,E)=>C.indexOf(_.ref.current)-C.indexOf(E.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:a,Slot:u,ItemSlot:h},w,o]}var La=i.createContext(void 0);function _t(e){const t=i.useContext(La);return e||t||"ltr"}var ja=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],T=ja.reduce((e,t)=>{const n=Ee(`Primitive.${t}`),o=i.forwardRef((r,s)=>{const{asChild:a,...c}=r,l=a?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(l,{...c,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function _o(e,t){e&&nt.flushSync(()=>e.dispatchEvent(t))}function ce(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>t.current?.(...n),[])}function Fa(e,t=globalThis?.document){const n=ce(e);i.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var $a="DismissableLayer",dn="dismissableLayer.update",Ba="dismissableLayer.pointerDownOutside",Wa="dismissableLayer.focusOutside",no,Io=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ke=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...l}=e,u=i.useContext(Io),[f,d]=i.useState(null),v=f?.ownerDocument??globalThis?.document,[,h]=i.useState({}),w=F(t,E=>d(E)),p=Array.from(u.layers),[g]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=p.indexOf(g),x=f?p.indexOf(f):-1,C=u.layersWithOutsidePointerEventsDisabled.size>0,b=x>=y,S=Ha(E=>{const M=E.target,D=[...u.branches].some(N=>N.contains(M));!b||D||(r?.(E),a?.(E),E.defaultPrevented||c?.())},v),_=Ua(E=>{const M=E.target;[...u.branches].some(N=>N.contains(M))||(s?.(E),a?.(E),E.defaultPrevented||c?.())},v);return Fa(E=>{x===u.layers.size-1&&(o?.(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},v),i.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(no=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),oo(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=no)}},[f,v,n,u]),i.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),oo())},[f,u]),i.useEffect(()=>{const E=()=>h({});return document.addEventListener(dn,E),()=>document.removeEventListener(dn,E)},[]),m.jsx(T.div,{...l,ref:w,style:{pointerEvents:C?b?"auto":"none":void 0,...e.style},onFocusCapture:R(e.onFocusCapture,_.onFocusCapture),onBlurCapture:R(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:R(e.onPointerDownCapture,S.onPointerDownCapture)})});Ke.displayName=$a;var Va="DismissableLayerBranch",To=i.forwardRef((e,t)=>{const n=i.useContext(Io),o=i.useRef(null),r=F(t,o);return i.useEffect(()=>{const s=o.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),m.jsx(T.div,{...e,ref:r})});To.displayName=Va;function Ha(e,t=globalThis?.document){const n=ce(e),o=i.useRef(!1),r=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!o.current){let l=function(){Oo(Ba,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=l,t.addEventListener("click",r.current,{once:!0})):l()}else t.removeEventListener("click",r.current);o.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function Ua(e,t=globalThis?.document){const n=ce(e),o=i.useRef(!1);return i.useEffect(()=>{const r=s=>{s.target&&!o.current&&Oo(Wa,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function oo(){const e=new CustomEvent(dn);document.dispatchEvent(e)}function Oo(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?_o(r,s):r.dispatchEvent(s)}var _d=Ke,Id=To,Qt=0;function It(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ro()),document.body.insertAdjacentElement("beforeend",e[1]??ro()),Qt++,()=>{Qt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Qt--}},[])}function ro(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Jt="focusScope.autoFocusOnMount",en="focusScope.autoFocusOnUnmount",so={bubbles:!1,cancelable:!0},Ka="FocusScope",ot=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:s,...a}=e,[c,l]=i.useState(null),u=ce(r),f=ce(s),d=i.useRef(null),v=F(t,p=>l(p)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(o){let p=function(C){if(h.paused||!c)return;const b=C.target;c.contains(b)?d.current=b:be(d.current,{select:!0})},g=function(C){if(h.paused||!c)return;const b=C.relatedTarget;b!==null&&(c.contains(b)||be(d.current,{select:!0}))},y=function(C){if(document.activeElement===document.body)for(const S of C)S.removedNodes.length>0&&be(c)};document.addEventListener("focusin",p),document.addEventListener("focusout",g);const x=new MutationObserver(y);return c&&x.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",g),x.disconnect()}}},[o,c,h.paused]),i.useEffect(()=>{if(c){io.add(h);const p=document.activeElement;if(!c.contains(p)){const y=new CustomEvent(Jt,so);c.addEventListener(Jt,u),c.dispatchEvent(y),y.defaultPrevented||(Ga(Za(No(c)),{select:!0}),document.activeElement===p&&be(c))}return()=>{c.removeEventListener(Jt,u),setTimeout(()=>{const y=new CustomEvent(en,so);c.addEventListener(en,f),c.dispatchEvent(y),y.defaultPrevented||be(p??document.body,{select:!0}),c.removeEventListener(en,f),io.remove(h)},0)}}},[c,u,f,h]);const w=i.useCallback(p=>{if(!n&&!o||h.paused)return;const g=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,y=document.activeElement;if(g&&y){const x=p.currentTarget,[C,b]=za(x);C&&b?!p.shiftKey&&y===b?(p.preventDefault(),n&&be(C,{select:!0})):p.shiftKey&&y===C&&(p.preventDefault(),n&&be(b,{select:!0})):y===x&&p.preventDefault()}},[n,o,h.paused]);return m.jsx(T.div,{tabIndex:-1,...a,ref:v,onKeyDown:w})});ot.displayName=Ka;function Ga(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(be(o,{select:t}),document.activeElement!==n)return}function za(e){const t=No(e),n=ao(t,e),o=ao(t.reverse(),e);return[n,o]}function No(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ao(e,t){for(const n of e)if(!Ya(n,{upTo:t}))return n}function Ya(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Xa(e){return e instanceof HTMLInputElement&&"select"in e}function be(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Xa(e)&&t&&e.select()}}var io=qa();function qa(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=co(e,t),e.unshift(t)},remove(t){e=co(e,t),e[0]?.resume()}}}function co(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function Za(e){return e.filter(t=>t.tagName!=="A")}var z=globalThis?.document?i.useLayoutEffect:()=>{},Qa=Ao[" useId ".trim().toString()]||(()=>{}),Ja=0;function oe(e){const[t,n]=i.useState(Qa());return z(()=>{n(o=>o??String(Ja++))},[e]),e||(t?`radix-${t}`:"")}const ei=["top","right","bottom","left"],Re=Math.min,Q=Math.max,yt=Math.round,dt=Math.floor,me=e=>({x:e,y:e}),ti={left:"right",right:"left",bottom:"top",top:"bottom"},ni={start:"end",end:"start"};function fn(e,t,n){return Q(e,Re(t,n))}function xe(e,t){return typeof e=="function"?e(t):e}function ye(e){return e.split("-")[0]}function Ge(e){return e.split("-")[1]}function En(e){return e==="x"?"y":"x"}function Rn(e){return e==="y"?"height":"width"}function pe(e){return["top","bottom"].includes(ye(e))?"y":"x"}function Pn(e){return En(pe(e))}function oi(e,t,n){n===void 0&&(n=!1);const o=Ge(e),r=Pn(e),s=Rn(r);let a=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(a=Ct(a)),[a,Ct(a)]}function ri(e){const t=Ct(e);return[pn(e),t,pn(t)]}function pn(e){return e.replace(/start|end/g,t=>ni[t])}function si(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:a;default:return[]}}function ai(e,t,n,o){const r=Ge(e);let s=si(ye(e),n==="start",o);return r&&(s=s.map(a=>a+"-"+r),t&&(s=s.concat(s.map(pn)))),s}function Ct(e){return e.replace(/left|right|bottom|top/g,t=>ti[t])}function ii(e){return{top:0,right:0,bottom:0,left:0,...e}}function Do(e){return typeof e!="number"?ii(e):{top:e,right:e,bottom:e,left:e}}function bt(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function lo(e,t,n){let{reference:o,floating:r}=e;const s=pe(t),a=Pn(t),c=Rn(a),l=ye(t),u=s==="y",f=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,v=o[c]/2-r[c]/2;let h;switch(l){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:d};break;case"left":h={x:o.x-r.width,y:d};break;default:h={x:o.x,y:o.y}}switch(Ge(t)){case"start":h[a]-=v*(n&&u?-1:1);break;case"end":h[a]+=v*(n&&u?-1:1);break}return h}const ci=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),l=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:d}=lo(u,o,l),v=o,h={},w=0;for(let p=0;p<c.length;p++){const{name:g,fn:y}=c[p],{x,y:C,data:b,reset:S}=await y({x:f,y:d,initialPlacement:o,placement:v,strategy:r,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});f=x??f,d=C??d,h={...h,[g]:{...h[g],...b}},S&&w<=50&&(w++,typeof S=="object"&&(S.placement&&(v=S.placement),S.rects&&(u=S.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:r}):S.rects),{x:f,y:d}=lo(u,v,l)),p=-1)}return{x:f,y:d,placement:v,strategy:r,middlewareData:h}};async function Qe(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:a,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:v=!1,padding:h=0}=xe(t,e),w=Do(h),g=c[v?d==="floating"?"reference":"floating":d],y=bt(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:f,strategy:l})),x=d==="floating"?{x:o,y:r,width:a.floating.width,height:a.floating.height}:a.reference,C=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),b=await(s.isElement==null?void 0:s.isElement(C))?await(s.getScale==null?void 0:s.getScale(C))||{x:1,y:1}:{x:1,y:1},S=bt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:C,strategy:l}):x);return{top:(y.top-S.top+w.top)/b.y,bottom:(S.bottom-y.bottom+w.bottom)/b.y,left:(y.left-S.left+w.left)/b.x,right:(S.right-y.right+w.right)/b.x}}const li=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:a,elements:c,middlewareData:l}=t,{element:u,padding:f=0}=xe(e,t)||{};if(u==null)return{};const d=Do(f),v={x:n,y:o},h=Pn(r),w=Rn(h),p=await a.getDimensions(u),g=h==="y",y=g?"top":"left",x=g?"bottom":"right",C=g?"clientHeight":"clientWidth",b=s.reference[w]+s.reference[h]-v[h]-s.floating[w],S=v[h]-s.reference[h],_=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let E=_?_[C]:0;(!E||!await(a.isElement==null?void 0:a.isElement(_)))&&(E=c.floating[C]||s.floating[w]);const M=b/2-S/2,D=E/2-p[w]/2-1,N=Re(d[y],D),$=Re(d[x],D),B=N,L=E-p[w]-$,k=E/2-p[w]/2+M,W=fn(B,k,L),O=!l.arrow&&Ge(r)!=null&&k!==W&&s.reference[w]/2-(k<B?N:$)-p[w]/2<0,j=O?k<B?k-B:k-L:0;return{[h]:v[h]+j,data:{[h]:W,centerOffset:k-W-j,...O&&{alignmentOffset:j}},reset:O}}}),ui=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:a,initialPlacement:c,platform:l,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:v,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:p=!0,...g}=xe(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const y=ye(r),x=pe(c),C=ye(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(u.floating)),S=v||(C||!p?[Ct(c)]:ri(c)),_=w!=="none";!v&&_&&S.push(...ai(c,p,w,b));const E=[c,...S],M=await Qe(t,g),D=[];let N=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&D.push(M[y]),d){const k=oi(r,a,b);D.push(M[k[0]],M[k[1]])}if(N=[...N,{placement:r,overflows:D}],!D.every(k=>k<=0)){var $,B;const k=((($=s.flip)==null?void 0:$.index)||0)+1,W=E[k];if(W&&(!(d==="alignment"?x!==pe(W):!1)||N.every(I=>I.overflows[0]>0&&pe(I.placement)===x)))return{data:{index:k,overflows:N},reset:{placement:W}};let O=(B=N.filter(j=>j.overflows[0]<=0).sort((j,I)=>j.overflows[1]-I.overflows[1])[0])==null?void 0:B.placement;if(!O)switch(h){case"bestFit":{var L;const j=(L=N.filter(I=>{if(_){const P=pe(I.placement);return P===x||P==="y"}return!0}).map(I=>[I.placement,I.overflows.filter(P=>P>0).reduce((P,K)=>P+K,0)]).sort((I,P)=>I[1]-P[1])[0])==null?void 0:L[0];j&&(O=j);break}case"initialPlacement":O=c;break}if(r!==O)return{reset:{placement:O}}}return{}}}};function uo(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function fo(e){return ei.some(t=>e[t]>=0)}const di=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=xe(e,t);switch(o){case"referenceHidden":{const s=await Qe(t,{...r,elementContext:"reference"}),a=uo(s,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:fo(a)}}}case"escaped":{const s=await Qe(t,{...r,altBoundary:!0}),a=uo(s,n.floating);return{data:{escapedOffsets:a,escaped:fo(a)}}}default:return{}}}}};async function fi(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),a=ye(n),c=Ge(n),l=pe(n)==="y",u=["left","top"].includes(a)?-1:1,f=s&&l?-1:1,d=xe(t,e);let{mainAxis:v,crossAxis:h,alignmentAxis:w}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof w=="number"&&(h=c==="end"?w*-1:w),l?{x:h*f,y:v*u}:{x:v*u,y:h*f}}const pi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:a,middlewareData:c}=t,l=await fi(t,e);return a===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:a}}}}},mi=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:c={fn:g=>{let{x:y,y:x}=g;return{x:y,y:x}}},...l}=xe(e,t),u={x:n,y:o},f=await Qe(t,l),d=pe(ye(r)),v=En(d);let h=u[v],w=u[d];if(s){const g=v==="y"?"top":"left",y=v==="y"?"bottom":"right",x=h+f[g],C=h-f[y];h=fn(x,h,C)}if(a){const g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",x=w+f[g],C=w-f[y];w=fn(x,w,C)}const p=c.fn({...t,[v]:h,[d]:w});return{...p,data:{x:p.x-n,y:p.y-o,enabled:{[v]:s,[d]:a}}}}}},vi=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:a}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=xe(e,t),f={x:n,y:o},d=pe(r),v=En(d);let h=f[v],w=f[d];const p=xe(c,t),g=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const C=v==="y"?"height":"width",b=s.reference[v]-s.floating[C]+g.mainAxis,S=s.reference[v]+s.reference[C]-g.mainAxis;h<b?h=b:h>S&&(h=S)}if(u){var y,x;const C=v==="y"?"width":"height",b=["top","left"].includes(ye(r)),S=s.reference[d]-s.floating[C]+(b&&((y=a.offset)==null?void 0:y[d])||0)+(b?0:g.crossAxis),_=s.reference[d]+s.reference[C]+(b?0:((x=a.offset)==null?void 0:x[d])||0)-(b?g.crossAxis:0);w<S?w=S:w>_&&(w=_)}return{[v]:h,[d]:w}}}},hi=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:a,elements:c}=t,{apply:l=()=>{},...u}=xe(e,t),f=await Qe(t,u),d=ye(r),v=Ge(r),h=pe(r)==="y",{width:w,height:p}=s.floating;let g,y;d==="top"||d==="bottom"?(g=d,y=v===(await(a.isRTL==null?void 0:a.isRTL(c.floating))?"start":"end")?"left":"right"):(y=d,g=v==="end"?"top":"bottom");const x=p-f.top-f.bottom,C=w-f.left-f.right,b=Re(p-f[g],x),S=Re(w-f[y],C),_=!t.middlewareData.shift;let E=b,M=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(M=C),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(E=x),_&&!v){const N=Q(f.left,0),$=Q(f.right,0),B=Q(f.top,0),L=Q(f.bottom,0);h?M=w-2*(N!==0||$!==0?N+$:Q(f.left,f.right)):E=p-2*(B!==0||L!==0?B+L:Q(f.top,f.bottom))}await l({...t,availableWidth:M,availableHeight:E});const D=await a.getDimensions(c.floating);return w!==D.width||p!==D.height?{reset:{rects:!0}}:{}}}};function Tt(){return typeof window<"u"}function ze(e){return ko(e)?(e.nodeName||"").toLowerCase():"#document"}function J(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function he(e){var t;return(t=(ko(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function ko(e){return Tt()?e instanceof Node||e instanceof J(e).Node:!1}function le(e){return Tt()?e instanceof Element||e instanceof J(e).Element:!1}function ve(e){return Tt()?e instanceof HTMLElement||e instanceof J(e).HTMLElement:!1}function po(e){return!Tt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof J(e).ShadowRoot}function rt(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ue(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function gi(e){return["table","td","th"].includes(ze(e))}function Ot(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function An(e){const t=Mn(),n=le(e)?ue(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function wi(e){let t=Pe(e);for(;ve(t)&&!He(t);){if(An(t))return t;if(Ot(t))return null;t=Pe(t)}return null}function Mn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function He(e){return["html","body","#document"].includes(ze(e))}function ue(e){return J(e).getComputedStyle(e)}function Nt(e){return le(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Pe(e){if(ze(e)==="html")return e;const t=e.assignedSlot||e.parentNode||po(e)&&e.host||he(e);return po(t)?t.host:t}function Lo(e){const t=Pe(e);return He(t)?e.ownerDocument?e.ownerDocument.body:e.body:ve(t)&&rt(t)?t:Lo(t)}function Je(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Lo(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),a=J(r);if(s){const c=mn(a);return t.concat(a,a.visualViewport||[],rt(r)?r:[],c&&n?Je(c):[])}return t.concat(r,Je(r,[],n))}function mn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function jo(e){const t=ue(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=ve(e),s=r?e.offsetWidth:n,a=r?e.offsetHeight:o,c=yt(n)!==s||yt(o)!==a;return c&&(n=s,o=a),{width:n,height:o,$:c}}function _n(e){return le(e)?e:e.contextElement}function We(e){const t=_n(e);if(!ve(t))return me(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=jo(t);let a=(s?yt(n.width):n.width)/o,c=(s?yt(n.height):n.height)/r;return(!a||!Number.isFinite(a))&&(a=1),(!c||!Number.isFinite(c))&&(c=1),{x:a,y:c}}const xi=me(0);function Fo(e){const t=J(e);return!Mn()||!t.visualViewport?xi:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function yi(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==J(e)?!1:t}function Oe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=_n(e);let a=me(1);t&&(o?le(o)&&(a=We(o)):a=We(e));const c=yi(s,n,o)?Fo(s):me(0);let l=(r.left+c.x)/a.x,u=(r.top+c.y)/a.y,f=r.width/a.x,d=r.height/a.y;if(s){const v=J(s),h=o&&le(o)?J(o):o;let w=v,p=mn(w);for(;p&&o&&h!==w;){const g=We(p),y=p.getBoundingClientRect(),x=ue(p),C=y.left+(p.clientLeft+parseFloat(x.paddingLeft))*g.x,b=y.top+(p.clientTop+parseFloat(x.paddingTop))*g.y;l*=g.x,u*=g.y,f*=g.x,d*=g.y,l+=C,u+=b,w=J(p),p=mn(w)}}return bt({width:f,height:d,x:l,y:u})}function In(e,t){const n=Nt(e).scrollLeft;return t?t.left+n:Oe(he(e)).left+n}function $o(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:In(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function Ci(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",a=he(o),c=t?Ot(t.floating):!1;if(o===a||c&&s)return n;let l={scrollLeft:0,scrollTop:0},u=me(1);const f=me(0),d=ve(o);if((d||!d&&!s)&&((ze(o)!=="body"||rt(a))&&(l=Nt(o)),ve(o))){const h=Oe(o);u=We(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const v=a&&!d&&!s?$o(a,l,!0):me(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+f.x+v.x,y:n.y*u.y-l.scrollTop*u.y+f.y+v.y}}function bi(e){return Array.from(e.getClientRects())}function Si(e){const t=he(e),n=Nt(e),o=e.ownerDocument.body,r=Q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=Q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let a=-n.scrollLeft+In(e);const c=-n.scrollTop;return ue(o).direction==="rtl"&&(a+=Q(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:a,y:c}}function Ei(e,t){const n=J(e),o=he(e),r=n.visualViewport;let s=o.clientWidth,a=o.clientHeight,c=0,l=0;if(r){s=r.width,a=r.height;const u=Mn();(!u||u&&t==="fixed")&&(c=r.offsetLeft,l=r.offsetTop)}return{width:s,height:a,x:c,y:l}}function Ri(e,t){const n=Oe(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=ve(e)?We(e):me(1),a=e.clientWidth*s.x,c=e.clientHeight*s.y,l=r*s.x,u=o*s.y;return{width:a,height:c,x:l,y:u}}function mo(e,t,n){let o;if(t==="viewport")o=Ei(e,n);else if(t==="document")o=Si(he(e));else if(le(t))o=Ri(t,n);else{const r=Fo(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return bt(o)}function Bo(e,t){const n=Pe(e);return n===t||!le(n)||He(n)?!1:ue(n).position==="fixed"||Bo(n,t)}function Pi(e,t){const n=t.get(e);if(n)return n;let o=Je(e,[],!1).filter(c=>le(c)&&ze(c)!=="body"),r=null;const s=ue(e).position==="fixed";let a=s?Pe(e):e;for(;le(a)&&!He(a);){const c=ue(a),l=An(a);!l&&c.position==="fixed"&&(r=null),(s?!l&&!r:!l&&c.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||rt(a)&&!l&&Bo(e,a))?o=o.filter(f=>f!==a):r=c,a=Pe(a)}return t.set(e,o),o}function Ai(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const a=[...n==="clippingAncestors"?Ot(t)?[]:Pi(t,this._c):[].concat(n),o],c=a[0],l=a.reduce((u,f)=>{const d=mo(t,f,r);return u.top=Q(d.top,u.top),u.right=Re(d.right,u.right),u.bottom=Re(d.bottom,u.bottom),u.left=Q(d.left,u.left),u},mo(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Mi(e){const{width:t,height:n}=jo(e);return{width:t,height:n}}function _i(e,t,n){const o=ve(t),r=he(t),s=n==="fixed",a=Oe(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=me(0);function u(){l.x=In(r)}if(o||!o&&!s)if((ze(t)!=="body"||rt(r))&&(c=Nt(t)),o){const h=Oe(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else r&&u();s&&!o&&r&&u();const f=r&&!o&&!s?$o(r,c):me(0),d=a.left+c.scrollLeft-l.x-f.x,v=a.top+c.scrollTop-l.y-f.y;return{x:d,y:v,width:a.width,height:a.height}}function tn(e){return ue(e).position==="static"}function vo(e,t){if(!ve(e)||ue(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return he(e)===n&&(n=n.ownerDocument.body),n}function Wo(e,t){const n=J(e);if(Ot(e))return n;if(!ve(e)){let r=Pe(e);for(;r&&!He(r);){if(le(r)&&!tn(r))return r;r=Pe(r)}return n}let o=vo(e,t);for(;o&&gi(o)&&tn(o);)o=vo(o,t);return o&&He(o)&&tn(o)&&!An(o)?n:o||wi(e)||n}const Ii=async function(e){const t=this.getOffsetParent||Wo,n=this.getDimensions,o=await n(e.floating);return{reference:_i(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Ti(e){return ue(e).direction==="rtl"}const Oi={convertOffsetParentRelativeRectToViewportRelativeRect:Ci,getDocumentElement:he,getClippingRect:Ai,getOffsetParent:Wo,getElementRects:Ii,getClientRects:bi,getDimensions:Mi,getScale:We,isElement:le,isRTL:Ti};function Vo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ni(e,t){let n=null,o;const r=he(e);function s(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function a(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:f,top:d,width:v,height:h}=u;if(c||t(),!v||!h)return;const w=dt(d),p=dt(r.clientWidth-(f+v)),g=dt(r.clientHeight-(d+h)),y=dt(f),C={rootMargin:-w+"px "+-p+"px "+-g+"px "+-y+"px",threshold:Q(0,Re(1,l))||1};let b=!0;function S(_){const E=_[0].intersectionRatio;if(E!==l){if(!b)return a();E?a(!1,E):o=setTimeout(()=>{a(!1,1e-7)},1e3)}E===1&&!Vo(u,e.getBoundingClientRect())&&a(),b=!1}try{n=new IntersectionObserver(S,{...C,root:r.ownerDocument})}catch{n=new IntersectionObserver(S,C)}n.observe(e)}return a(!0),s}function Di(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,u=_n(e),f=r||s?[...u?Je(u):[],...Je(t)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),s&&y.addEventListener("resize",n)});const d=u&&c?Ni(u,n):null;let v=-1,h=null;a&&(h=new ResizeObserver(y=>{let[x]=y;x&&x.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var C;(C=h)==null||C.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let w,p=l?Oe(e):null;l&&g();function g(){const y=Oe(e);p&&!Vo(p,y)&&n(),p=y,w=requestAnimationFrame(g)}return n(),()=>{var y;f.forEach(x=>{r&&x.removeEventListener("scroll",n),s&&x.removeEventListener("resize",n)}),d?.(),(y=h)==null||y.disconnect(),h=null,l&&cancelAnimationFrame(w)}}const ki=pi,Li=mi,ji=ui,Fi=hi,$i=di,ho=li,Bi=vi,Wi=(e,t,n)=>{const o=new Map,r={platform:Oi,...n},s={...r.platform,_c:o};return ci(e,t,{...r,platform:s})};var Vi=typeof document<"u",Hi=function(){},gt=Vi?i.useLayoutEffect:Hi;function St(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!St(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!St(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Ho(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function go(e,t){const n=Ho(e);return Math.round(t*n)/n}function nn(e){const t=i.useRef(e);return gt(()=>{t.current=e}),t}function Ui(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:a}={},transform:c=!0,whileElementsMounted:l,open:u}=e,[f,d]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=i.useState(o);St(v,o)||h(o);const[w,p]=i.useState(null),[g,y]=i.useState(null),x=i.useCallback(I=>{I!==_.current&&(_.current=I,p(I))},[]),C=i.useCallback(I=>{I!==E.current&&(E.current=I,y(I))},[]),b=s||w,S=a||g,_=i.useRef(null),E=i.useRef(null),M=i.useRef(f),D=l!=null,N=nn(l),$=nn(r),B=nn(u),L=i.useCallback(()=>{if(!_.current||!E.current)return;const I={placement:t,strategy:n,middleware:v};$.current&&(I.platform=$.current),Wi(_.current,E.current,I).then(P=>{const K={...P,isPositioned:B.current!==!1};k.current&&!St(M.current,K)&&(M.current=K,nt.flushSync(()=>{d(K)}))})},[v,t,n,$,B]);gt(()=>{u===!1&&M.current.isPositioned&&(M.current.isPositioned=!1,d(I=>({...I,isPositioned:!1})))},[u]);const k=i.useRef(!1);gt(()=>(k.current=!0,()=>{k.current=!1}),[]),gt(()=>{if(b&&(_.current=b),S&&(E.current=S),b&&S){if(N.current)return N.current(b,S,L);L()}},[b,S,L,N,D]);const W=i.useMemo(()=>({reference:_,floating:E,setReference:x,setFloating:C}),[x,C]),O=i.useMemo(()=>({reference:b,floating:S}),[b,S]),j=i.useMemo(()=>{const I={position:n,left:0,top:0};if(!O.floating)return I;const P=go(O.floating,f.x),K=go(O.floating,f.y);return c?{...I,transform:"translate("+P+"px, "+K+"px)",...Ho(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:P,top:K}},[n,c,O.floating,f.x,f.y]);return i.useMemo(()=>({...f,update:L,refs:W,elements:O,floatingStyles:j}),[f,L,W,O,j])}const Ki=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?ho({element:o.current,padding:r}).fn(n):{}:o?ho({element:o,padding:r}).fn(n):{}}}},Gi=(e,t)=>({...ki(e),options:[e,t]}),zi=(e,t)=>({...Li(e),options:[e,t]}),Yi=(e,t)=>({...Bi(e),options:[e,t]}),Xi=(e,t)=>({...ji(e),options:[e,t]}),qi=(e,t)=>({...Fi(e),options:[e,t]}),Zi=(e,t)=>({...$i(e),options:[e,t]}),Qi=(e,t)=>({...Ki(e),options:[e,t]});var Ji="Arrow",Uo=i.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return m.jsx(T.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});Uo.displayName=Ji;var ec=Uo;function Ko(e){const[t,n]=i.useState(void 0);return z(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var Tn="Popper",[Go,Ye]=se(Tn),[tc,zo]=Go(Tn),Yo=e=>{const{__scopePopper:t,children:n}=e,[o,r]=i.useState(null);return m.jsx(tc,{scope:t,anchor:o,onAnchorChange:r,children:n})};Yo.displayName=Tn;var Xo="PopperAnchor",qo=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=zo(Xo,n),a=i.useRef(null),c=F(t,a);return i.useEffect(()=>{s.onAnchorChange(o?.current||a.current)}),o?null:m.jsx(T.div,{...r,ref:c})});qo.displayName=Xo;var On="PopperContent",[nc,oc]=Go(On),Zo=i.forwardRef((e,t)=>{const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:v=!1,updatePositionStrategy:h="optimized",onPlaced:w,...p}=e,g=zo(On,n),[y,x]=i.useState(null),C=F(t,A=>x(A)),[b,S]=i.useState(null),_=Ko(b),E=_?.width??0,M=_?.height??0,D=o+(s!=="center"?"-"+s:""),N=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},$=Array.isArray(u)?u:[u],B=$.length>0,L={padding:N,boundary:$.filter(sc),altBoundary:B},{refs:k,floatingStyles:W,placement:O,isPositioned:j,middlewareData:I}=Ui({strategy:"fixed",placement:D,whileElementsMounted:(...A)=>Di(...A,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[Gi({mainAxis:r+M,alignmentAxis:a}),l&&zi({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?Yi():void 0,...L}),l&&Xi({...L}),qi({...L,apply:({elements:A,rects:V,availableWidth:X,availableHeight:H})=>{const{width:U,height:G}=V.reference,ne=A.floating.style;ne.setProperty("--radix-popper-available-width",`${X}px`),ne.setProperty("--radix-popper-available-height",`${H}px`),ne.setProperty("--radix-popper-anchor-width",`${U}px`),ne.setProperty("--radix-popper-anchor-height",`${G}px`)}}),b&&Qi({element:b,padding:c}),ac({arrowWidth:E,arrowHeight:M}),v&&Zi({strategy:"referenceHidden",...L})]}),[P,K]=er(O),Y=ce(w);z(()=>{j&&Y?.()},[j,Y]);const ae=I.arrow?.x,ge=I.arrow?.y,te=I.arrow?.centerOffset!==0,[we,Z]=i.useState();return z(()=>{y&&Z(window.getComputedStyle(y).zIndex)},[y]),m.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:j?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:we,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:m.jsx(nc,{scope:n,placedSide:P,onArrowChange:S,arrowX:ae,arrowY:ge,shouldHideArrow:te,children:m.jsx(T.div,{"data-side":P,"data-align":K,...p,ref:C,style:{...p.style,animation:j?void 0:"none"}})})})});Zo.displayName=On;var Qo="PopperArrow",rc={top:"bottom",right:"left",bottom:"top",left:"right"},Jo=i.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=oc(Qo,o),a=rc[s.placedSide];return m.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:m.jsx(ec,{...r,ref:n,style:{...r.style,display:"block"}})})});Jo.displayName=Qo;function sc(e){return e!==null}var ac=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:o,middlewareData:r}=t,a=r.arrow?.centerOffset!==0,c=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,f]=er(n),d={start:"0%",center:"50%",end:"100%"}[f],v=(r.arrow?.x??0)+c/2,h=(r.arrow?.y??0)+l/2;let w="",p="";return u==="bottom"?(w=a?d:`${v}px`,p=`${-l}px`):u==="top"?(w=a?d:`${v}px`,p=`${o.floating.height+l}px`):u==="right"?(w=`${-l}px`,p=a?d:`${h}px`):u==="left"&&(w=`${o.floating.width+l}px`,p=a?d:`${h}px`),{data:{x:w,y:p}}}});function er(e){const[t,n="center"]=e.split("-");return[t,n]}var Nn=Yo,Dt=qo,Dn=Zo,kn=Jo,ic="Portal",st=i.forwardRef((e,t)=>{const{container:n,...o}=e,[r,s]=i.useState(!1);z(()=>s(!0),[]);const a=n||r&&globalThis?.document?.body;return a?_a.createPortal(m.jsx(T.div,{...o,ref:t}),a):null});st.displayName=ic;var cc=Ao[" useInsertionEffect ".trim().toString()]||z;function Ce({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[r,s,a]=lc({defaultProp:t,onChange:n}),c=e!==void 0,l=c?e:r;{const f=i.useRef(e!==void 0);i.useEffect(()=>{const d=f.current;if(d!==c){const v=d?"controlled":"uncontrolled",h=c?"controlled":"uncontrolled"}f.current=c},[c,o])}const u=i.useCallback(f=>{if(c){const d=uc(f)?f(e):f;d!==e&&a.current?.(d)}else s(f)},[c,e,s,a]);return[l,u]}function lc({defaultProp:e,onChange:t}){const[n,o]=i.useState(e),r=i.useRef(n),s=i.useRef(t);return cc(()=>{s.current=t},[t]),i.useEffect(()=>{r.current!==n&&(s.current?.(n),r.current=n)},[n,r]),[n,o,s]}function uc(e){return typeof e=="function"}function tr(e){const t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var nr=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),dc="VisuallyHidden",or=i.forwardRef((e,t)=>m.jsx(T.span,{...e,ref:t,style:{...nr,...e.style}}));or.displayName=dc;var Td=or,fc=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Fe=new WeakMap,ft=new WeakMap,pt={},on=0,rr=function(e){return e&&(e.host||rr(e.parentNode))},pc=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=rr(n);return o&&e.contains(o)?o:null}).filter(function(n){return!!n})},mc=function(e,t,n,o){var r=pc(t,Array.isArray(e)?e:[e]);pt[n]||(pt[n]=new WeakMap);var s=pt[n],a=[],c=new Set,l=new Set(r),u=function(d){!d||c.has(d)||(c.add(d),u(d.parentNode))};r.forEach(u);var f=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(v){if(c.has(v))f(v);else try{var h=v.getAttribute(o),w=h!==null&&h!=="false",p=(Fe.get(v)||0)+1,g=(s.get(v)||0)+1;Fe.set(v,p),s.set(v,g),a.push(v),p===1&&w&&ft.set(v,!0),g===1&&v.setAttribute(n,"true"),w||v.setAttribute(o,"true")}catch{}})};return f(t),c.clear(),on++,function(){a.forEach(function(d){var v=Fe.get(d)-1,h=s.get(d)-1;Fe.set(d,v),s.set(d,h),v||(ft.has(d)||d.removeAttribute(o),ft.delete(d)),h||d.removeAttribute(n)}),on--,on||(Fe=new WeakMap,Fe=new WeakMap,ft=new WeakMap,pt={})}},kt=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=fc(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live], script"))),mc(o,r,n,"aria-hidden")):function(){return null}},fe=function(){return fe=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},fe.apply(this,arguments)};function sr(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function vc(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,s;o<r;o++)(s||!(o in t))&&(s||(s=Array.prototype.slice.call(t,0,o)),s[o]=t[o]);return e.concat(s||Array.prototype.slice.call(t))}var wt="right-scroll-bar-position",xt="width-before-scroll-bar",hc="with-scroll-bars-hidden",gc="--removed-body-scroll-bar-size";function rn(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function wc(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}var xc=typeof window<"u"?i.useLayoutEffect:i.useEffect,wo=new WeakMap;function yc(e,t){var n=wc(null,function(o){return e.forEach(function(r){return rn(r,o)})});return xc(function(){var o=wo.get(n);if(o){var r=new Set(o),s=new Set(e),a=n.current;r.forEach(function(c){s.has(c)||rn(c,null)}),s.forEach(function(c){r.has(c)||rn(c,a)})}wo.set(n,e)},[e]),n}function Cc(e){return e}function bc(e,t){t===void 0&&(t=Cc);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var a=t(s,o);return n.push(a),function(){n=n.filter(function(c){return c!==a})}},assignSyncMedium:function(s){for(o=!0;n.length;){var a=n;n=[],a.forEach(s)}n={push:function(c){return s(c)},filter:function(){return n}}},assignMedium:function(s){o=!0;var a=[];if(n.length){var c=n;n=[],c.forEach(s),a=n}var l=function(){var f=a;a=[],f.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(f){a.push(f),u()},filter:function(f){return a=a.filter(f),n}}}};return r}function Sc(e){e===void 0&&(e={});var t=bc(null);return t.options=fe({async:!0,ssr:!1},e),t}var ar=function(e){var t=e.sideCar,n=sr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return i.createElement(o,fe({},n))};ar.isSideCarExport=!0;function Ec(e,t){return e.useMedium(t),ar}var ir=Sc(),sn=function(){},Lt=i.forwardRef(function(e,t){var n=i.useRef(null),o=i.useState({onScrollCapture:sn,onWheelCapture:sn,onTouchMoveCapture:sn}),r=o[0],s=o[1],a=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,f=e.enabled,d=e.shards,v=e.sideCar,h=e.noRelative,w=e.noIsolation,p=e.inert,g=e.allowPinchZoom,y=e.as,x=y===void 0?"div":y,C=e.gapMode,b=sr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=v,_=yc([n,t]),E=fe(fe({},b),r);return i.createElement(i.Fragment,null,f&&i.createElement(S,{sideCar:ir,removeScrollBar:u,shards:d,noRelative:h,noIsolation:w,inert:p,setCallbacks:s,allowPinchZoom:!!g,lockRef:n,gapMode:C}),a?i.cloneElement(i.Children.only(c),fe(fe({},E),{ref:_})):i.createElement(x,fe({},E,{className:l,ref:_}),c))});Lt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Lt.classNames={fullWidth:xt,zeroRight:wt};var Rc=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Pc(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Rc();return t&&e.setAttribute("nonce",t),e}function Ac(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Mc(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var _c=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Pc())&&(Ac(t,n),Mc(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Ic=function(){var e=_c();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},cr=function(){var e=Ic(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},Tc={left:0,top:0,right:0,gap:0},an=function(e){return parseInt(e||"",10)||0},Oc=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[an(n),an(o),an(r)]},Nc=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Tc;var t=Oc(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Dc=cr(),Ve="data-scroll-locked",kc=function(e,t,n,o){var r=e.left,s=e.top,a=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(hc,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(c,"px ").concat(o,`;
  }
  body[`).concat(Ve,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(wt,` {
    right: `).concat(c,"px ").concat(o,`;
  }
  
  .`).concat(xt,` {
    margin-right: `).concat(c,"px ").concat(o,`;
  }
  
  .`).concat(wt," .").concat(wt,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(xt," .").concat(xt,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(Ve,`] {
    `).concat(gc,": ").concat(c,`px;
  }
`)},xo=function(){var e=parseInt(document.body.getAttribute(Ve)||"0",10);return isFinite(e)?e:0},Lc=function(){i.useEffect(function(){return document.body.setAttribute(Ve,(xo()+1).toString()),function(){var e=xo()-1;e<=0?document.body.removeAttribute(Ve):document.body.setAttribute(Ve,e.toString())}},[])},jc=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o;Lc();var s=i.useMemo(function(){return Nc(r)},[r]);return i.createElement(Dc,{styles:kc(s,!t,r,n?"":"!important")})},vn=!1;if(typeof window<"u")try{var mt=Object.defineProperty({},"passive",{get:function(){return vn=!0,!0}});window.addEventListener("test",mt,mt),window.removeEventListener("test",mt,mt)}catch{vn=!1}var $e=vn?{passive:!1}:!1,Fc=function(e){return e.tagName==="TEXTAREA"},lr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Fc(e)&&n[t]==="visible")},$c=function(e){return lr(e,"overflowY")},Bc=function(e){return lr(e,"overflowX")},yo=function(e,t){var n=t.ownerDocument,o=t;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var r=ur(e,o);if(r){var s=dr(e,o),a=s[1],c=s[2];if(a>c)return!0}o=o.parentNode}while(o&&o!==n.body);return!1},Wc=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},Vc=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},ur=function(e,t){return e==="v"?$c(t):Bc(t)},dr=function(e,t){return e==="v"?Wc(t):Vc(t)},Hc=function(e,t){return e==="h"&&t==="rtl"?-1:1},Uc=function(e,t,n,o,r){var s=Hc(e,window.getComputedStyle(t).direction),a=s*o,c=n.target,l=t.contains(c),u=!1,f=a>0,d=0,v=0;do{if(!c)break;var h=dr(e,c),w=h[0],p=h[1],g=h[2],y=p-g-s*w;(w||y)&&ur(e,c)&&(d+=y,v+=w);var x=c.parentNode;c=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(f&&Math.abs(d)<1||!f&&Math.abs(v)<1)&&(u=!0),u},vt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Co=function(e){return[e.deltaX,e.deltaY]},bo=function(e){return e&&"current"in e?e.current:e},Kc=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Gc=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},zc=0,Be=[];function Yc(e){var t=i.useRef([]),n=i.useRef([0,0]),o=i.useRef(),r=i.useState(zc++)[0],s=i.useState(cr)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var p=vc([e.lockRef.current],(e.shards||[]).map(bo),!0).filter(Boolean);return p.forEach(function(g){return g.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),p.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(p,g){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!a.current.allowPinchZoom;var y=vt(p),x=n.current,C="deltaX"in p?p.deltaX:x[0]-y[0],b="deltaY"in p?p.deltaY:x[1]-y[1],S,_=p.target,E=Math.abs(C)>Math.abs(b)?"h":"v";if("touches"in p&&E==="h"&&_.type==="range")return!1;var M=yo(E,_);if(!M)return!0;if(M?S=E:(S=E==="v"?"h":"v",M=yo(E,_)),!M)return!1;if(!o.current&&"changedTouches"in p&&(C||b)&&(o.current=S),!S)return!0;var D=o.current||S;return Uc(D,g,p,D==="h"?C:b)},[]),l=i.useCallback(function(p){var g=p;if(!(!Be.length||Be[Be.length-1]!==s)){var y="deltaY"in g?Co(g):vt(g),x=t.current.filter(function(S){return S.name===g.type&&(S.target===g.target||g.target===S.shadowParent)&&Kc(S.delta,y)})[0];if(x&&x.should){g.cancelable&&g.preventDefault();return}if(!x){var C=(a.current.shards||[]).map(bo).filter(Boolean).filter(function(S){return S.contains(g.target)}),b=C.length>0?c(g,C[0]):!a.current.noIsolation;b&&g.cancelable&&g.preventDefault()}}},[]),u=i.useCallback(function(p,g,y,x){var C={name:p,delta:g,target:y,should:x,shadowParent:Xc(y)};t.current.push(C),setTimeout(function(){t.current=t.current.filter(function(b){return b!==C})},1)},[]),f=i.useCallback(function(p){n.current=vt(p),o.current=void 0},[]),d=i.useCallback(function(p){u(p.type,Co(p),p.target,c(p,e.lockRef.current))},[]),v=i.useCallback(function(p){u(p.type,vt(p),p.target,c(p,e.lockRef.current))},[]);i.useEffect(function(){return Be.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:v}),document.addEventListener("wheel",l,$e),document.addEventListener("touchmove",l,$e),document.addEventListener("touchstart",f,$e),function(){Be=Be.filter(function(p){return p!==s}),document.removeEventListener("wheel",l,$e),document.removeEventListener("touchmove",l,$e),document.removeEventListener("touchstart",f,$e)}},[]);var h=e.removeScrollBar,w=e.inert;return i.createElement(i.Fragment,null,w?i.createElement(s,{styles:Gc(r)}):null,h?i.createElement(jc,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Xc(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const qc=Ec(ir,Yc);var at=i.forwardRef(function(e,t){return i.createElement(Lt,fe({},e,{ref:t,sideCar:qc}))});at.classNames=Lt.classNames;var Zc=[" ","Enter","ArrowUp","ArrowDown"],Qc=[" ","Enter"],Ne="Select",[jt,Ft,Jc]=Sn(Ne),[Xe,Od]=se(Ne,[Jc,Ye]),$t=Ye(),[el,Ae]=Xe(Ne),[tl,nl]=Xe(Ne),fr=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:a,defaultValue:c,onValueChange:l,dir:u,name:f,autoComplete:d,disabled:v,required:h,form:w}=e,p=$t(t),[g,y]=i.useState(null),[x,C]=i.useState(null),[b,S]=i.useState(!1),_=_t(u),[E,M]=Ce({prop:o,defaultProp:r??!1,onChange:s,caller:Ne}),[D,N]=Ce({prop:a,defaultProp:c,onChange:l,caller:Ne}),$=i.useRef(null),B=g?w||!!g.closest("form"):!0,[L,k]=i.useState(new Set),W=Array.from(L).map(O=>O.props.value).join(";");return m.jsx(Nn,{...p,children:m.jsxs(el,{required:h,scope:t,trigger:g,onTriggerChange:y,valueNode:x,onValueNodeChange:C,valueNodeHasChildren:b,onValueNodeHasChildrenChange:S,contentId:oe(),value:D,onValueChange:N,open:E,onOpenChange:M,dir:_,triggerPointerDownPosRef:$,disabled:v,children:[m.jsx(jt.Provider,{scope:t,children:m.jsx(tl,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(O=>{k(j=>new Set(j).add(O))},[]),onNativeOptionRemove:i.useCallback(O=>{k(j=>{const I=new Set(j);return I.delete(O),I})},[]),children:n})}),B?m.jsxs(jr,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:d,value:D,onChange:O=>N(O.target.value),disabled:v,form:w,children:[D===void 0?m.jsx("option",{value:""}):null,Array.from(L)]},W):null]})})};fr.displayName=Ne;var pr="SelectTrigger",mr=i.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=$t(n),a=Ae(pr,n),c=a.disabled||o,l=F(t,a.onTriggerChange),u=Ft(n),f=i.useRef("touch"),[d,v,h]=$r(p=>{const g=u().filter(C=>!C.disabled),y=g.find(C=>C.value===a.value),x=Br(g,p,y);x!==void 0&&a.onValueChange(x.value)}),w=p=>{c||(a.onOpenChange(!0),h()),p&&(a.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return m.jsx(Dt,{asChild:!0,...s,children:m.jsx(T.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Fr(a.value)?"":void 0,...r,ref:l,onClick:R(r.onClick,p=>{p.currentTarget.focus(),f.current!=="mouse"&&w(p)}),onPointerDown:R(r.onPointerDown,p=>{f.current=p.pointerType;const g=p.target;g.hasPointerCapture(p.pointerId)&&g.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(w(p),p.preventDefault())}),onKeyDown:R(r.onKeyDown,p=>{const g=d.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&v(p.key),!(g&&p.key===" ")&&Zc.includes(p.key)&&(w(),p.preventDefault())})})})});mr.displayName=pr;var vr="SelectValue",hr=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:a="",...c}=e,l=Ae(vr,n),{onValueNodeHasChildrenChange:u}=l,f=s!==void 0,d=F(t,l.onValueNodeChange);return z(()=>{u(f)},[u,f]),m.jsx(T.span,{...c,ref:d,style:{pointerEvents:"none"},children:Fr(l.value)?m.jsx(m.Fragment,{children:a}):s})});hr.displayName=vr;var ol="SelectIcon",gr=i.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return m.jsx(T.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});gr.displayName=ol;var rl="SelectPortal",wr=e=>m.jsx(st,{asChild:!0,...e});wr.displayName=rl;var De="SelectContent",xr=i.forwardRef((e,t)=>{const n=Ae(De,e.__scopeSelect),[o,r]=i.useState();if(z(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?nt.createPortal(m.jsx(yr,{scope:e.__scopeSelect,children:m.jsx(jt.Slot,{scope:e.__scopeSelect,children:m.jsx("div",{children:e.children})})}),s):null}return m.jsx(Cr,{...e,ref:t})});xr.displayName=De;var ie=10,[yr,Me]=Xe(De),sl="SelectContentImpl",al=Ee("SelectContent.RemoveScroll"),Cr=i.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:a,side:c,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:v,collisionPadding:h,sticky:w,hideWhenDetached:p,avoidCollisions:g,...y}=e,x=Ae(De,n),[C,b]=i.useState(null),[S,_]=i.useState(null),E=F(t,A=>b(A)),[M,D]=i.useState(null),[N,$]=i.useState(null),B=Ft(n),[L,k]=i.useState(!1),W=i.useRef(!1);i.useEffect(()=>{if(C)return kt(C)},[C]),It();const O=i.useCallback(A=>{const[V,...X]=B().map(G=>G.ref.current),[H]=X.slice(-1),U=document.activeElement;for(const G of A)if(G===U||(G?.scrollIntoView({block:"nearest"}),G===V&&S&&(S.scrollTop=0),G===H&&S&&(S.scrollTop=S.scrollHeight),G?.focus(),document.activeElement!==U))return},[B,S]),j=i.useCallback(()=>O([M,C]),[O,M,C]);i.useEffect(()=>{L&&j()},[L,j]);const{onOpenChange:I,triggerPointerDownPosRef:P}=x;i.useEffect(()=>{if(C){let A={x:0,y:0};const V=H=>{A={x:Math.abs(Math.round(H.pageX)-(P.current?.x??0)),y:Math.abs(Math.round(H.pageY)-(P.current?.y??0))}},X=H=>{A.x<=10&&A.y<=10?H.preventDefault():C.contains(H.target)||I(!1),document.removeEventListener("pointermove",V),P.current=null};return P.current!==null&&(document.addEventListener("pointermove",V),document.addEventListener("pointerup",X,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",V),document.removeEventListener("pointerup",X,{capture:!0})}}},[C,I,P]),i.useEffect(()=>{const A=()=>I(!1);return window.addEventListener("blur",A),window.addEventListener("resize",A),()=>{window.removeEventListener("blur",A),window.removeEventListener("resize",A)}},[I]);const[K,Y]=$r(A=>{const V=B().filter(U=>!U.disabled),X=V.find(U=>U.ref.current===document.activeElement),H=Br(V,A,X);H&&setTimeout(()=>H.ref.current.focus())}),ae=i.useCallback((A,V,X)=>{const H=!W.current&&!X;(x.value!==void 0&&x.value===V||H)&&(D(A),H&&(W.current=!0))},[x.value]),ge=i.useCallback(()=>C?.focus(),[C]),te=i.useCallback((A,V,X)=>{const H=!W.current&&!X;(x.value!==void 0&&x.value===V||H)&&$(A)},[x.value]),we=o==="popper"?hn:br,Z=we===hn?{side:c,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:v,collisionPadding:h,sticky:w,hideWhenDetached:p,avoidCollisions:g}:{};return m.jsx(yr,{scope:n,content:C,viewport:S,onViewportChange:_,itemRefCallback:ae,selectedItem:M,onItemLeave:ge,itemTextRefCallback:te,focusSelectedItem:j,selectedItemText:N,position:o,isPositioned:L,searchRef:K,children:m.jsx(at,{as:al,allowPinchZoom:!0,children:m.jsx(ot,{asChild:!0,trapped:x.open,onMountAutoFocus:A=>{A.preventDefault()},onUnmountAutoFocus:R(r,A=>{x.trigger?.focus({preventScroll:!0}),A.preventDefault()}),children:m.jsx(Ke,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:A=>A.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:m.jsx(we,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:A=>A.preventDefault(),...y,...Z,onPlaced:()=>k(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:R(y.onKeyDown,A=>{const V=A.ctrlKey||A.altKey||A.metaKey;if(A.key==="Tab"&&A.preventDefault(),!V&&A.key.length===1&&Y(A.key),["ArrowUp","ArrowDown","Home","End"].includes(A.key)){let H=B().filter(U=>!U.disabled).map(U=>U.ref.current);if(["ArrowUp","End"].includes(A.key)&&(H=H.slice().reverse()),["ArrowUp","ArrowDown"].includes(A.key)){const U=A.target,G=H.indexOf(U);H=H.slice(G+1)}setTimeout(()=>O(H)),A.preventDefault()}})})})})})})});Cr.displayName=sl;var il="SelectItemAlignedPosition",br=i.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=Ae(De,n),a=Me(De,n),[c,l]=i.useState(null),[u,f]=i.useState(null),d=F(t,E=>f(E)),v=Ft(n),h=i.useRef(!1),w=i.useRef(!0),{viewport:p,selectedItem:g,selectedItemText:y,focusSelectedItem:x}=a,C=i.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&u&&p&&g&&y){const E=s.trigger.getBoundingClientRect(),M=u.getBoundingClientRect(),D=s.valueNode.getBoundingClientRect(),N=y.getBoundingClientRect();if(s.dir!=="rtl"){const U=N.left-M.left,G=D.left-U,ne=E.left-G,Ie=E.width+ne,Xt=Math.max(Ie,M.width),qt=window.innerWidth-ie,Zt=eo(G,[ie,Math.max(ie,qt-Xt)]);c.style.minWidth=Ie+"px",c.style.left=Zt+"px"}else{const U=M.right-N.right,G=window.innerWidth-D.right-U,ne=window.innerWidth-E.right-G,Ie=E.width+ne,Xt=Math.max(Ie,M.width),qt=window.innerWidth-ie,Zt=eo(G,[ie,Math.max(ie,qt-Xt)]);c.style.minWidth=Ie+"px",c.style.right=Zt+"px"}const $=v(),B=window.innerHeight-ie*2,L=p.scrollHeight,k=window.getComputedStyle(u),W=parseInt(k.borderTopWidth,10),O=parseInt(k.paddingTop,10),j=parseInt(k.borderBottomWidth,10),I=parseInt(k.paddingBottom,10),P=W+O+L+I+j,K=Math.min(g.offsetHeight*5,P),Y=window.getComputedStyle(p),ae=parseInt(Y.paddingTop,10),ge=parseInt(Y.paddingBottom,10),te=E.top+E.height/2-ie,we=B-te,Z=g.offsetHeight/2,A=g.offsetTop+Z,V=W+O+A,X=P-V;if(V<=te){const U=$.length>0&&g===$[$.length-1].ref.current;c.style.bottom="0px";const G=u.clientHeight-p.offsetTop-p.offsetHeight,ne=Math.max(we,Z+(U?ge:0)+G+j),Ie=V+ne;c.style.height=Ie+"px"}else{const U=$.length>0&&g===$[0].ref.current;c.style.top="0px";const ne=Math.max(te,W+p.offsetTop+(U?ae:0)+Z)+X;c.style.height=ne+"px",p.scrollTop=V-te+p.offsetTop}c.style.margin=`${ie}px 0`,c.style.minHeight=K+"px",c.style.maxHeight=B+"px",o?.(),requestAnimationFrame(()=>h.current=!0)}},[v,s.trigger,s.valueNode,c,u,p,g,y,s.dir,o]);z(()=>C(),[C]);const[b,S]=i.useState();z(()=>{u&&S(window.getComputedStyle(u).zIndex)},[u]);const _=i.useCallback(E=>{E&&w.current===!0&&(C(),x?.(),w.current=!1)},[C,x]);return m.jsx(ll,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:_,children:m.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:b},children:m.jsx(T.div,{...r,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});br.displayName=il;var cl="SelectPopperPosition",hn=i.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=ie,...s}=e,a=$t(n);return m.jsx(Dn,{...a,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});hn.displayName=cl;var[ll,Ln]=Xe(De,{}),gn="SelectViewport",Sr=i.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=Me(gn,n),a=Ln(gn,n),c=F(t,s.onViewportChange),l=i.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),m.jsx(jt.Slot,{scope:n,children:m.jsx(T.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:R(r.onScroll,u=>{const f=u.currentTarget,{contentWrapper:d,shouldExpandOnScrollRef:v}=a;if(v?.current&&d){const h=Math.abs(l.current-f.scrollTop);if(h>0){const w=window.innerHeight-ie*2,p=parseFloat(d.style.minHeight),g=parseFloat(d.style.height),y=Math.max(p,g);if(y<w){const x=y+h,C=Math.min(w,x),b=x-C;d.style.height=C+"px",d.style.bottom==="0px"&&(f.scrollTop=b>0?b:0,d.style.justifyContent="flex-end")}}}l.current=f.scrollTop})})})]})});Sr.displayName=gn;var Er="SelectGroup",[ul,dl]=Xe(Er),Rr=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=oe();return m.jsx(ul,{scope:n,id:r,children:m.jsx(T.div,{role:"group","aria-labelledby":r,...o,ref:t})})});Rr.displayName=Er;var Pr="SelectLabel",Ar=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=dl(Pr,n);return m.jsx(T.div,{id:r.id,...o,ref:t})});Ar.displayName=Pr;var Et="SelectItem",[fl,Mr]=Xe(Et),_r=i.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...a}=e,c=Ae(Et,n),l=Me(Et,n),u=c.value===o,[f,d]=i.useState(s??""),[v,h]=i.useState(!1),w=F(t,x=>l.itemRefCallback?.(x,o,r)),p=oe(),g=i.useRef("touch"),y=()=>{r||(c.onValueChange(o),c.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(fl,{scope:n,value:o,disabled:r,textId:p,isSelected:u,onItemTextChange:i.useCallback(x=>{d(C=>C||(x?.textContent??"").trim())},[]),children:m.jsx(jt.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:m.jsx(T.div,{role:"option","aria-labelledby":p,"data-highlighted":v?"":void 0,"aria-selected":u&&v,"data-state":u?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...a,ref:w,onFocus:R(a.onFocus,()=>h(!0)),onBlur:R(a.onBlur,()=>h(!1)),onClick:R(a.onClick,()=>{g.current!=="mouse"&&y()}),onPointerUp:R(a.onPointerUp,()=>{g.current==="mouse"&&y()}),onPointerDown:R(a.onPointerDown,x=>{g.current=x.pointerType}),onPointerMove:R(a.onPointerMove,x=>{g.current=x.pointerType,r?l.onItemLeave?.():g.current==="mouse"&&x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:R(a.onPointerLeave,x=>{x.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:R(a.onKeyDown,x=>{l.searchRef?.current!==""&&x.key===" "||(Qc.includes(x.key)&&y(),x.key===" "&&x.preventDefault())})})})})});_r.displayName=Et;var qe="SelectItemText",Ir=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,a=Ae(qe,n),c=Me(qe,n),l=Mr(qe,n),u=nl(qe,n),[f,d]=i.useState(null),v=F(t,y=>d(y),l.onItemTextChange,y=>c.itemTextRefCallback?.(y,l.value,l.disabled)),h=f?.textContent,w=i.useMemo(()=>m.jsx("option",{value:l.value,disabled:l.disabled,children:h},l.value),[l.disabled,l.value,h]),{onNativeOptionAdd:p,onNativeOptionRemove:g}=u;return z(()=>(p(w),()=>g(w)),[p,g,w]),m.jsxs(m.Fragment,{children:[m.jsx(T.span,{id:l.textId,...s,ref:v}),l.isSelected&&a.valueNode&&!a.valueNodeHasChildren?nt.createPortal(s.children,a.valueNode):null]})});Ir.displayName=qe;var Tr="SelectItemIndicator",Or=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return Mr(Tr,n).isSelected?m.jsx(T.span,{"aria-hidden":!0,...o,ref:t}):null});Or.displayName=Tr;var wn="SelectScrollUpButton",Nr=i.forwardRef((e,t)=>{const n=Me(wn,e.__scopeSelect),o=Ln(wn,e.__scopeSelect),[r,s]=i.useState(!1),a=F(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?m.jsx(kr,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});Nr.displayName=wn;var xn="SelectScrollDownButton",Dr=i.forwardRef((e,t)=>{const n=Me(xn,e.__scopeSelect),o=Ln(xn,e.__scopeSelect),[r,s]=i.useState(!1),a=F(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollHeight-l.clientHeight,f=Math.ceil(l.scrollTop)<u;s(f)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?m.jsx(kr,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});Dr.displayName=xn;var kr=i.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=Me("SelectScrollButton",n),a=i.useRef(null),c=Ft(n),l=i.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return i.useEffect(()=>()=>l(),[l]),z(()=>{c().find(f=>f.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[c]),m.jsx(T.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:R(r.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(o,50))}),onPointerMove:R(r.onPointerMove,()=>{s.onItemLeave?.(),a.current===null&&(a.current=window.setInterval(o,50))}),onPointerLeave:R(r.onPointerLeave,()=>{l()})})}),pl="SelectSeparator",Lr=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return m.jsx(T.div,{"aria-hidden":!0,...o,ref:t})});Lr.displayName=pl;var yn="SelectArrow",ml=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=$t(n),s=Ae(yn,n),a=Me(yn,n);return s.open&&a.position==="popper"?m.jsx(kn,{...r,...o,ref:t}):null});ml.displayName=yn;var vl="SelectBubbleInput",jr=i.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=i.useRef(null),s=F(o,r),a=tr(t);return i.useEffect(()=>{const c=r.current;if(!c)return;const l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(a!==t&&f){const d=new Event("change",{bubbles:!0});f.call(c,t),c.dispatchEvent(d)}},[a,t]),m.jsx(T.select,{...n,style:{...nr,...n.style},ref:s,defaultValue:t})});jr.displayName=vl;function Fr(e){return e===""||e===void 0}function $r(e){const t=ce(e),n=i.useRef(""),o=i.useRef(0),r=i.useCallback(a=>{const c=n.current+a;t(c),function l(u){n.current=u,window.clearTimeout(o.current),u!==""&&(o.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=i.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function Br(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=hl(e,Math.max(s,0));r.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function hl(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Nd=fr,Dd=mr,kd=hr,Ld=gr,jd=wr,Fd=xr,$d=Sr,Bd=Rr,Wd=Ar,Vd=_r,Hd=Ir,Ud=Or,Kd=Nr,Gd=Dr,zd=Lr;function gl(e,t){return i.useReducer((n,o)=>t[n][o]??n,e)}var ee=e=>{const{present:t,children:n}=e,o=wl(t),r=typeof n=="function"?n({present:o.isPresent}):i.Children.only(n),s=F(o.ref,xl(r));return typeof n=="function"||o.isPresent?i.cloneElement(r,{ref:s}):null};ee.displayName="Presence";function wl(e){const[t,n]=i.useState(),o=i.useRef(null),r=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=gl(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=ht(o.current);s.current=c==="mounted"?u:"none"},[c]),z(()=>{const u=o.current,f=r.current;if(f!==e){const v=s.current,h=ht(u);e?l("MOUNT"):h==="none"||u?.display==="none"?l("UNMOUNT"):l(f&&v!==h?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,l]),z(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=h=>{const p=ht(o.current).includes(h.animationName);if(h.target===t&&p&&(l("ANIMATION_END"),!r.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},v=h=>{h.target===t&&(s.current=ht(o.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{o.current=u?getComputedStyle(u):null,n(u)},[])}}function ht(e){return e?.animationName||"none"}function xl(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Bt="Dialog",[Wr,Yd]=se(Bt),[yl,de]=Wr(Bt),Vr=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:a=!0}=e,c=i.useRef(null),l=i.useRef(null),[u,f]=Ce({prop:o,defaultProp:r??!1,onChange:s,caller:Bt});return m.jsx(yl,{scope:t,triggerRef:c,contentRef:l,contentId:oe(),titleId:oe(),descriptionId:oe(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(d=>!d),[f]),modal:a,children:n})};Vr.displayName=Bt;var Hr="DialogTrigger",Ur=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Hr,n),s=F(t,r.triggerRef);return m.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":$n(r.open),...o,ref:s,onClick:R(e.onClick,r.onOpenToggle)})});Ur.displayName=Hr;var jn="DialogPortal",[Cl,Kr]=Wr(jn,{forceMount:void 0}),Gr=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,s=de(jn,t);return m.jsx(Cl,{scope:t,forceMount:n,children:i.Children.map(o,a=>m.jsx(ee,{present:n||s.open,children:m.jsx(st,{asChild:!0,container:r,children:a})}))})};Gr.displayName=jn;var Rt="DialogOverlay",zr=i.forwardRef((e,t)=>{const n=Kr(Rt,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=de(Rt,e.__scopeDialog);return s.modal?m.jsx(ee,{present:o||s.open,children:m.jsx(Sl,{...r,ref:t})}):null});zr.displayName=Rt;var bl=Ee("DialogOverlay.RemoveScroll"),Sl=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Rt,n);return m.jsx(at,{as:bl,allowPinchZoom:!0,shards:[r.contentRef],children:m.jsx(T.div,{"data-state":$n(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),ke="DialogContent",Yr=i.forwardRef((e,t)=>{const n=Kr(ke,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=de(ke,e.__scopeDialog);return m.jsx(ee,{present:o||s.open,children:s.modal?m.jsx(El,{...r,ref:t}):m.jsx(Rl,{...r,ref:t})})});Yr.displayName=ke;var El=i.forwardRef((e,t)=>{const n=de(ke,e.__scopeDialog),o=i.useRef(null),r=F(t,n.contentRef,o);return i.useEffect(()=>{const s=o.current;if(s)return kt(s)},[]),m.jsx(Xr,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,c=a.button===0&&a.ctrlKey===!0;(a.button===2||c)&&s.preventDefault()}),onFocusOutside:R(e.onFocusOutside,s=>s.preventDefault())})}),Rl=i.forwardRef((e,t)=>{const n=de(ke,e.__scopeDialog),o=i.useRef(!1),r=i.useRef(!1);return m.jsx(Xr,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const a=s.target;n.triggerRef.current?.contains(a)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),Xr=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,...a}=e,c=de(ke,n),l=i.useRef(null),u=F(t,l);return It(),m.jsxs(m.Fragment,{children:[m.jsx(ot,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:m.jsx(Ke,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":$n(c.open),...a,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(Pl,{titleId:c.titleId}),m.jsx(Ml,{contentRef:l,descriptionId:c.descriptionId})]})]})}),Fn="DialogTitle",qr=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Fn,n);return m.jsx(T.h2,{id:r.titleId,...o,ref:t})});qr.displayName=Fn;var Zr="DialogDescription",Qr=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Zr,n);return m.jsx(T.p,{id:r.descriptionId,...o,ref:t})});Qr.displayName=Zr;var Jr="DialogClose",es=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Jr,n);return m.jsx(T.button,{type:"button",...o,ref:t,onClick:R(e.onClick,()=>r.onOpenChange(!1))})});es.displayName=Jr;function $n(e){return e?"open":"closed"}var ts="DialogTitleWarning",[Xd,ns]=Ia(ts,{contentName:ke,titleName:Fn,docsSlug:"dialog"}),Pl=({titleId:e})=>{const t=ns(ts),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{if(e){const o=document.getElementById(e)}},[n,e]),null},Al="DialogDescriptionWarning",Ml=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ns(Al).contentName}}.`;return i.useEffect(()=>{const r=e.current?.getAttribute("aria-describedby");if(t&&r){const s=document.getElementById(t)}},[o,e,t]),null},qd=Vr,Zd=Ur,Qd=Gr,Jd=zr,ef=Yr,tf=qr,nf=Qr,of=es,Wt="Popover",[os,rf]=se(Wt,[Ye]),it=Ye(),[_l,_e]=os(Wt),rs=e=>{const{__scopePopover:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:a=!1}=e,c=it(t),l=i.useRef(null),[u,f]=i.useState(!1),[d,v]=Ce({prop:o,defaultProp:r??!1,onChange:s,caller:Wt});return m.jsx(Nn,{...c,children:m.jsx(_l,{scope:t,contentId:oe(),triggerRef:l,open:d,onOpenChange:v,onOpenToggle:i.useCallback(()=>v(h=>!h),[v]),hasCustomAnchor:u,onCustomAnchorAdd:i.useCallback(()=>f(!0),[]),onCustomAnchorRemove:i.useCallback(()=>f(!1),[]),modal:a,children:n})})};rs.displayName=Wt;var ss="PopoverAnchor",Il=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=_e(ss,n),s=it(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:c}=r;return i.useEffect(()=>(a(),()=>c()),[a,c]),m.jsx(Dt,{...s,...o,ref:t})});Il.displayName=ss;var as="PopoverTrigger",is=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=_e(as,n),s=it(n),a=F(t,r.triggerRef),c=m.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":fs(r.open),...o,ref:a,onClick:R(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?c:m.jsx(Dt,{asChild:!0,...s,children:c})});is.displayName=as;var Bn="PopoverPortal",[Tl,Ol]=os(Bn,{forceMount:void 0}),cs=e=>{const{__scopePopover:t,forceMount:n,children:o,container:r}=e,s=_e(Bn,t);return m.jsx(Tl,{scope:t,forceMount:n,children:m.jsx(ee,{present:n||s.open,children:m.jsx(st,{asChild:!0,container:r,children:o})})})};cs.displayName=Bn;var Ue="PopoverContent",ls=i.forwardRef((e,t)=>{const n=Ol(Ue,e.__scopePopover),{forceMount:o=n.forceMount,...r}=e,s=_e(Ue,e.__scopePopover);return m.jsx(ee,{present:o||s.open,children:s.modal?m.jsx(Dl,{...r,ref:t}):m.jsx(kl,{...r,ref:t})})});ls.displayName=Ue;var Nl=Ee("PopoverContent.RemoveScroll"),Dl=i.forwardRef((e,t)=>{const n=_e(Ue,e.__scopePopover),o=i.useRef(null),r=F(t,o),s=i.useRef(!1);return i.useEffect(()=>{const a=o.current;if(a)return kt(a)},[]),m.jsx(at,{as:Nl,allowPinchZoom:!0,children:m.jsx(us,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,a=>{a.preventDefault(),s.current||n.triggerRef.current?.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0,u=c.button===2||l;s.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:R(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1})})})}),kl=i.forwardRef((e,t)=>{const n=_e(Ue,e.__scopePopover),o=i.useRef(!1),r=i.useRef(!1);return m.jsx(us,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const a=s.target;n.triggerRef.current?.contains(a)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),us=i.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,disableOutsidePointerEvents:a,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:f,...d}=e,v=_e(Ue,n),h=it(n);return It(),m.jsx(ot,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:m.jsx(Ke,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>v.onOpenChange(!1),children:m.jsx(Dn,{"data-state":fs(v.open),role:"dialog",id:v.contentId,...h,...d,ref:t,style:{...d.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),ds="PopoverClose",Ll=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=_e(ds,n);return m.jsx(T.button,{type:"button",...o,ref:t,onClick:R(e.onClick,()=>r.onOpenChange(!1))})});Ll.displayName=ds;var jl="PopoverArrow",Fl=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=it(n);return m.jsx(kn,{...r,...o,ref:t})});Fl.displayName=jl;function fs(e){return e?"open":"closed"}var sf=rs,af=is,cf=cs,lf=ls,Vt="Collapsible",[$l,uf]=se(Vt),[Bl,Wn]=$l(Vt),ps=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:o,defaultOpen:r,disabled:s,onOpenChange:a,...c}=e,[l,u]=Ce({prop:o,defaultProp:r??!1,onChange:a,caller:Vt});return m.jsx(Bl,{scope:n,disabled:s,contentId:oe(),open:l,onOpenToggle:i.useCallback(()=>u(f=>!f),[u]),children:m.jsx(T.div,{"data-state":Hn(l),"data-disabled":s?"":void 0,...c,ref:t})})});ps.displayName=Vt;var ms="CollapsibleTrigger",Wl=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,...o}=e,r=Wn(ms,n);return m.jsx(T.button,{type:"button","aria-controls":r.contentId,"aria-expanded":r.open||!1,"data-state":Hn(r.open),"data-disabled":r.disabled?"":void 0,disabled:r.disabled,...o,ref:t,onClick:R(e.onClick,r.onOpenToggle)})});Wl.displayName=ms;var Vn="CollapsibleContent",Vl=i.forwardRef((e,t)=>{const{forceMount:n,...o}=e,r=Wn(Vn,e.__scopeCollapsible);return m.jsx(ee,{present:n||r.open,children:({present:s})=>m.jsx(Hl,{...o,ref:t,present:s})})});Vl.displayName=Vn;var Hl=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:o,children:r,...s}=e,a=Wn(Vn,n),[c,l]=i.useState(o),u=i.useRef(null),f=F(t,u),d=i.useRef(0),v=d.current,h=i.useRef(0),w=h.current,p=a.open||c,g=i.useRef(p),y=i.useRef(void 0);return i.useEffect(()=>{const x=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(x)},[]),z(()=>{const x=u.current;if(x){y.current=y.current||{transitionDuration:x.style.transitionDuration,animationName:x.style.animationName},x.style.transitionDuration="0s",x.style.animationName="none";const C=x.getBoundingClientRect();d.current=C.height,h.current=C.width,g.current||(x.style.transitionDuration=y.current.transitionDuration,x.style.animationName=y.current.animationName),l(o)}},[a.open,o]),m.jsx(T.div,{"data-state":Hn(a.open),"data-disabled":a.disabled?"":void 0,id:a.contentId,hidden:!p,...s,ref:f,style:{"--radix-collapsible-content-height":v?`${v}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:p&&r})});function Hn(e){return e?"open":"closed"}var df=ps,cn={exports:{}},ln={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var So;function Ul(){if(So)return ln;So=1;var e=Ma();function t(d,v){return d===v&&(d!==0||1/d===1/v)||d!==d&&v!==v}var n=typeof Object.is=="function"?Object.is:t,o=e.useState,r=e.useEffect,s=e.useLayoutEffect,a=e.useDebugValue;function c(d,v){var h=v(),w=o({inst:{value:h,getSnapshot:v}}),p=w[0].inst,g=w[1];return s(function(){p.value=h,p.getSnapshot=v,l(p)&&g({inst:p})},[d,h,v]),r(function(){return l(p)&&g({inst:p}),d(function(){l(p)&&g({inst:p})})},[d]),a(h),h}function l(d){var v=d.getSnapshot;d=d.value;try{var h=v();return!n(d,h)}catch{return!0}}function u(d,v){return v()}var f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u:c;return ln.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:f,ln}var Eo;function Kl(){return Eo||(Eo=1,cn.exports=Ul()),cn.exports}var Gl=Kl();function zl(){return Gl.useSyncExternalStore(Yl,()=>!0,()=>!1)}function Yl(){return()=>{}}var Un="Avatar",[Xl,ff]=se(Un),[ql,vs]=Xl(Un),hs=i.forwardRef((e,t)=>{const{__scopeAvatar:n,...o}=e,[r,s]=i.useState("idle");return m.jsx(ql,{scope:n,imageLoadingStatus:r,onImageLoadingStatusChange:s,children:m.jsx(T.span,{...o,ref:t})})});hs.displayName=Un;var gs="AvatarImage",Zl=i.forwardRef((e,t)=>{const{__scopeAvatar:n,src:o,onLoadingStatusChange:r=()=>{},...s}=e,a=vs(gs,n),c=Ql(o,s),l=ce(u=>{r(u),a.onImageLoadingStatusChange(u)});return z(()=>{c!=="idle"&&l(c)},[c,l]),c==="loaded"?m.jsx(T.img,{...s,ref:t,src:o}):null});Zl.displayName=gs;var ws="AvatarFallback",xs=i.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:o,...r}=e,s=vs(ws,n),[a,c]=i.useState(o===void 0);return i.useEffect(()=>{if(o!==void 0){const l=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(l)}},[o]),a&&s.imageLoadingStatus!=="loaded"?m.jsx(T.span,{...r,ref:t}):null});xs.displayName=ws;function Ro(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Ql(e,{referrerPolicy:t,crossOrigin:n}){const o=zl(),r=i.useRef(null),s=o?(r.current||(r.current=new window.Image),r.current):null,[a,c]=i.useState(()=>Ro(s,e));return z(()=>{c(Ro(s,e))},[s,e]),z(()=>{const l=d=>()=>{c(d)};if(!s)return;const u=l("loaded"),f=l("error");return s.addEventListener("load",u),s.addEventListener("error",f),t&&(s.referrerPolicy=t),typeof n=="string"&&(s.crossOrigin=n),()=>{s.removeEventListener("load",u),s.removeEventListener("error",f)}},[s,n,t]),a}var pf=hs,mf=xs,un="rovingFocusGroup.onEntryFocus",Jl={bubbles:!1,cancelable:!0},ct="RovingFocusGroup",[Cn,ys,eu]=Sn(ct),[tu,Ht]=se(ct,[eu]),[nu,ou]=tu(ct),Cs=i.forwardRef((e,t)=>m.jsx(Cn.Provider,{scope:e.__scopeRovingFocusGroup,children:m.jsx(Cn.Slot,{scope:e.__scopeRovingFocusGroup,children:m.jsx(ru,{...e,ref:t})})}));Cs.displayName=ct;var ru=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:s,currentTabStopId:a,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...d}=e,v=i.useRef(null),h=F(t,v),w=_t(s),[p,g]=Ce({prop:a,defaultProp:c??null,onChange:l,caller:ct}),[y,x]=i.useState(!1),C=ce(u),b=ys(n),S=i.useRef(!1),[_,E]=i.useState(0);return i.useEffect(()=>{const M=v.current;if(M)return M.addEventListener(un,C),()=>M.removeEventListener(un,C)},[C]),m.jsx(nu,{scope:n,orientation:o,dir:w,loop:r,currentTabStopId:p,onItemFocus:i.useCallback(M=>g(M),[g]),onItemShiftTab:i.useCallback(()=>x(!0),[]),onFocusableItemAdd:i.useCallback(()=>E(M=>M+1),[]),onFocusableItemRemove:i.useCallback(()=>E(M=>M-1),[]),children:m.jsx(T.div,{tabIndex:y||_===0?-1:0,"data-orientation":o,...d,ref:h,style:{outline:"none",...e.style},onMouseDown:R(e.onMouseDown,()=>{S.current=!0}),onFocus:R(e.onFocus,M=>{const D=!S.current;if(M.target===M.currentTarget&&D&&!y){const N=new CustomEvent(un,Jl);if(M.currentTarget.dispatchEvent(N),!N.defaultPrevented){const $=b().filter(O=>O.focusable),B=$.find(O=>O.active),L=$.find(O=>O.id===p),W=[B,L,...$].filter(Boolean).map(O=>O.ref.current);Es(W,f)}}S.current=!1}),onBlur:R(e.onBlur,()=>x(!1))})})}),bs="RovingFocusGroupItem",Ss=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:s,children:a,...c}=e,l=oe(),u=s||l,f=ou(bs,n),d=f.currentTabStopId===u,v=ys(n),{onFocusableItemAdd:h,onFocusableItemRemove:w,currentTabStopId:p}=f;return i.useEffect(()=>{if(o)return h(),()=>w()},[o,h,w]),m.jsx(Cn.ItemSlot,{scope:n,id:u,focusable:o,active:r,children:m.jsx(T.span,{tabIndex:d?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:R(e.onMouseDown,g=>{o?f.onItemFocus(u):g.preventDefault()}),onFocus:R(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:R(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){f.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const y=iu(g,f.orientation,f.dir);if(y!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let C=v().filter(b=>b.focusable).map(b=>b.ref.current);if(y==="last")C.reverse();else if(y==="prev"||y==="next"){y==="prev"&&C.reverse();const b=C.indexOf(g.currentTarget);C=f.loop?cu(C,b+1):C.slice(b+1)}setTimeout(()=>Es(C))}}),children:typeof a=="function"?a({isCurrentTabStop:d,hasTabStop:p!=null}):a})})});Ss.displayName=bs;var su={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function au(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function iu(e,t,n){const o=au(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return su[o]}function Es(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function cu(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Rs=Cs,Ps=Ss,bn=["Enter"," "],lu=["ArrowDown","PageUp","Home"],As=["ArrowUp","PageDown","End"],uu=[...lu,...As],du={ltr:[...bn,"ArrowRight"],rtl:[...bn,"ArrowLeft"]},fu={ltr:["ArrowLeft"],rtl:["ArrowRight"]},lt="Menu",[et,pu,mu]=Sn(lt),[Le,Ms]=se(lt,[mu,Ye,Ht]),Ut=Ye(),_s=Ht(),[vu,je]=Le(lt),[hu,ut]=Le(lt),Is=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:r,onOpenChange:s,modal:a=!0}=e,c=Ut(t),[l,u]=i.useState(null),f=i.useRef(!1),d=ce(s),v=_t(r);return i.useEffect(()=>{const h=()=>{f.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>f.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),m.jsx(Nn,{...c,children:m.jsx(vu,{scope:t,open:n,onOpenChange:d,content:l,onContentChange:u,children:m.jsx(hu,{scope:t,onClose:i.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:v,modal:a,children:o})})})};Is.displayName=lt;var gu="MenuAnchor",Kn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Ut(n);return m.jsx(Dt,{...r,...o,ref:t})});Kn.displayName=gu;var Gn="MenuPortal",[wu,Ts]=Le(Gn,{forceMount:void 0}),Os=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:r}=e,s=je(Gn,t);return m.jsx(wu,{scope:t,forceMount:n,children:m.jsx(ee,{present:n||s.open,children:m.jsx(st,{asChild:!0,container:r,children:o})})})};Os.displayName=Gn;var re="MenuContent",[xu,zn]=Le(re),Ns=i.forwardRef((e,t)=>{const n=Ts(re,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=je(re,e.__scopeMenu),a=ut(re,e.__scopeMenu);return m.jsx(et.Provider,{scope:e.__scopeMenu,children:m.jsx(ee,{present:o||s.open,children:m.jsx(et.Slot,{scope:e.__scopeMenu,children:a.modal?m.jsx(yu,{...r,ref:t}):m.jsx(Cu,{...r,ref:t})})})})}),yu=i.forwardRef((e,t)=>{const n=je(re,e.__scopeMenu),o=i.useRef(null),r=F(t,o);return i.useEffect(()=>{const s=o.current;if(s)return kt(s)},[]),m.jsx(Yn,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:R(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Cu=i.forwardRef((e,t)=>{const n=je(re,e.__scopeMenu);return m.jsx(Yn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),bu=Ee("MenuContent.ScrollLock"),Yn=i.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,onDismiss:h,disableOutsideScroll:w,...p}=e,g=je(re,n),y=ut(re,n),x=Ut(n),C=_s(n),b=pu(n),[S,_]=i.useState(null),E=i.useRef(null),M=F(t,E,g.onContentChange),D=i.useRef(0),N=i.useRef(""),$=i.useRef(0),B=i.useRef(null),L=i.useRef("right"),k=i.useRef(0),W=w?at:i.Fragment,O=w?{as:bu,allowPinchZoom:!0}:void 0,j=P=>{const K=N.current+P,Y=b().filter(A=>!A.disabled),ae=document.activeElement,ge=Y.find(A=>A.ref.current===ae)?.textValue,te=Y.map(A=>A.textValue),we=Du(te,K,ge),Z=Y.find(A=>A.textValue===we)?.ref.current;(function A(V){N.current=V,window.clearTimeout(D.current),V!==""&&(D.current=window.setTimeout(()=>A(""),1e3))})(K),Z&&setTimeout(()=>Z.focus())};i.useEffect(()=>()=>window.clearTimeout(D.current),[]),It();const I=i.useCallback(P=>L.current===B.current?.side&&Lu(P,B.current?.area),[]);return m.jsx(xu,{scope:n,searchRef:N,onItemEnter:i.useCallback(P=>{I(P)&&P.preventDefault()},[I]),onItemLeave:i.useCallback(P=>{I(P)||(E.current?.focus(),_(null))},[I]),onTriggerLeave:i.useCallback(P=>{I(P)&&P.preventDefault()},[I]),pointerGraceTimerRef:$,onPointerGraceIntentChange:i.useCallback(P=>{B.current=P},[]),children:m.jsx(W,{...O,children:m.jsx(ot,{asChild:!0,trapped:r,onMountAutoFocus:R(s,P=>{P.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:m.jsx(Ke,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,onDismiss:h,children:m.jsx(Rs,{asChild:!0,...C,dir:y.dir,orientation:"vertical",loop:o,currentTabStopId:S,onCurrentTabStopIdChange:_,onEntryFocus:R(l,P=>{y.isUsingKeyboardRef.current||P.preventDefault()}),preventScrollOnEntryFocus:!0,children:m.jsx(Dn,{role:"menu","aria-orientation":"vertical","data-state":Xs(g.open),"data-radix-menu-content":"",dir:y.dir,...x,...p,ref:M,style:{outline:"none",...p.style},onKeyDown:R(p.onKeyDown,P=>{const Y=P.target.closest("[data-radix-menu-content]")===P.currentTarget,ae=P.ctrlKey||P.altKey||P.metaKey,ge=P.key.length===1;Y&&(P.key==="Tab"&&P.preventDefault(),!ae&&ge&&j(P.key));const te=E.current;if(P.target!==te||!uu.includes(P.key))return;P.preventDefault();const Z=b().filter(A=>!A.disabled).map(A=>A.ref.current);As.includes(P.key)&&Z.reverse(),Ou(Z)}),onBlur:R(e.onBlur,P=>{P.currentTarget.contains(P.target)||(window.clearTimeout(D.current),N.current="")}),onPointerMove:R(e.onPointerMove,tt(P=>{const K=P.target,Y=k.current!==P.clientX;if(P.currentTarget.contains(K)&&Y){const ae=P.clientX>k.current?"right":"left";L.current=ae,k.current=P.clientX}}))})})})})})})});Ns.displayName=re;var Su="MenuGroup",Xn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(T.div,{role:"group",...o,ref:t})});Xn.displayName=Su;var Eu="MenuLabel",Ds=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(T.div,{...o,ref:t})});Ds.displayName=Eu;var Pt="MenuItem",Po="menu.itemSelect",Kt=i.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...r}=e,s=i.useRef(null),a=ut(Pt,e.__scopeMenu),c=zn(Pt,e.__scopeMenu),l=F(t,s),u=i.useRef(!1),f=()=>{const d=s.current;if(!n&&d){const v=new CustomEvent(Po,{bubbles:!0,cancelable:!0});d.addEventListener(Po,h=>o?.(h),{once:!0}),_o(d,v),v.defaultPrevented?u.current=!1:a.onClose()}};return m.jsx(ks,{...r,ref:l,disabled:n,onClick:R(e.onClick,f),onPointerDown:d=>{e.onPointerDown?.(d),u.current=!0},onPointerUp:R(e.onPointerUp,d=>{u.current||d.currentTarget?.click()}),onKeyDown:R(e.onKeyDown,d=>{const v=c.searchRef.current!=="";n||v&&d.key===" "||bn.includes(d.key)&&(d.currentTarget.click(),d.preventDefault())})})});Kt.displayName=Pt;var ks=i.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:r,...s}=e,a=zn(Pt,n),c=_s(n),l=i.useRef(null),u=F(t,l),[f,d]=i.useState(!1),[v,h]=i.useState("");return i.useEffect(()=>{const w=l.current;w&&h((w.textContent??"").trim())},[s.children]),m.jsx(et.ItemSlot,{scope:n,disabled:o,textValue:r??v,children:m.jsx(Ps,{asChild:!0,...c,focusable:!o,children:m.jsx(T.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:u,onPointerMove:R(e.onPointerMove,tt(w=>{o?a.onItemLeave(w):(a.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:R(e.onPointerLeave,tt(w=>a.onItemLeave(w))),onFocus:R(e.onFocus,()=>d(!0)),onBlur:R(e.onBlur,()=>d(!1))})})})}),Ru="MenuCheckboxItem",Ls=i.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...r}=e;return m.jsx(Ws,{scope:e.__scopeMenu,checked:n,children:m.jsx(Kt,{role:"menuitemcheckbox","aria-checked":At(n)?"mixed":n,...r,ref:t,"data-state":Zn(n),onSelect:R(r.onSelect,()=>o?.(At(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Ls.displayName=Ru;var js="MenuRadioGroup",[Pu,Au]=Le(js,{value:void 0,onValueChange:()=>{}}),Fs=i.forwardRef((e,t)=>{const{value:n,onValueChange:o,...r}=e,s=ce(o);return m.jsx(Pu,{scope:e.__scopeMenu,value:n,onValueChange:s,children:m.jsx(Xn,{...r,ref:t})})});Fs.displayName=js;var $s="MenuRadioItem",Bs=i.forwardRef((e,t)=>{const{value:n,...o}=e,r=Au($s,e.__scopeMenu),s=n===r.value;return m.jsx(Ws,{scope:e.__scopeMenu,checked:s,children:m.jsx(Kt,{role:"menuitemradio","aria-checked":s,...o,ref:t,"data-state":Zn(s),onSelect:R(o.onSelect,()=>r.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});Bs.displayName=$s;var qn="MenuItemIndicator",[Ws,Mu]=Le(qn,{checked:!1}),Vs=i.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...r}=e,s=Mu(qn,n);return m.jsx(ee,{present:o||At(s.checked)||s.checked===!0,children:m.jsx(T.span,{...r,ref:t,"data-state":Zn(s.checked)})})});Vs.displayName=qn;var _u="MenuSeparator",Hs=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(T.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});Hs.displayName=_u;var Iu="MenuArrow",Us=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Ut(n);return m.jsx(kn,{...r,...o,ref:t})});Us.displayName=Iu;var Tu="MenuSub",[vf,Ks]=Le(Tu),Ze="MenuSubTrigger",Gs=i.forwardRef((e,t)=>{const n=je(Ze,e.__scopeMenu),o=ut(Ze,e.__scopeMenu),r=Ks(Ze,e.__scopeMenu),s=zn(Ze,e.__scopeMenu),a=i.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},f=i.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return i.useEffect(()=>f,[f]),i.useEffect(()=>{const d=c.current;return()=>{window.clearTimeout(d),l(null)}},[c,l]),m.jsx(Kn,{asChild:!0,...u,children:m.jsx(ks,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":Xs(n.open),...e,ref:Mt(t,r.onTriggerChange),onClick:d=>{e.onClick?.(d),!(e.disabled||d.defaultPrevented)&&(d.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:R(e.onPointerMove,tt(d=>{s.onItemEnter(d),!d.defaultPrevented&&!e.disabled&&!n.open&&!a.current&&(s.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:R(e.onPointerLeave,tt(d=>{f();const v=n.content?.getBoundingClientRect();if(v){const h=n.content?.dataset.side,w=h==="right",p=w?-5:5,g=v[w?"left":"right"],y=v[w?"right":"left"];s.onPointerGraceIntentChange({area:[{x:d.clientX+p,y:d.clientY},{x:g,y:v.top},{x:y,y:v.top},{x:y,y:v.bottom},{x:g,y:v.bottom}],side:h}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(d),d.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:R(e.onKeyDown,d=>{const v=s.searchRef.current!=="";e.disabled||v&&d.key===" "||du[o.dir].includes(d.key)&&(n.onOpenChange(!0),n.content?.focus(),d.preventDefault())})})})});Gs.displayName=Ze;var zs="MenuSubContent",Ys=i.forwardRef((e,t)=>{const n=Ts(re,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=je(re,e.__scopeMenu),a=ut(re,e.__scopeMenu),c=Ks(zs,e.__scopeMenu),l=i.useRef(null),u=F(t,l);return m.jsx(et.Provider,{scope:e.__scopeMenu,children:m.jsx(ee,{present:o||s.open,children:m.jsx(et.Slot,{scope:e.__scopeMenu,children:m.jsx(Yn,{id:c.contentId,"aria-labelledby":c.triggerId,...r,ref:u,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{a.isUsingKeyboardRef.current&&l.current?.focus(),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:R(e.onFocusOutside,f=>{f.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:R(e.onEscapeKeyDown,f=>{a.onClose(),f.preventDefault()}),onKeyDown:R(e.onKeyDown,f=>{const d=f.currentTarget.contains(f.target),v=fu[a.dir].includes(f.key);d&&v&&(s.onOpenChange(!1),c.trigger?.focus(),f.preventDefault())})})})})})});Ys.displayName=zs;function Xs(e){return e?"open":"closed"}function At(e){return e==="indeterminate"}function Zn(e){return At(e)?"indeterminate":e?"checked":"unchecked"}function Ou(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Nu(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function Du(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=Nu(e,Math.max(s,0));r.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function ku(e,t){const{x:n,y:o}=e;let r=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const c=t[s],l=t[a],u=c.x,f=c.y,d=l.x,v=l.y;f>o!=v>o&&n<(d-u)*(o-f)/(v-f)+u&&(r=!r)}return r}function Lu(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return ku(n,t)}function tt(e){return t=>t.pointerType==="mouse"?e(t):void 0}var ju=Is,Fu=Kn,$u=Os,Bu=Ns,Wu=Xn,Vu=Ds,Hu=Kt,Uu=Ls,Ku=Fs,Gu=Bs,zu=Vs,Yu=Hs,Xu=Us,qu=Gs,Zu=Ys,Gt="DropdownMenu",[Qu,hf]=se(Gt,[Ms]),q=Ms(),[Ju,qs]=Qu(Gt),Zs=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:r,defaultOpen:s,onOpenChange:a,modal:c=!0}=e,l=q(t),u=i.useRef(null),[f,d]=Ce({prop:r,defaultProp:s??!1,onChange:a,caller:Gt});return m.jsx(Ju,{scope:t,triggerId:oe(),triggerRef:u,contentId:oe(),open:f,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(v=>!v),[d]),modal:c,children:m.jsx(ju,{...l,open:f,onOpenChange:d,dir:o,modal:c,children:n})})};Zs.displayName=Gt;var Qs="DropdownMenuTrigger",Js=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...r}=e,s=qs(Qs,n),a=q(n);return m.jsx(Fu,{asChild:!0,...a,children:m.jsx(T.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:Mt(t,s.triggerRef),onPointerDown:R(e.onPointerDown,c=>{!o&&c.button===0&&c.ctrlKey===!1&&(s.onOpenToggle(),s.open||c.preventDefault())}),onKeyDown:R(e.onKeyDown,c=>{o||(["Enter"," "].includes(c.key)&&s.onOpenToggle(),c.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});Js.displayName=Qs;var ed="DropdownMenuPortal",ea=e=>{const{__scopeDropdownMenu:t,...n}=e,o=q(t);return m.jsx($u,{...o,...n})};ea.displayName=ed;var ta="DropdownMenuContent",na=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=qs(ta,n),s=q(n),a=i.useRef(!1);return m.jsx(Bu,{id:r.contentId,"aria-labelledby":r.triggerId,...s,...o,ref:t,onCloseAutoFocus:R(e.onCloseAutoFocus,c=>{a.current||r.triggerRef.current?.focus(),a.current=!1,c.preventDefault()}),onInteractOutside:R(e.onInteractOutside,c=>{const l=c.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,f=l.button===2||u;(!r.modal||f)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});na.displayName=ta;var td="DropdownMenuGroup",oa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Wu,{...r,...o,ref:t})});oa.displayName=td;var nd="DropdownMenuLabel",ra=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Vu,{...r,...o,ref:t})});ra.displayName=nd;var od="DropdownMenuItem",sa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Hu,{...r,...o,ref:t})});sa.displayName=od;var rd="DropdownMenuCheckboxItem",aa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Uu,{...r,...o,ref:t})});aa.displayName=rd;var sd="DropdownMenuRadioGroup",ad=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Ku,{...r,...o,ref:t})});ad.displayName=sd;var id="DropdownMenuRadioItem",cd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Gu,{...r,...o,ref:t})});cd.displayName=id;var ld="DropdownMenuItemIndicator",ia=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(zu,{...r,...o,ref:t})});ia.displayName=ld;var ud="DropdownMenuSeparator",ca=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Yu,{...r,...o,ref:t})});ca.displayName=ud;var dd="DropdownMenuArrow",fd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Xu,{...r,...o,ref:t})});fd.displayName=dd;var pd="DropdownMenuSubTrigger",md=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(qu,{...r,...o,ref:t})});md.displayName=pd;var vd="DropdownMenuSubContent",hd=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Zu,{...r,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});hd.displayName=vd;var gf=Zs,wf=Js,xf=ea,yf=na,Cf=oa,bf=ra,Sf=sa,Ef=aa,Rf=ia,Pf=ca,gd="Label",la=i.forwardRef((e,t)=>m.jsx(T.label,{...e,ref:t,onMouseDown:n=>{n.target.closest("button, input, select, textarea")||(e.onMouseDown?.(n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));la.displayName=gd;var Af=la,zt="Checkbox",[wd,Mf]=se(zt),[xd,Qn]=wd(zt);function yd(e){const{__scopeCheckbox:t,checked:n,children:o,defaultChecked:r,disabled:s,form:a,name:c,onCheckedChange:l,required:u,value:f="on",internal_do_not_use_render:d}=e,[v,h]=Ce({prop:n,defaultProp:r??!1,onChange:l,caller:zt}),[w,p]=i.useState(null),[g,y]=i.useState(null),x=i.useRef(!1),C=w?!!a||!!w.closest("form"):!0,b={checked:v,disabled:s,setChecked:h,control:w,setControl:p,name:c,form:a,value:f,hasConsumerStoppedPropagationRef:x,required:u,defaultChecked:Se(r)?!1:r,isFormControl:C,bubbleInput:g,setBubbleInput:y};return m.jsx(xd,{scope:t,...b,children:Sd(d)?d(b):o})}var ua="CheckboxTrigger",da=i.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...o},r)=>{const{control:s,value:a,disabled:c,checked:l,required:u,setControl:f,setChecked:d,hasConsumerStoppedPropagationRef:v,isFormControl:h,bubbleInput:w}=Qn(ua,e),p=F(r,f),g=i.useRef(l);return i.useEffect(()=>{const y=s?.form;if(y){const x=()=>d(g.current);return y.addEventListener("reset",x),()=>y.removeEventListener("reset",x)}},[s,d]),m.jsx(T.button,{type:"button",role:"checkbox","aria-checked":Se(l)?"mixed":l,"aria-required":u,"data-state":va(l),"data-disabled":c?"":void 0,disabled:c,value:a,...o,ref:p,onKeyDown:R(t,y=>{y.key==="Enter"&&y.preventDefault()}),onClick:R(n,y=>{d(x=>Se(x)?!0:!x),w&&h&&(v.current=y.isPropagationStopped(),v.current||y.stopPropagation())})})});da.displayName=ua;var Cd=i.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:o,checked:r,defaultChecked:s,required:a,disabled:c,value:l,onCheckedChange:u,form:f,...d}=e;return m.jsx(yd,{__scopeCheckbox:n,checked:r,defaultChecked:s,disabled:c,required:a,onCheckedChange:u,name:o,form:f,value:l,internal_do_not_use_render:({isFormControl:v})=>m.jsxs(m.Fragment,{children:[m.jsx(da,{...d,ref:t,__scopeCheckbox:n}),v&&m.jsx(ma,{__scopeCheckbox:n})]})})});Cd.displayName=zt;var fa="CheckboxIndicator",bd=i.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:o,...r}=e,s=Qn(fa,n);return m.jsx(ee,{present:o||Se(s.checked)||s.checked===!0,children:m.jsx(T.span,{"data-state":va(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});bd.displayName=fa;var pa="CheckboxBubbleInput",ma=i.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:o,hasConsumerStoppedPropagationRef:r,checked:s,defaultChecked:a,required:c,disabled:l,name:u,value:f,form:d,bubbleInput:v,setBubbleInput:h}=Qn(pa,e),w=F(n,h),p=tr(s),g=Ko(o);i.useEffect(()=>{const x=v;if(!x)return;const C=window.HTMLInputElement.prototype,S=Object.getOwnPropertyDescriptor(C,"checked").set,_=!r.current;if(p!==s&&S){const E=new Event("click",{bubbles:_});x.indeterminate=Se(s),S.call(x,Se(s)?!1:s),x.dispatchEvent(E)}},[v,p,s,r]);const y=i.useRef(Se(s)?!1:s);return m.jsx(T.input,{type:"checkbox","aria-hidden":!0,defaultChecked:a??y.current,required:c,disabled:l,name:u,value:f,form:d,...t,tabIndex:-1,ref:w,style:{...t.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});ma.displayName=pa;function Sd(e){return typeof e=="function"}function Se(e){return e==="indeterminate"}function va(e){return Se(e)?"indeterminate":e?"checked":"unchecked"}var Yt="Tabs",[Ed,_f]=se(Yt,[Ht]),ha=Ht(),[Rd,Jn]=Ed(Yt),ga=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,onValueChange:r,defaultValue:s,orientation:a="horizontal",dir:c,activationMode:l="automatic",...u}=e,f=_t(c),[d,v]=Ce({prop:o,onChange:r,defaultProp:s??"",caller:Yt});return m.jsx(Rd,{scope:n,baseId:oe(),value:d,onValueChange:v,orientation:a,dir:f,activationMode:l,children:m.jsx(T.div,{dir:f,"data-orientation":a,...u,ref:t})})});ga.displayName=Yt;var wa="TabsList",xa=i.forwardRef((e,t)=>{const{__scopeTabs:n,loop:o=!0,...r}=e,s=Jn(wa,n),a=ha(n);return m.jsx(Rs,{asChild:!0,...a,orientation:s.orientation,dir:s.dir,loop:o,children:m.jsx(T.div,{role:"tablist","aria-orientation":s.orientation,...r,ref:t})})});xa.displayName=wa;var ya="TabsTrigger",Ca=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,disabled:r=!1,...s}=e,a=Jn(ya,n),c=ha(n),l=Ea(a.baseId,o),u=Ra(a.baseId,o),f=o===a.value;return m.jsx(Ps,{asChild:!0,...c,focusable:!r,active:f,children:m.jsx(T.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:l,...s,ref:t,onMouseDown:R(e.onMouseDown,d=>{!r&&d.button===0&&d.ctrlKey===!1?a.onValueChange(o):d.preventDefault()}),onKeyDown:R(e.onKeyDown,d=>{[" ","Enter"].includes(d.key)&&a.onValueChange(o)}),onFocus:R(e.onFocus,()=>{const d=a.activationMode!=="manual";!f&&!r&&d&&a.onValueChange(o)})})})});Ca.displayName=ya;var ba="TabsContent",Sa=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,forceMount:r,children:s,...a}=e,c=Jn(ba,n),l=Ea(c.baseId,o),u=Ra(c.baseId,o),f=o===c.value,d=i.useRef(f);return i.useEffect(()=>{const v=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(v)},[]),m.jsx(ee,{present:r||f,children:({present:v})=>m.jsx(T.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!v,id:u,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:v&&s})})});Sa.displayName=ba;function Ea(e,t){return`${e}-trigger-${t}`}function Ra(e,t){return`${e}-content-${t}`}var If=ga,Tf=xa,Of=Ca,Nf=Sa;export{_a as $,Cd as A,bd as B,Nf as C,nf as D,Nd as E,Dd as F,Ld as G,jd as H,Ps as I,Fd as J,$d as K,Tf as L,Vd as M,Ud as N,Jd as O,T as P,Hd as Q,Rs as R,Ad as S,Of as T,Kd as U,kd as V,Gd as W,Bd as X,Wd as Y,zd as Z,Ye as _,Ce as a,Nn as a0,Dt as a1,st as a2,kn as a3,Ke as a4,Dn as a5,Md as a6,Td as a7,Sn as a8,Id as a9,_d as aa,or as ab,_o as ac,df as ad,Wl as ae,Vl as af,pf as ag,mf as ah,gf as ai,wf as aj,xf as ak,yf as al,Ef as am,Rf as an,bf as ao,Sf as ap,Pf as aq,Cf as ar,Af as as,se as b,Ht as c,F as d,R as e,ee as f,tr as g,Ko as h,If as i,oe as j,qd as k,Qd as l,ef as m,Mt as n,sf as o,af as p,cf as q,nt as r,lf as s,of as t,_t as u,tf as v,Zd as w,ce as x,z as y,eo as z};
//# sourceMappingURL=radix-CuNiF4nS.js.map
