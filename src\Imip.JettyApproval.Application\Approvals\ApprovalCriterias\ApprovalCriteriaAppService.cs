using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Mapping.Mappers;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Permissions.Apps;
using Imip.JettyApproval.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// Application service for ApprovalCriteria entity
/// </summary>
[Authorize(JettyApprovalPermission.PolicyApprovalCriteria.Default)]
public class ApprovalCriteriaAppService :
    CrudAppService<ApprovalCriteria, ApprovalCriteriaDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalCriteriaDto, CreateUpdateApprovalCriteriaDto>,
    IApprovalCriteriaAppService
{
    private readonly IApprovalCriteriaRepository _approvalCriteriaRepository;
    private readonly ApprovalCriteriaMapper _mapper;
    private readonly ILogger<ApprovalCriteriaAppService> _logger;

    public ApprovalCriteriaAppService(
        IApprovalCriteriaRepository approvalCriteriaRepository,
        ApprovalCriteriaMapper mapper,
        ILogger<ApprovalCriteriaAppService> logger)
        : base(approvalCriteriaRepository)
    {
        _approvalCriteriaRepository = approvalCriteriaRepository;
        _mapper = mapper;
        _logger = logger;
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalCriteria.Create)]
    public override async Task<ApprovalCriteriaDto> CreateAsync(CreateUpdateApprovalCriteriaDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalCriteriaRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalCriteria.Edit)]
    public override async Task<ApprovalCriteriaDto> UpdateAsync(Guid id, CreateUpdateApprovalCriteriaDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalCriteriaRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalCriteriaRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalCriteria.Delete)]
    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalCriteriaRepository.GetAsync(id);

        await _approvalCriteriaRepository.DeleteAsync(entity, autoSave: true);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalCriteria.View)]
    public override async Task<ApprovalCriteriaDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalCriteriaRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalCriteria.View)]
    public override async Task<PagedResultDto<ApprovalCriteriaDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalCriteriaRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalCriteriaDto>(totalCount, dtos);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalCriteria.View)]
    public virtual async Task<PagedResultDto<ApprovalCriteriaDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _approvalCriteriaRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<ApprovalCriteriaDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<ApprovalCriteria> ApplyDynamicQuery(IQueryable<ApprovalCriteria> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalCriteria>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalCriteria>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}