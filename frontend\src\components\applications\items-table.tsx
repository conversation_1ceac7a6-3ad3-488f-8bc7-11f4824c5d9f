import type { FilterCondition, FilterGroup, FilterOperator, JettyRequestItemDto, LogicalOperator } from "@/client/types.gen";
import { DataTable } from "@/components/data-table/DataTable";
import FilterSortBar, { type SortDirection } from "@/components/filter-sort-bar";
import { Button } from "@/components/ui/button";
import ErrorBoundary from "@/components/ui/error-boundary";
import TableSkeleton from "@/components/ui/table-skeleton";
import { useJettyRequestItems } from "@/lib/hooks/useJettyRequestItems";
import { router } from "@inertiajs/react";
import { type ColumnDef, type PaginationState } from "@tanstack/react-table";
import { AlertTriangle, Plus, RefreshCw } from "lucide-react";
import React, { useMemo, useState } from "react";

const FILTER_FIELDS = [
  { value: "tenantName", label: "Tenant Name" },
  { value: "itemName", label: "Item Name" },
  { value: "qty", label: "Quantity" },
  { value: "uoM", label: "Unit of Measurement" },
  { value: "status", label: "Status" },
  { value: "jettyRequestId", label: "Jetty Request ID" },
];

const FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [
  { value: "Equals", label: "Equals" },
  { value: "Contains", label: "Contains" },
  { value: "NotEquals", label: "Not Equals" },
  { value: "GreaterThan", label: ">" },
  { value: "LessThan", label: "<" },
];

const columns: ColumnDef<JettyRequestItemDto>[] = [
  {
    accessorKey: "tenantName",
    header: "Tenant Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "itemName",
    header: "Item Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "qty",
    header: "Quantity",
    cell: info => {
      const value = info.getValue() as number;
      return value !== undefined ? value.toString() : "-";
    },
  },
  {
    accessorKey: "uoM",
    header: "Unit of Measurement",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "notes",
    header: "Notes",
    cell: info => {
      const notes = info.getValue() as string;
      return notes ? (notes.length > 50 ? `${notes.substring(0, 50)}...` : notes) : "-";
    },
  },
  {
    accessorKey: "creationTime",
    header: "Created",
    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
  },
];

const ItemsTableContent: React.FC = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Convert filters to FilterGroup
  const filterGroup: FilterGroup | undefined = useMemo(() => {
    if (filters.length === 0) return undefined;

    return {
      operator: "And" as LogicalOperator,
      conditions: filters,
    };
  }, [filters]);

  // Convert sorts to string
  const sorting = useMemo(() => {
    if (sorts.length === 0) return undefined;
    return sorts.map(sort => `${sort.field} ${sort.direction}`).join(",");
  }, [sorts]);

  const { data, isLoading, error, refetch } = useJettyRequestItems(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    sorting
  );

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleNewItem = () => {
    router.visit("/application/items/create");
  };

  // Handle error state
  if (error) {
    return (
      <div className="bg-card text-card-foreground rounded-xl border shadow-sm p-6">
        <div className="flex items-center gap-2 text-destructive mb-4">
          <AlertTriangle className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Error Loading Items</h2>
        </div>
        <p className="text-muted-foreground mb-4">
          {error.message || "An unexpected error occurred while loading the items."}
        </p>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2">
      <div className="text-xl font-bold px-2 pt-2 pb-1">Jetty Request Items</div>
      <FilterSortBar
        filterFields={FILTER_FIELDS}
        operators={FILTER_OPERATORS}
        filters={filters}
        sorts={sorts}
        onFiltersChange={setFilters}
        onSortsChange={setSorts}
      >
        <div className="ml-auto flex items-center gap-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="icon"
            className="h-10 w-10"
            disabled={isLoading || isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            onClick={handleNewItem}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-full shadow font-semibold text-base"
            size="lg"
          >
            <Plus className="h-5 w-5" /> New Item
          </Button>
        </div>
      </FilterSortBar>
      {isLoading ? (
        <TableSkeleton columns={columns} />
      ) : (
        <DataTable
          title=""
          columns={columns}
          data={data?.items ?? []}
          totalCount={data?.totalCount ?? 0}
          isLoading={isLoading}
          manualPagination={true}
          pageSize={pagination.pageSize}
          onPaginationChange={setPagination}
          hideDefaultFilterbar={true}
          enableRowSelection={false}
          manualSorting={true}
          // Sorting and filtering are handled above
        />
      )}
    </div>
  );
};

const ItemsTable: React.FC = () => {
  return (
    <ErrorBoundary>
      <ItemsTableContent />
    </ErrorBoundary>
  );
};

export default ItemsTable; 