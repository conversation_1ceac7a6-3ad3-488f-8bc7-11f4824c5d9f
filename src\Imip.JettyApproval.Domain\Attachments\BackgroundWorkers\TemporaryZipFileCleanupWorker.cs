using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.BlobStoring;
using Volo.Abp.Threading;

namespace Imip.JettyApproval.Attachments.BackgroundWorkers;

/// <summary>
/// Background worker for cleaning up temporary ZIP files
/// </summary>
public class TemporaryZipFileCleanupWorker : AsyncPeriodicBackgroundWorkerBase
{
    private readonly ILogger<TemporaryZipFileCleanupWorker> _logger;

    public TemporaryZipFileCleanupWorker(
        AbpAsyncTimer timer,
        IServiceScopeFactory serviceScopeFactory,
        ILogger<TemporaryZipFileCleanupWorker> logger)
        : base(timer, serviceScopeFactory)
    {
        _logger = logger;
        // Run every minute
        timer.Period = 60 * 1000;
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        try
        {
            var temporaryZipFileRepository = workerContext.ServiceProvider.GetRequiredService<ITemporaryZipFileRepository>();
            var blobContainer = workerContext.ServiceProvider.GetRequiredService<IBlobContainerFactory>().Create("default");

            // Get files that are due for deletion
            var now = DateTime.UtcNow;
            var filesToDelete = await temporaryZipFileRepository.GetDueForDeletionAsync(now);

            // Process each file
            foreach (var file in filesToDelete)
            {
                try
                {
                    // Delete the file from blob storage if BlobName is not null
                    if (!string.IsNullOrEmpty(file.BlobName))
                    {
                        await blobContainer.DeleteAsync(file.BlobName);
                    }
                    else if (!string.IsNullOrEmpty(file.Description))
                    {
                        // This is a local file, the Description field contains the file paths
                        try
                        {
                            // Parse the file paths from the Description field
                            var paths = file.Description.Split('|');
                            if (paths.Length >= 1 && !string.IsNullOrEmpty(paths[0]))
                            {
                                // Delete the DOCX file if it exists
                                if (System.IO.File.Exists(paths[0]))
                                {
                                    System.IO.File.Delete(paths[0]);
                                }
                            }

                            if (paths.Length >= 2 && !string.IsNullOrEmpty(paths[1]))
                            {
                                // Delete the PDF file if it exists
                                if (System.IO.File.Exists(paths[1]))
                                {
                                    System.IO.File.Delete(paths[1]);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting local files for temporary ZIP file with ID {Id}", file.Id);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Skipped deletion of temporary ZIP file with ID {Id} because both BlobName and Description are null", file.Id);
                    }

                    // Mark the file as processed
                    await temporaryZipFileRepository.MarkAsProcessedAsync(file.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting temporary ZIP file {Id} with blob name {BlobName}",
                        file.Id, file.BlobName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in temporary ZIP file cleanup worker");
        }
    }
}