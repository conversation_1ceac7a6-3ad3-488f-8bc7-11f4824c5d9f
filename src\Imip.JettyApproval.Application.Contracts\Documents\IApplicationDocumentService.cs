using System;
using System.Threading.Tasks;
using Imip.JettyApproval.Attachments;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// Interface for application document generation service
/// </summary>
public interface IApplicationDocumentService : IApplicationService
{
    /// <summary>
    /// Generates an application document for a JettyRequestItem and saves it as an attachment
    /// </summary>
    /// <param name="input">The document generation input</param>
    /// <returns>The file upload result with stream URL</returns>
    Task<FileUploadResultDto> GenerateApplicationDocumentAsAttachmentAsync(ApplicationDocumentGenerationDto input);

    /// <summary>
    /// Generates an application document for a JettyRequestItem and returns the file content
    /// </summary>
    /// <param name="input">The document generation input</param>
    /// <returns>The generated file</returns>
    Task<FileDto> GenerateApplicationDocumentAsync(ApplicationDocumentGenerationDto input);
}
