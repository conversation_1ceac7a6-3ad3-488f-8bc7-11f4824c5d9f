var De=e=>{throw TypeError(e)};var ae=(e,t,s)=>t.has(e)||De("Cannot "+s);var i=(e,t,s)=>(ae(e,t,"read from private field"),s?s.call(e):t.get(e)),p=(e,t,s)=>t.has(e)?De("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),l=(e,t,s,r)=>(ae(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),R=(e,t,s)=>(ae(e,t,"access private method"),s);var re=(e,t,s,r)=>({set _(u){l(e,t,u,s)},get _(){return i(e,t,r)}});function Cs(e,t){for(var s=0;s<t.length;s++){const r=t[s];if(typeof r!="string"&&!Array.isArray(r)){for(const u in r)if(u!=="default"&&!(u in e)){const a=Object.getOwnPropertyDescriptor(r,u);a&&Object.defineProperty(e,u,a.get?a:{enumerable:!0,get:()=>r[u]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var ar=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ss(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function cr(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if(typeof t=="function"){var s=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};s.prototype=t.prototype}else s={};return Object.defineProperty(s,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var u=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(s,r,u.get?u:{enumerable:!0,get:function(){return e[r]}})}),s}var ce={exports:{}},zt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xe;function _s(){if(xe)return zt;xe=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function s(r,u,a){var f=null;if(a!==void 0&&(f=""+a),u.key!==void 0&&(f=""+u.key),"key"in u){a={};for(var d in u)d!=="key"&&(a[d]=u[d])}else a=u;return u=a.ref,{$$typeof:e,type:r,key:f,ref:u!==void 0?u:null,props:a}}return zt.Fragment=t,zt.jsx=s,zt.jsxs=s,zt}var Qe;function ws(){return Qe||(Qe=1,ce.exports=_s()),ce.exports}var Ps=ws(),he={exports:{}},b={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Ts(){if(je)return b;je=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),a=Symbol.for("react.consumer"),f=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),o=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.iterator;function j(n){return n===null||typeof n!="object"?null:(n=v&&n[v]||n["@@iterator"],typeof n=="function"?n:null)}var P={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,w={};function T(n,c,m){this.props=n,this.context=c,this.refs=w,this.updater=m||P}T.prototype.isReactComponent={},T.prototype.setState=function(n,c){if(typeof n!="object"&&typeof n!="function"&&n!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,n,c,"setState")},T.prototype.forceUpdate=function(n){this.updater.enqueueForceUpdate(this,n,"forceUpdate")};function I(){}I.prototype=T.prototype;function k(n,c,m){this.props=n,this.context=c,this.refs=w,this.updater=m||P}var Y=k.prototype=new I;Y.constructor=k,M(Y,T.prototype),Y.isPureReactComponent=!0;var W=Array.isArray,C={H:null,A:null,T:null,S:null,V:null},ut=Object.prototype.hasOwnProperty;function ee(n,c,m,g,S,F){return m=F.ref,{$$typeof:e,type:n,key:c,ref:m!==void 0?m:null,props:F}}function B(n,c){return ee(n.type,c,void 0,void 0,void 0,n.props)}function q(n){return typeof n=="object"&&n!==null&&n.$$typeof===e}function Dt(n){var c={"=":"=0",":":"=2"};return"$"+n.replace(/[=:]/g,function(m){return c[m]})}var st=/\/+/g;function at(n,c){return typeof n=="object"&&n!==null&&n.key!=null?Dt(""+n.key):c.toString(36)}function Me(){}function bs(n){switch(n.status){case"fulfilled":return n.value;case"rejected":throw n.reason;default:switch(typeof n.status=="string"?n.then(Me,Me):(n.status="pending",n.then(function(c){n.status==="pending"&&(n.status="fulfilled",n.value=c)},function(c){n.status==="pending"&&(n.status="rejected",n.reason=c)})),n.status){case"fulfilled":return n.value;case"rejected":throw n.reason}}throw n}function xt(n,c,m,g,S){var F=typeof n;(F==="undefined"||F==="boolean")&&(n=null);var O=!1;if(n===null)O=!0;else switch(F){case"bigint":case"string":case"number":O=!0;break;case"object":switch(n.$$typeof){case e:case t:O=!0;break;case y:return O=n._init,xt(O(n._payload),c,m,g,S)}}if(O)return S=S(n),O=g===""?"."+at(n,0):g,W(S)?(m="",O!=null&&(m=O.replace(st,"$&/")+"/"),xt(S,c,m,"",function(Es){return Es})):S!=null&&(q(S)&&(S=B(S,m+(S.key==null||n&&n.key===S.key?"":(""+S.key).replace(st,"$&/")+"/")+O)),c.push(S)),1;O=0;var ct=g===""?".":g+":";if(W(n))for(var D=0;D<n.length;D++)g=n[D],F=ct+at(g,D),O+=xt(g,c,m,F,S);else if(D=j(n),typeof D=="function")for(n=D.call(n),D=0;!(g=n.next()).done;)g=g.value,F=ct+at(g,D++),O+=xt(g,c,m,F,S);else if(F==="object"){if(typeof n.then=="function")return xt(bs(n),c,m,g,S);throw c=String(n),Error("Objects are not valid as a React child (found: "+(c==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":c)+"). If you meant to render a collection of children, use an array instead.")}return O}function se(n,c,m){if(n==null)return n;var g=[],S=0;return xt(n,g,"","",function(F){return c.call(m,F,S++)}),g}function Rs(n){if(n._status===-1){var c=n._result;c=c(),c.then(function(m){(n._status===0||n._status===-1)&&(n._status=1,n._result=m)},function(m){(n._status===0||n._status===-1)&&(n._status=2,n._result=m)}),n._status===-1&&(n._status=0,n._result=c)}if(n._status===1)return n._result.default;throw n._result}var Fe=typeof reportError=="function"?reportError:function(n){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var c=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof n=="object"&&n!==null&&typeof n.message=="string"?String(n.message):String(n),error:n});if(!window.dispatchEvent(c))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",n);return}};function Os(){}return b.Children={map:se,forEach:function(n,c,m){se(n,function(){c.apply(this,arguments)},m)},count:function(n){var c=0;return se(n,function(){c++}),c},toArray:function(n){return se(n,function(c){return c})||[]},only:function(n){if(!q(n))throw Error("React.Children.only expected to receive a single React element child.");return n}},b.Component=T,b.Fragment=s,b.Profiler=u,b.PureComponent=k,b.StrictMode=r,b.Suspense=h,b.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=C,b.__COMPILER_RUNTIME={__proto__:null,c:function(n){return C.H.useMemoCache(n)}},b.cache=function(n){return function(){return n.apply(null,arguments)}},b.cloneElement=function(n,c,m){if(n==null)throw Error("The argument must be a React element, but you passed "+n+".");var g=M({},n.props),S=n.key,F=void 0;if(c!=null)for(O in c.ref!==void 0&&(F=void 0),c.key!==void 0&&(S=""+c.key),c)!ut.call(c,O)||O==="key"||O==="__self"||O==="__source"||O==="ref"&&c.ref===void 0||(g[O]=c[O]);var O=arguments.length-2;if(O===1)g.children=m;else if(1<O){for(var ct=Array(O),D=0;D<O;D++)ct[D]=arguments[D+2];g.children=ct}return ee(n.type,S,void 0,void 0,F,g)},b.createContext=function(n){return n={$$typeof:f,_currentValue:n,_currentValue2:n,_threadCount:0,Provider:null,Consumer:null},n.Provider=n,n.Consumer={$$typeof:a,_context:n},n},b.createElement=function(n,c,m){var g,S={},F=null;if(c!=null)for(g in c.key!==void 0&&(F=""+c.key),c)ut.call(c,g)&&g!=="key"&&g!=="__self"&&g!=="__source"&&(S[g]=c[g]);var O=arguments.length-2;if(O===1)S.children=m;else if(1<O){for(var ct=Array(O),D=0;D<O;D++)ct[D]=arguments[D+2];S.children=ct}if(n&&n.defaultProps)for(g in O=n.defaultProps,O)S[g]===void 0&&(S[g]=O[g]);return ee(n,F,void 0,void 0,null,S)},b.createRef=function(){return{current:null}},b.forwardRef=function(n){return{$$typeof:d,render:n}},b.isValidElement=q,b.lazy=function(n){return{$$typeof:y,_payload:{_status:-1,_result:n},_init:Rs}},b.memo=function(n,c){return{$$typeof:o,type:n,compare:c===void 0?null:c}},b.startTransition=function(n){var c=C.T,m={};C.T=m;try{var g=n(),S=C.S;S!==null&&S(m,g),typeof g=="object"&&g!==null&&typeof g.then=="function"&&g.then(Os,Fe)}catch(F){Fe(F)}finally{C.T=c}},b.unstable_useCacheRefresh=function(){return C.H.useCacheRefresh()},b.use=function(n){return C.H.use(n)},b.useActionState=function(n,c,m){return C.H.useActionState(n,c,m)},b.useCallback=function(n,c){return C.H.useCallback(n,c)},b.useContext=function(n){return C.H.useContext(n)},b.useDebugValue=function(){},b.useDeferredValue=function(n,c){return C.H.useDeferredValue(n,c)},b.useEffect=function(n,c,m){var g=C.H;if(typeof m=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return g.useEffect(n,c)},b.useId=function(){return C.H.useId()},b.useImperativeHandle=function(n,c,m){return C.H.useImperativeHandle(n,c,m)},b.useInsertionEffect=function(n,c){return C.H.useInsertionEffect(n,c)},b.useLayoutEffect=function(n,c){return C.H.useLayoutEffect(n,c)},b.useMemo=function(n,c){return C.H.useMemo(n,c)},b.useOptimistic=function(n,c){return C.H.useOptimistic(n,c)},b.useReducer=function(n,c,m){return C.H.useReducer(n,c,m)},b.useRef=function(n){return C.H.useRef(n)},b.useState=function(n){return C.H.useState(n)},b.useSyncExternalStore=function(n,c,m){return C.H.useSyncExternalStore(n,c,m)},b.useTransition=function(){return C.H.useTransition()},b.version="19.1.0",b}var Ie;function is(){return Ie||(Ie=1,he.exports=Ts()),he.exports}var Q=is();const As=Ss(Q),hr=Cs({__proto__:null,default:As},[Q]);var le={exports:{}},U={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qe;function Ms(){if(qe)return U;qe=1;var e=is();function t(h){var o="https://react.dev/errors/"+h;if(1<arguments.length){o+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)o+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+h+"; visit "+o+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(t(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},u=Symbol.for("react.portal");function a(h,o,y){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:v==null?null:""+v,children:h,containerInfo:o,implementation:y}}var f=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function d(h,o){if(h==="font")return"";if(typeof o=="string")return o==="use-credentials"?o:""}return U.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,U.createPortal=function(h,o){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!o||o.nodeType!==1&&o.nodeType!==9&&o.nodeType!==11)throw Error(t(299));return a(h,o,null,y)},U.flushSync=function(h){var o=f.T,y=r.p;try{if(f.T=null,r.p=2,h)return h()}finally{f.T=o,r.p=y,r.d.f()}},U.preconnect=function(h,o){typeof h=="string"&&(o?(o=o.crossOrigin,o=typeof o=="string"?o==="use-credentials"?o:"":void 0):o=null,r.d.C(h,o))},U.prefetchDNS=function(h){typeof h=="string"&&r.d.D(h)},U.preinit=function(h,o){if(typeof h=="string"&&o&&typeof o.as=="string"){var y=o.as,v=d(y,o.crossOrigin),j=typeof o.integrity=="string"?o.integrity:void 0,P=typeof o.fetchPriority=="string"?o.fetchPriority:void 0;y==="style"?r.d.S(h,typeof o.precedence=="string"?o.precedence:void 0,{crossOrigin:v,integrity:j,fetchPriority:P}):y==="script"&&r.d.X(h,{crossOrigin:v,integrity:j,fetchPriority:P,nonce:typeof o.nonce=="string"?o.nonce:void 0})}},U.preinitModule=function(h,o){if(typeof h=="string")if(typeof o=="object"&&o!==null){if(o.as==null||o.as==="script"){var y=d(o.as,o.crossOrigin);r.d.M(h,{crossOrigin:y,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0})}}else o==null&&r.d.M(h)},U.preload=function(h,o){if(typeof h=="string"&&typeof o=="object"&&o!==null&&typeof o.as=="string"){var y=o.as,v=d(y,o.crossOrigin);r.d.L(h,y,{crossOrigin:v,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0,type:typeof o.type=="string"?o.type:void 0,fetchPriority:typeof o.fetchPriority=="string"?o.fetchPriority:void 0,referrerPolicy:typeof o.referrerPolicy=="string"?o.referrerPolicy:void 0,imageSrcSet:typeof o.imageSrcSet=="string"?o.imageSrcSet:void 0,imageSizes:typeof o.imageSizes=="string"?o.imageSizes:void 0,media:typeof o.media=="string"?o.media:void 0})}},U.preloadModule=function(h,o){if(typeof h=="string")if(o){var y=d(o.as,o.crossOrigin);r.d.m(h,{as:typeof o.as=="string"&&o.as!=="script"?o.as:void 0,crossOrigin:y,integrity:typeof o.integrity=="string"?o.integrity:void 0})}else r.d.m(h)},U.requestFormReset=function(h){r.d.r(h)},U.unstable_batchedUpdates=function(h,o){return h(o)},U.useFormState=function(h,o,y){return f.H.useFormState(h,o,y)},U.useFormStatus=function(){return f.H.useHostTransitionStatus()},U.version="19.1.0",U}var Ue;function lr(){if(Ue)return le.exports;Ue=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch{}}return e(),le.exports=Ms(),le.exports}var Bt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Mt=typeof window>"u"||"Deno"in globalThis;function $(){}function Fs(e,t){return typeof e=="function"?e(t):e}function de(e){return typeof e=="number"&&e>=0&&e!==1/0}function ns(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Ot(e,t){return typeof e=="function"?e(t):e}function X(e,t){return typeof e=="function"?e(t):e}function Le(e,t){const{type:s="all",exact:r,fetchStatus:u,predicate:a,queryKey:f,stale:d}=e;if(f){if(r){if(t.queryHash!==we(f,t.options))return!1}else if(!Jt(t.queryKey,f))return!1}if(s!=="all"){const h=t.isActive();if(s==="active"&&!h||s==="inactive"&&h)return!1}return!(typeof d=="boolean"&&t.isStale()!==d||u&&u!==t.state.fetchStatus||a&&!a(t))}function ke(e,t){const{exact:s,status:r,predicate:u,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(Ft(t.options.mutationKey)!==Ft(a))return!1}else if(!Jt(t.options.mutationKey,a))return!1}return!(r&&t.state.status!==r||u&&!u(t))}function we(e,t){return(t?.queryKeyHashFn||Ft)(e)}function Ft(e){return JSON.stringify(e,(t,s)=>pe(s)?Object.keys(s).sort().reduce((r,u)=>(r[u]=s[u],r),{}):s)}function Jt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(s=>Jt(e[s],t[s])):!1}function os(e,t){if(e===t)return e;const s=He(e)&&He(t);if(s||pe(e)&&pe(t)){const r=s?e:Object.keys(e),u=r.length,a=s?t:Object.keys(t),f=a.length,d=s?[]:{},h=new Set(r);let o=0;for(let y=0;y<f;y++){const v=s?y:a[y];(!s&&h.has(v)||s)&&e[v]===void 0&&t[v]===void 0?(d[v]=void 0,o++):(d[v]=os(e[v],t[v]),d[v]===e[v]&&e[v]!==void 0&&o++)}return u===f&&o===u?e:d}return t}function oe(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function He(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function pe(e){if(!Ne(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!Ne(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Ne(e){return Object.prototype.toString.call(e)==="[object Object]"}function Ds(e){return new Promise(t=>{setTimeout(t,e)})}function ye(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?os(e,t):t}function xs(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function Qs(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var Pe=Symbol();function us(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:!e.queryFn||e.queryFn===Pe?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function as(e,t){return typeof e=="function"?e(...t):!!e}var Et,lt,Qt,ze,js=(ze=class extends Bt{constructor(){super();p(this,Et);p(this,lt);p(this,Qt);l(this,Qt,t=>{if(!Mt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){i(this,lt)||this.setEventListener(i(this,Qt))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,lt))==null||t.call(this),l(this,lt,void 0))}setEventListener(t){var s;l(this,Qt,t),(s=i(this,lt))==null||s.call(this),l(this,lt,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){i(this,Et)!==t&&(l(this,Et,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){return typeof i(this,Et)=="boolean"?i(this,Et):globalThis.document?.visibilityState!=="hidden"}},Et=new WeakMap,lt=new WeakMap,Qt=new WeakMap,ze),Te=new js,jt,ft,It,We,Is=(We=class extends Bt{constructor(){super();p(this,jt,!0);p(this,ft);p(this,It);l(this,It,t=>{if(!Mt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){i(this,ft)||this.setEventListener(i(this,It))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,ft))==null||t.call(this),l(this,ft,void 0))}setEventListener(t){var s;l(this,It,t),(s=i(this,ft))==null||s.call(this),l(this,ft,t(this.setOnline.bind(this)))}setOnline(t){i(this,jt)!==t&&(l(this,jt,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return i(this,jt)}},jt=new WeakMap,ft=new WeakMap,It=new WeakMap,We),ue=new Is;function ve(){let e,t;const s=new Promise((u,a)=>{e=u,t=a});s.status="pending",s.catch(()=>{});function r(u){Object.assign(s,u),delete s.resolve,delete s.reject}return s.resolve=u=>{r({status:"fulfilled",value:u}),e(u)},s.reject=u=>{r({status:"rejected",reason:u}),t(u)},s}function qs(e){return Math.min(1e3*2**e,3e4)}function cs(e){return(e??"online")==="online"?ue.isOnline():!0}var hs=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function fe(e){return e instanceof hs}function ls(e){let t=!1,s=0,r=!1,u;const a=ve(),f=w=>{r||(j(new hs(w)),e.abort?.())},d=()=>{t=!0},h=()=>{t=!1},o=()=>Te.isFocused()&&(e.networkMode==="always"||ue.isOnline())&&e.canRun(),y=()=>cs(e.networkMode)&&e.canRun(),v=w=>{r||(r=!0,e.onSuccess?.(w),u?.(),a.resolve(w))},j=w=>{r||(r=!0,e.onError?.(w),u?.(),a.reject(w))},P=()=>new Promise(w=>{u=T=>{(r||o())&&w(T)},e.onPause?.()}).then(()=>{u=void 0,r||e.onContinue?.()}),M=()=>{if(r)return;let w;const T=s===0?e.initialPromise:void 0;try{w=T??e.fn()}catch(I){w=Promise.reject(I)}Promise.resolve(w).then(v).catch(I=>{if(r)return;const k=e.retry??(Mt?0:3),Y=e.retryDelay??qs,W=typeof Y=="function"?Y(s,I):Y,C=k===!0||typeof k=="number"&&s<k||typeof k=="function"&&k(s,I);if(t||!C){j(I);return}s++,e.onFail?.(s,I),Ds(W).then(()=>o()?void 0:P()).then(()=>{t?j(I):M()})})};return{promise:a,cancel:f,continue:()=>(u?.(),a),cancelRetry:d,continueRetry:h,canStart:y,start:()=>(y()?M():P().then(M),a)}}var Us=e=>setTimeout(e,0);function Ls(){let e=[],t=0,s=d=>{d()},r=d=>{d()},u=Us;const a=d=>{t?e.push(d):u(()=>{s(d)})},f=()=>{const d=e;e=[],d.length&&u(()=>{r(()=>{d.forEach(h=>{s(h)})})})};return{batch:d=>{let h;t++;try{h=d()}finally{t--,t||f()}return h},batchCalls:d=>(...h)=>{a(()=>{d(...h)})},schedule:a,setNotifyFunction:d=>{s=d},setBatchNotifyFunction:d=>{r=d},setScheduler:d=>{u=d}}}var x=Ls(),Ct,Je,fs=(Je=class{constructor(){p(this,Ct)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),de(this.gcTime)&&l(this,Ct,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Mt?1/0:5*60*1e3))}clearGcTimeout(){i(this,Ct)&&(clearTimeout(i(this,Ct)),l(this,Ct,void 0))}},Ct=new WeakMap,Je),qt,Ut,z,St,L,Vt,_t,J,rt,Ve,ks=(Ve=class extends fs{constructor(t){super();p(this,J);p(this,qt);p(this,Ut);p(this,z);p(this,St);p(this,L);p(this,Vt);p(this,_t);l(this,_t,!1),l(this,Vt,t.defaultOptions),this.setOptions(t.options),this.observers=[],l(this,St,t.client),l(this,z,i(this,St).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,l(this,qt,Hs(this.options)),this.state=t.state??i(this,qt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return i(this,L)?.promise}setOptions(t){this.options={...i(this,Vt),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,z).remove(this)}setData(t,s){const r=ye(this.state.data,t,this.options);return R(this,J,rt).call(this,{data:r,type:"success",dataUpdatedAt:s?.updatedAt,manual:s?.manual}),r}setState(t,s){R(this,J,rt).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){const s=i(this,L)?.promise;return i(this,L)?.cancel(t),s?s.then($).catch($):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,qt))}isActive(){return this.observers.some(t=>X(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Pe||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>Ot(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!ns(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(s=>s.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),i(this,L)?.continue()}onOnline(){this.observers.find(s=>s.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),i(this,L)?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),i(this,z).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(i(this,L)&&(i(this,_t)?i(this,L).cancel({revert:!0}):i(this,L).cancelRetry()),this.scheduleGc()),i(this,z).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||R(this,J,rt).call(this,{type:"invalidate"})}fetch(t,s){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&s?.cancelRefetch)this.cancel({silent:!0});else if(i(this,L))return i(this,L).continueRetry(),i(this,L).promise}if(t&&this.setOptions(t),!this.options.queryFn){const o=this.observers.find(y=>y.options.queryFn);o&&this.setOptions(o.options)}const r=new AbortController,u=o=>{Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(l(this,_t,!0),r.signal)})},a=()=>{const o=us(this.options,s),v=(()=>{const j={client:i(this,St),queryKey:this.queryKey,meta:this.meta};return u(j),j})();return l(this,_t,!1),this.options.persister?this.options.persister(o,v,this):o(v)},d=(()=>{const o={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:i(this,St),state:this.state,fetchFn:a};return u(o),o})();this.options.behavior?.onFetch(d,this),l(this,Ut,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==d.fetchOptions?.meta)&&R(this,J,rt).call(this,{type:"fetch",meta:d.fetchOptions?.meta});const h=o=>{fe(o)&&o.silent||R(this,J,rt).call(this,{type:"error",error:o}),fe(o)||(i(this,z).config.onError?.(o,this),i(this,z).config.onSettled?.(this.state.data,o,this)),this.scheduleGc()};return l(this,L,ls({initialPromise:s?.initialPromise,fn:d.fetchFn,abort:r.abort.bind(r),onSuccess:o=>{if(o===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(o)}catch(y){h(y);return}i(this,z).config.onSuccess?.(o,this),i(this,z).config.onSettled?.(o,this.state.error,this),this.scheduleGc()},onError:h,onFail:(o,y)=>{R(this,J,rt).call(this,{type:"failed",failureCount:o,error:y})},onPause:()=>{R(this,J,rt).call(this,{type:"pause"})},onContinue:()=>{R(this,J,rt).call(this,{type:"continue"})},retry:d.options.retry,retryDelay:d.options.retryDelay,networkMode:d.options.networkMode,canRun:()=>!0})),i(this,L).start()}},qt=new WeakMap,Ut=new WeakMap,z=new WeakMap,St=new WeakMap,L=new WeakMap,Vt=new WeakMap,_t=new WeakMap,J=new WeakSet,rt=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...ds(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const u=t.error;return fe(u)&&u.revert&&i(this,Ut)?{...i(this,Ut),fetchStatus:"idle"}:{...r,error:u,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:u,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),x.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),i(this,z).notify({query:this,type:"updated",action:t})})},Ve);function ds(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:cs(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Hs(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var Z,Xe,Ns=(Xe=class extends Bt{constructor(t={}){super();p(this,Z);this.config=t,l(this,Z,new Map)}build(t,s,r){const u=s.queryKey,a=s.queryHash??we(u,s);let f=this.get(a);return f||(f=new ks({client:t,queryKey:u,queryHash:a,options:t.defaultQueryOptions(s),state:r,defaultOptions:t.getQueryDefaults(u)}),this.add(f)),f}add(t){i(this,Z).has(t.queryHash)||(i(this,Z).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=i(this,Z).get(t.queryHash);s&&(t.destroy(),s===t&&i(this,Z).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){x.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return i(this,Z).get(t)}getAll(){return[...i(this,Z).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Le(s,r))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(r=>Le(t,r)):s}notify(t){x.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){x.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){x.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Z=new WeakMap,Xe),tt,H,wt,et,ht,Ze,$s=(Ze=class extends fs{constructor(t){super();p(this,et);p(this,tt);p(this,H);p(this,wt);this.mutationId=t.mutationId,l(this,H,t.mutationCache),l(this,tt,[]),this.state=t.state||ps(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){i(this,tt).includes(t)||(i(this,tt).push(t),this.clearGcTimeout(),i(this,H).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){l(this,tt,i(this,tt).filter(s=>s!==t)),this.scheduleGc(),i(this,H).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){i(this,tt).length||(this.state.status==="pending"?this.scheduleGc():i(this,H).remove(this))}continue(){return i(this,wt)?.continue()??this.execute(this.state.variables)}async execute(t){const s=()=>{R(this,et,ht).call(this,{type:"continue"})};l(this,wt,ls({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(a,f)=>{R(this,et,ht).call(this,{type:"failed",failureCount:a,error:f})},onPause:()=>{R(this,et,ht).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,H).canRun(this)}));const r=this.state.status==="pending",u=!i(this,wt).canStart();try{if(r)s();else{R(this,et,ht).call(this,{type:"pending",variables:t,isPaused:u}),await i(this,H).config.onMutate?.(t,this);const f=await this.options.onMutate?.(t);f!==this.state.context&&R(this,et,ht).call(this,{type:"pending",context:f,variables:t,isPaused:u})}const a=await i(this,wt).start();return await i(this,H).config.onSuccess?.(a,t,this.state.context,this),await this.options.onSuccess?.(a,t,this.state.context),await i(this,H).config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,t,this.state.context),R(this,et,ht).call(this,{type:"success",data:a}),a}catch(a){try{throw await i(this,H).config.onError?.(a,t,this.state.context,this),await this.options.onError?.(a,t,this.state.context),await i(this,H).config.onSettled?.(void 0,a,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,a,t,this.state.context),a}finally{R(this,et,ht).call(this,{type:"error",error:a})}}finally{i(this,H).runNext(this)}}},tt=new WeakMap,H=new WeakMap,wt=new WeakMap,et=new WeakSet,ht=function(t){const s=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),x.batch(()=>{i(this,tt).forEach(r=>{r.onMutationUpdate(t)}),i(this,H).notify({mutation:this,type:"updated",action:t})})},Ze);function ps(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var it,V,Xt,ts,Ks=(ts=class extends Bt{constructor(t={}){super();p(this,it);p(this,V);p(this,Xt);this.config=t,l(this,it,new Set),l(this,V,new Map),l(this,Xt,0)}build(t,s,r){const u=new $s({mutationCache:this,mutationId:++re(this,Xt)._,options:t.defaultMutationOptions(s),state:r});return this.add(u),u}add(t){i(this,it).add(t);const s=ie(t);if(typeof s=="string"){const r=i(this,V).get(s);r?r.push(t):i(this,V).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(i(this,it).delete(t)){const s=ie(t);if(typeof s=="string"){const r=i(this,V).get(s);if(r)if(r.length>1){const u=r.indexOf(t);u!==-1&&r.splice(u,1)}else r[0]===t&&i(this,V).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=ie(t);if(typeof s=="string"){const u=i(this,V).get(s)?.find(a=>a.state.status==="pending");return!u||u===t}else return!0}runNext(t){const s=ie(t);return typeof s=="string"?i(this,V).get(s)?.find(u=>u!==t&&u.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){x.batch(()=>{i(this,it).forEach(t=>{this.notify({type:"removed",mutation:t})}),i(this,it).clear(),i(this,V).clear()})}getAll(){return Array.from(i(this,it))}find(t){const s={exact:!0,...t};return this.getAll().find(r=>ke(s,r))}findAll(t={}){return this.getAll().filter(s=>ke(t,s))}notify(t){x.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return x.batch(()=>Promise.all(t.map(s=>s.continue().catch($))))}},it=new WeakMap,V=new WeakMap,Xt=new WeakMap,ts);function ie(e){return e.options.scope?.id}function $e(e){return{onFetch:(t,s)=>{const r=t.options,u=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],f=t.state.data?.pageParams||[];let d={pages:[],pageParams:[]},h=0;const o=async()=>{let y=!1;const v=M=>{Object.defineProperty(M,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},j=us(t.options,t.fetchOptions),P=async(M,w,T)=>{if(y)return Promise.reject();if(w==null&&M.pages.length)return Promise.resolve(M);const k=(()=>{const ut={client:t.client,queryKey:t.queryKey,pageParam:w,direction:T?"backward":"forward",meta:t.options.meta};return v(ut),ut})(),Y=await j(k),{maxPages:W}=t.options,C=T?Qs:xs;return{pages:C(M.pages,Y,W),pageParams:C(M.pageParams,w,W)}};if(u&&a.length){const M=u==="backward",w=M?Gs:Ke,T={pages:a,pageParams:f},I=w(r,T);d=await P(T,I,M)}else{const M=e??a.length;do{const w=h===0?f[0]??r.initialPageParam:Ke(r,d);if(h>0&&w==null)break;d=await P(d,w),h++}while(h<M)}return d};t.options.persister?t.fetchFn=()=>t.options.persister?.(o,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=o}}}function Ke(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function Gs(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}var A,dt,pt,Lt,kt,yt,Ht,Nt,es,fr=(es=class{constructor(e={}){p(this,A);p(this,dt);p(this,pt);p(this,Lt);p(this,kt);p(this,yt);p(this,Ht);p(this,Nt);l(this,A,e.queryCache||new Ns),l(this,dt,e.mutationCache||new Ks),l(this,pt,e.defaultOptions||{}),l(this,Lt,new Map),l(this,kt,new Map),l(this,yt,0)}mount(){re(this,yt)._++,i(this,yt)===1&&(l(this,Ht,Te.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,A).onFocus())})),l(this,Nt,ue.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,A).onOnline())})))}unmount(){var e,t;re(this,yt)._--,i(this,yt)===0&&((e=i(this,Ht))==null||e.call(this),l(this,Ht,void 0),(t=i(this,Nt))==null||t.call(this),l(this,Nt,void 0))}isFetching(e){return i(this,A).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return i(this,dt).findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return i(this,A).get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=i(this,A).build(this,t),r=s.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(Ot(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return i(this,A).findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),a=i(this,A).get(r.queryHash)?.state.data,f=Fs(t,a);if(f!==void 0)return i(this,A).build(this,r).setData(f,{...s,manual:!0})}setQueriesData(e,t,s){return x.batch(()=>i(this,A).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return i(this,A).get(t.queryHash)?.state}removeQueries(e){const t=i(this,A);x.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=i(this,A);return x.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},r=x.batch(()=>i(this,A).findAll(e).map(u=>u.cancel(s)));return Promise.all(r).then($).catch($)}invalidateQueries(e,t={}){return x.batch(()=>(i(this,A).findAll(e).forEach(s=>{s.invalidate()}),e?.refetchType==="none"?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},r=x.batch(()=>i(this,A).findAll(e).filter(u=>!u.isDisabled()&&!u.isStatic()).map(u=>{let a=u.fetch(void 0,s);return s.throwOnError||(a=a.catch($)),u.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(r).then($)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=i(this,A).build(this,t);return s.isStaleByTime(Ot(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then($).catch($)}fetchInfiniteQuery(e){return e.behavior=$e(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then($).catch($)}ensureInfiniteQueryData(e){return e.behavior=$e(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return ue.isOnline()?i(this,dt).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,A)}getMutationCache(){return i(this,dt)}getDefaultOptions(){return i(this,pt)}setDefaultOptions(e){l(this,pt,e)}setQueryDefaults(e,t){i(this,Lt).set(Ft(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...i(this,Lt).values()],s={};return t.forEach(r=>{Jt(e,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(e,t){i(this,kt).set(Ft(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...i(this,kt).values()],s={};return t.forEach(r=>{Jt(e,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...i(this,pt).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=we(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Pe&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...i(this,pt).mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){i(this,A).clear(),i(this,dt).clear()}},A=new WeakMap,dt=new WeakMap,pt=new WeakMap,Lt=new WeakMap,kt=new WeakMap,yt=new WeakMap,Ht=new WeakMap,Nt=new WeakMap,es),K,E,Zt,N,Pt,$t,vt,mt,te,Kt,Gt,Tt,At,gt,Yt,_,Wt,me,ge,be,Re,Oe,Ee,Ce,ys,ss,Ys=(ss=class extends Bt{constructor(t,s){super();p(this,_);p(this,K);p(this,E);p(this,Zt);p(this,N);p(this,Pt);p(this,$t);p(this,vt);p(this,mt);p(this,te);p(this,Kt);p(this,Gt);p(this,Tt);p(this,At);p(this,gt);p(this,Yt,new Set);this.options=s,l(this,K,t),l(this,mt,null),l(this,vt,ve()),this.options.experimental_prefetchInRender||i(this,vt).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,E).addObserver(this),Ge(i(this,E),this.options)?R(this,_,Wt).call(this):this.updateResult(),R(this,_,Re).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Se(i(this,E),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Se(i(this,E),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,R(this,_,Oe).call(this),R(this,_,Ee).call(this),i(this,E).removeObserver(this)}setOptions(t){const s=this.options,r=i(this,E);if(this.options=i(this,K).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof X(this.options.enabled,i(this,E))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");R(this,_,Ce).call(this),i(this,E).setOptions(this.options),s._defaulted&&!oe(this.options,s)&&i(this,K).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,E),observer:this});const u=this.hasListeners();u&&Ye(i(this,E),r,this.options,s)&&R(this,_,Wt).call(this),this.updateResult(),u&&(i(this,E)!==r||X(this.options.enabled,i(this,E))!==X(s.enabled,i(this,E))||Ot(this.options.staleTime,i(this,E))!==Ot(s.staleTime,i(this,E)))&&R(this,_,me).call(this);const a=R(this,_,ge).call(this);u&&(i(this,E)!==r||X(this.options.enabled,i(this,E))!==X(s.enabled,i(this,E))||a!==i(this,gt))&&R(this,_,be).call(this,a)}getOptimisticResult(t){const s=i(this,K).getQueryCache().build(i(this,K),t),r=this.createResult(s,t);return zs(this,r)&&(l(this,N,r),l(this,$t,this.options),l(this,Pt,i(this,E).state)),r}getCurrentResult(){return i(this,N)}trackResult(t,s){return new Proxy(t,{get:(r,u)=>(this.trackProp(u),s?.(u),Reflect.get(r,u))})}trackProp(t){i(this,Yt).add(t)}getCurrentQuery(){return i(this,E)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,K).defaultQueryOptions(t),r=i(this,K).getQueryCache().build(i(this,K),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return R(this,_,Wt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,N)))}createResult(t,s){const r=i(this,E),u=this.options,a=i(this,N),f=i(this,Pt),d=i(this,$t),o=t!==r?t.state:i(this,Zt),{state:y}=t;let v={...y},j=!1,P;if(s._optimisticResults){const q=this.hasListeners(),Dt=!q&&Ge(t,s),st=q&&Ye(t,r,s,u);(Dt||st)&&(v={...v,...ds(y.data,t.options)}),s._optimisticResults==="isRestoring"&&(v.fetchStatus="idle")}let{error:M,errorUpdatedAt:w,status:T}=v;P=v.data;let I=!1;if(s.placeholderData!==void 0&&P===void 0&&T==="pending"){let q;a?.isPlaceholderData&&s.placeholderData===d?.placeholderData?(q=a.data,I=!0):q=typeof s.placeholderData=="function"?s.placeholderData(i(this,Gt)?.state.data,i(this,Gt)):s.placeholderData,q!==void 0&&(T="success",P=ye(a?.data,q,s),j=!0)}if(s.select&&P!==void 0&&!I)if(a&&P===f?.data&&s.select===i(this,te))P=i(this,Kt);else try{l(this,te,s.select),P=s.select(P),P=ye(a?.data,P,s),l(this,Kt,P),l(this,mt,null)}catch(q){l(this,mt,q)}i(this,mt)&&(M=i(this,mt),P=i(this,Kt),w=Date.now(),T="error");const k=v.fetchStatus==="fetching",Y=T==="pending",W=T==="error",C=Y&&k,ut=P!==void 0,B={status:T,fetchStatus:v.fetchStatus,isPending:Y,isSuccess:T==="success",isError:W,isInitialLoading:C,isLoading:C,data:P,dataUpdatedAt:v.dataUpdatedAt,error:M,errorUpdatedAt:w,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>o.dataUpdateCount||v.errorUpdateCount>o.errorUpdateCount,isFetching:k,isRefetching:k&&!Y,isLoadingError:W&&!ut,isPaused:v.fetchStatus==="paused",isPlaceholderData:j,isRefetchError:W&&ut,isStale:Ae(t,s),refetch:this.refetch,promise:i(this,vt)};if(this.options.experimental_prefetchInRender){const q=at=>{B.status==="error"?at.reject(B.error):B.data!==void 0&&at.resolve(B.data)},Dt=()=>{const at=l(this,vt,B.promise=ve());q(at)},st=i(this,vt);switch(st.status){case"pending":t.queryHash===r.queryHash&&q(st);break;case"fulfilled":(B.status==="error"||B.data!==st.value)&&Dt();break;case"rejected":(B.status!=="error"||B.error!==st.reason)&&Dt();break}}return B}updateResult(){const t=i(this,N),s=this.createResult(i(this,E),this.options);if(l(this,Pt,i(this,E).state),l(this,$t,this.options),i(this,Pt).data!==void 0&&l(this,Gt,i(this,E)),oe(s,t))return;l(this,N,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:u}=this.options,a=typeof u=="function"?u():u;if(a==="all"||!a&&!i(this,Yt).size)return!0;const f=new Set(a??i(this,Yt));return this.options.throwOnError&&f.add("error"),Object.keys(i(this,N)).some(d=>{const h=d;return i(this,N)[h]!==t[h]&&f.has(h)})};R(this,_,ys).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&R(this,_,Re).call(this)}},K=new WeakMap,E=new WeakMap,Zt=new WeakMap,N=new WeakMap,Pt=new WeakMap,$t=new WeakMap,vt=new WeakMap,mt=new WeakMap,te=new WeakMap,Kt=new WeakMap,Gt=new WeakMap,Tt=new WeakMap,At=new WeakMap,gt=new WeakMap,Yt=new WeakMap,_=new WeakSet,Wt=function(t){R(this,_,Ce).call(this);let s=i(this,E).fetch(this.options,t);return t?.throwOnError||(s=s.catch($)),s},me=function(){R(this,_,Oe).call(this);const t=Ot(this.options.staleTime,i(this,E));if(Mt||i(this,N).isStale||!de(t))return;const r=ns(i(this,N).dataUpdatedAt,t)+1;l(this,Tt,setTimeout(()=>{i(this,N).isStale||this.updateResult()},r))},ge=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,E)):this.options.refetchInterval)??!1},be=function(t){R(this,_,Ee).call(this),l(this,gt,t),!(Mt||X(this.options.enabled,i(this,E))===!1||!de(i(this,gt))||i(this,gt)===0)&&l(this,At,setInterval(()=>{(this.options.refetchIntervalInBackground||Te.isFocused())&&R(this,_,Wt).call(this)},i(this,gt)))},Re=function(){R(this,_,me).call(this),R(this,_,be).call(this,R(this,_,ge).call(this))},Oe=function(){i(this,Tt)&&(clearTimeout(i(this,Tt)),l(this,Tt,void 0))},Ee=function(){i(this,At)&&(clearInterval(i(this,At)),l(this,At,void 0))},Ce=function(){const t=i(this,K).getQueryCache().build(i(this,K),this.options);if(t===i(this,E))return;const s=i(this,E);l(this,E,t),l(this,Zt,t.state),this.hasListeners()&&(s?.removeObserver(this),t.addObserver(this))},ys=function(t){x.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,N))}),i(this,K).getQueryCache().notify({query:i(this,E),type:"observerResultsUpdated"})})},ss);function Bs(e,t){return X(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Ge(e,t){return Bs(e,t)||e.state.data!==void 0&&Se(e,t,t.refetchOnMount)}function Se(e,t,s){if(X(t.enabled,e)!==!1&&Ot(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&Ae(e,t)}return!1}function Ye(e,t,s,r){return(e!==t||X(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&Ae(e,s)}function Ae(e,t){return X(t.enabled,e)!==!1&&e.isStaleByTime(Ot(t.staleTime,e))}function zs(e,t){return!oe(e.getCurrentResult(),t)}var bt,Rt,G,nt,ot,ne,_e,rs,Ws=(rs=class extends Bt{constructor(t,s){super();p(this,ot);p(this,bt);p(this,Rt);p(this,G);p(this,nt);l(this,bt,t),this.setOptions(s),this.bindMethods(),R(this,ot,ne).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const s=this.options;this.options=i(this,bt).defaultMutationOptions(t),oe(this.options,s)||i(this,bt).getMutationCache().notify({type:"observerOptionsUpdated",mutation:i(this,G),observer:this}),s?.mutationKey&&this.options.mutationKey&&Ft(s.mutationKey)!==Ft(this.options.mutationKey)?this.reset():i(this,G)?.state.status==="pending"&&i(this,G).setOptions(this.options)}onUnsubscribe(){this.hasListeners()||i(this,G)?.removeObserver(this)}onMutationUpdate(t){R(this,ot,ne).call(this),R(this,ot,_e).call(this,t)}getCurrentResult(){return i(this,Rt)}reset(){i(this,G)?.removeObserver(this),l(this,G,void 0),R(this,ot,ne).call(this),R(this,ot,_e).call(this)}mutate(t,s){return l(this,nt,s),i(this,G)?.removeObserver(this),l(this,G,i(this,bt).getMutationCache().build(i(this,bt),this.options)),i(this,G).addObserver(this),i(this,G).execute(t)}},bt=new WeakMap,Rt=new WeakMap,G=new WeakMap,nt=new WeakMap,ot=new WeakSet,ne=function(){const t=i(this,G)?.state??ps();l(this,Rt,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},_e=function(t){x.batch(()=>{if(i(this,nt)&&this.hasListeners()){const s=i(this,Rt).variables,r=i(this,Rt).context;t?.type==="success"?(i(this,nt).onSuccess?.(t.data,s,r),i(this,nt).onSettled?.(t.data,null,s,r)):t?.type==="error"&&(i(this,nt).onError?.(t.error,s,r),i(this,nt).onSettled?.(void 0,t.error,s,r))}this.listeners.forEach(s=>{s(i(this,Rt))})})},rs),vs=Q.createContext(void 0),ms=e=>{const t=Q.useContext(vs);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},dr=({client:e,children:t})=>(Q.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),Ps.jsx(vs.Provider,{value:e,children:t})),gs=Q.createContext(!1),Js=()=>Q.useContext(gs);gs.Provider;function Vs(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Xs=Q.createContext(Vs()),Zs=()=>Q.useContext(Xs),tr=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},er=e=>{Q.useEffect(()=>{e.clearReset()},[e])},sr=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:u})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(u&&e.data===void 0||as(s,[e.error,r])),rr=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...r)=>t(s(...r)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},ir=(e,t)=>e.isLoading&&e.isFetching&&!t,nr=(e,t)=>e?.suspense&&t.isPending,Be=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function or(e,t,s){const r=Js(),u=Zs(),a=ms(),f=a.defaultQueryOptions(e);a.getDefaultOptions().queries?._experimental_beforeQuery?.(f),f._optimisticResults=r?"isRestoring":"optimistic",rr(f),tr(f,u),er(u);const d=!a.getQueryCache().get(f.queryHash),[h]=Q.useState(()=>new t(a,f)),o=h.getOptimisticResult(f),y=!r&&e.subscribed!==!1;if(Q.useSyncExternalStore(Q.useCallback(v=>{const j=y?h.subscribe(x.batchCalls(v)):$;return h.updateResult(),j},[h,y]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),Q.useEffect(()=>{h.setOptions(f)},[f,h]),nr(f,o))throw Be(f,h,u);if(sr({result:o,errorResetBoundary:u,throwOnError:f.throwOnError,query:a.getQueryCache().get(f.queryHash),suspense:f.suspense}))throw o.error;return a.getDefaultOptions().queries?._experimental_afterQuery?.(f,o),f.experimental_prefetchInRender&&!Mt&&ir(o,r)&&(d?Be(f,h,u):a.getQueryCache().get(f.queryHash)?.promise)?.catch($).finally(()=>{h.updateResult()}),f.notifyOnChangeProps?o:h.trackResult(o)}function pr(e,t){return or(e,Ys)}function yr(e,t){const s=ms(),[r]=Q.useState(()=>new Ws(s,e));Q.useEffect(()=>{r.setOptions(e)},[r,e]);const u=Q.useSyncExternalStore(Q.useCallback(f=>r.subscribe(x.batchCalls(f)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),a=Q.useCallback((f,d)=>{r.mutate(f,d).catch($)},[r]);if(u.error&&as(r.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:a,mutateAsync:u.mutate}}export{As as $,dr as Q,hr as R,Q as a,is as b,cr as c,ar as d,ms as e,pr as f,Ss as g,fr as h,ws as i,Ps as j,lr as r,yr as u};
//# sourceMappingURL=vendor-BVmWhBab.js.map
