using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.JettyRequests;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.JettyRequests;

/// <summary>
/// Repository implementation for JettyRequestItem entity
/// </summary>
public class JettyRequestItemRepository : EfCoreRepository<JettyApprovalDbContext, JettyRequestItem, Guid>, IJettyRequestItemRepository
{
    public JettyRequestItemRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<JettyRequestItem>> GetByJettyRequestIdAsync(Guid jettyRequestId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequestItem>()
            .Where(x => x.JettyRequestId == jettyRequestId)
            .ToListAsync();
    }

    public async Task<List<JettyRequestItem>> GetByTenantNameAsync(string tenantName)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequestItem>()
            .Where(x => x.TenantName != null && x.TenantName.Contains(tenantName))
            .ToListAsync();
    }

    public async Task<List<JettyRequestItem>> GetByStatusAsync(string status)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequestItem>()
            .Where(x => x.Status != null && x.Status.Contains(status))
            .ToListAsync();
    }

    public async Task<List<JettyRequestItem>> GetByItemNameAsync(string itemName)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<JettyRequestItem>()
            .Where(x => x.ItemName != null && x.ItemName.Contains(itemName))
            .ToListAsync();
    }
}