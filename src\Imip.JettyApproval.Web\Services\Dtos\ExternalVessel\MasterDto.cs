using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Web.Services.Dtos.ExternalVessel;

public class AgentDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Type { get; set; }
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? NpwpNo { get; set; }
    public string? BdmSapcode { get; set; }
    public string? TaxCode { get; set; }
    public string? AddressNpwp { get; set; }
    public string? Address { get; set; }
    public string? SapcodeS4 { get; set; }
}


public class BcTypeDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Type { get; set; } = null!;
    public string CreatedBy { get; set; } = null!;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int? TransNo { get; set; }
    public string? TransName { get; set; }
    public string Status { get; set; } = null!;
}


public class BusinessPartnerDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Alias { get; set; }
    public string? Image { get; set; }
    public string? Direction { get; set; }
    public string RegionType { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Tenant { get; set; }
    public string? Npwp1 { get; set; }
    public string? Npwp2 { get; set; }
}


public class CargoDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Alias { get; set; }
    public string? Flag { get; set; }
    public decimal GrossWeight { get; set; }
    public string? Type { get; set; }
    public decimal? LoaQty { get; set; }
}


public class DestinationPortDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Deleted { get; set; } = string.Empty;
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Country { get; set; }
    public string DocType { get; set; } = string.Empty;
}


public class ItemClassificationDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }

    public string Name { get; set; } = null!;

    public string? ReportType { get; set; }

    public string Deleted { get; set; } = null!;

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public long? Category { get; set; }
}


public class JettyDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Alias { get; set; } = string.Empty;
    public decimal Max { get; set; }
    public string Deleted { get; set; } = string.Empty;
    public int CreatedBy { get; set; }
    public int UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Port { get; set; }
}


public class PortOfLoadingDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Deleted { get; set; } = string.Empty;
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Country { get; set; }
    public string DocType { get; set; } = string.Empty;
}


public class PortServiceDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string? ItemCode { get; set; }
    public string? ItemName { get; set; }
    public string Active { get; set; } = null!;
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public string? FrgnName { get; set; }
    public string? SapCodeS4 { get; set; }
}

public class SurveyorDto : AuditedEntityDto<Guid>
{
    public long DocEntry { get; set; }
    public string Name { get; set; } = null!;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public string IsActive { get; set; } = null!;
    public long CreatedBy { get; set; }
    public long? UpdatedBy { get; set; }
}


public class MasterTenantDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = null!;
    public string CreatedBy { get; set; } = null!;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Flags { get; set; }
    public string? FullName { get; set; }
    public string? LetterPerson { get; set; }
    public string? LetterRole { get; set; }
    public string? Npwp { get; set; }
    public string? Address { get; set; }
    public string? Nib { get; set; }
    public string? Phone { get; set; }
    public string Status { get; set; } = null!;
    public string? NoAndDateNotaris { get; set; }
    public string? DescNotaris { get; set; }
    public string? Sapcode { get; set; }
    public string IsExternal { get; set; } = null!;
    public string? Billing { get; set; }
    public decimal? BillingPrice { get; set; }
    public string? EsignUserId { get; set; }
    public string? Token { get; set; }
    public string? SapcodeBdt { get; set; }
    public string? SapcodeUsd { get; set; }
    public string? Coordinate { get; set; }
    public string? Boundaries { get; set; }
    public string? IsTenant { get; set; }
    public string? ChannelId { get; set; }
    public string UsePrivy { get; set; } = null!;
    public string? SapcodeS4 { get; set; }
    public string? Skbpph { get; set; }
    public string? CompanyGroup { get; set; }
    public string? FactoryLocation { get; set; }
    public long? MasterGroupId { get; set; }
}


public class TradingDto : AuditedEntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string IsActive { get; set; } = string.Empty;
    public long CreatedBy { get; set; }
    public long? UpdatedBy { get; set; }
}
