using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// DTO for generating application documents from JettyRequestItem
/// </summary>
public class ApplicationDocumentGenerationDto
{
    /// <summary>
    /// The ID of the JettyRequestItem to generate document for
    /// </summary>
    [Required]
    public Guid JettyRequestItemId { get; set; }

    /// <summary>
    /// Optional specific template ID to use (if not provided, the default Letter template will be used)
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Custom filename for the generated document (without extension)
    /// </summary>
    public string? CustomFilename { get; set; }

    /// <summary>
    /// Whether to generate PDF (true) or keep as DOCX (false). Default is true.
    /// </summary>
    public bool GeneratePdf { get; set; } = true;
}

/// <summary>
/// DTO containing data for application document template placeholders
/// </summary>
public class ApplicationDocumentDataDto
{
    // JettyRequestItem properties
    public string? TenantName { get; set; }
    public string? ItemName { get; set; }
    public decimal Qty { get; set; }
    public string? UoM { get; set; }
    public string? Notes { get; set; }
    public string? Status { get; set; }
    public string? LetterNo { get; set; }
    public DateTime? LetterDate { get; set; }

    // JettyRequest properties
    public int DocNum { get; set; }
    public string? VesselType { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public string? Jetty { get; set; }
    public DateTime? ArrivalDate { get; set; }
    public DateTime? DepartureDate { get; set; }
    public DateTime? AsideDate { get; set; }
    public DateTime? CastOfDate { get; set; }
    public DateTime? PostDate { get; set; }
    public string? Barge { get; set; }
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }

    // Additional computed fields
    public DateTime GeneratedDate { get; set; } = DateTime.Now;
    public string? GeneratedBy { get; set; }
}
