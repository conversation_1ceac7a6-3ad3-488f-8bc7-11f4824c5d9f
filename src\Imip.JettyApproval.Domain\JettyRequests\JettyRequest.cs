using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Entity for storing jetty request information
/// </summary>
public class JettyRequest : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// Document number for the jetty request
    /// </summary>
    public int DocNum { get; set; }

    /// <summary>
    /// Type of vessel
    /// </summary>
    public string? VesselType { get; set; }

    /// <summary>
    /// Reference ID for the request
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Name of the vessel
    /// </summary>
    public string? VesselName { get; set; }

    /// <summary>
    /// Voyage information
    /// </summary>
    public string? Voyage { get; set; }

    /// <summary>
    /// Jetty name/location
    /// </summary>
    public string? Jetty { get; set; }

    /// <summary>
    /// Arrival date and time
    /// </summary>
    public DateTime? ArrivalDate { get; set; }

    /// <summary>
    /// Departure date and time
    /// </summary>
    public DateTime? DepartureDate { get; set; }

    /// <summary>
    /// Aside date and time
    /// </summary>
    public DateTime? AsideDate { get; set; }

    /// <summary>
    /// Cast off date and time
    /// </summary>
    public DateTime? CastOfDate { get; set; }

    /// <summary>
    /// Post date and time
    /// </summary>
    public DateTime? PostDate { get; set; }

    /// <summary>
    /// Barge information
    /// </summary>
    public string? Barge { get; set; }

    /// <summary>
    /// Port of origin
    /// </summary>
    public string? PortOrigin { get; set; }

    /// <summary>
    /// Destination port
    /// </summary>
    public string? DestinationPort { get; set; }

    /// <summary>
    /// Navigation property for jetty request items
    /// </summary>
    public virtual ICollection<JettyRequestItem> Items { get; set; }

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected JettyRequest()
    {
        Items = new List<JettyRequestItem>();
    }

    /// <summary>
    /// Creates a new JettyRequest
    /// </summary>
    public JettyRequest(
        Guid id,
        int docNum,
        string? vesselType = null,
        Guid? referenceId = null,
        string? vesselName = null,
        string? voyage = null,
        string? jetty = null,
        DateTime? arrivalDate = null,
        DateTime? departureDate = null,
        DateTime? asideDate = null,
        DateTime? castOfDate = null,
        DateTime? postDate = null,
        string? barge = null,
        string? portOrigin = null,
        string? destinationPort = null)
        : base(id)
    {
        DocNum = docNum;
        VesselType = vesselType;
        ReferenceId = referenceId;
        VesselName = vesselName;
        Voyage = voyage;
        Jetty = jetty;
        ArrivalDate = arrivalDate;
        DepartureDate = departureDate;
        AsideDate = asideDate;
        CastOfDate = castOfDate;
        PostDate = postDate;
        Barge = barge;
        PortOrigin = portOrigin;
        DestinationPort = destinationPort;
        Items = new List<JettyRequestItem>();
    }

    /// <summary>
    /// Creates a new JettyRequest without ID (for mapping from DTOs)
    /// </summary>
    public JettyRequest(
        int docNum,
        string? vesselType = null,
        Guid? referenceId = null,
        string? vesselName = null,
        string? voyage = null,
        string? jetty = null,
        DateTime? arrivalDate = null,
        DateTime? departureDate = null,
        DateTime? asideDate = null,
        DateTime? castOfDate = null,
        DateTime? postDate = null,
        string? barge = null,
        string? portOrigin = null,
        string? destinationPort = null)
    {
        DocNum = docNum;
        VesselType = vesselType;
        ReferenceId = referenceId;
        VesselName = vesselName;
        Voyage = voyage;
        Jetty = jetty;
        ArrivalDate = arrivalDate;
        DepartureDate = departureDate;
        AsideDate = asideDate;
        CastOfDate = castOfDate;
        PostDate = postDate;
        Barge = barge;
        PortOrigin = portOrigin;
        DestinationPort = destinationPort;
        Items = new List<JettyRequestItem>();
    }

    /// <summary>
    /// Adds an item to the jetty request
    /// </summary>
    public void AddItem(JettyRequestItem item)
    {
        Items.Add(item);
    }

    /// <summary>
    /// Removes an item from the jetty request
    /// </summary>
    public void RemoveItem(JettyRequestItem item)
    {
        Items.Remove(item);
    }
}