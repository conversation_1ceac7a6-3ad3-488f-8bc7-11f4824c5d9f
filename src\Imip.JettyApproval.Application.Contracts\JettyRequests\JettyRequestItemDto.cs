using System;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// DTO for JettyRequestItem entity
/// </summary>
public class JettyRequestItemDto : FullAuditedEntityDto<Guid>
{
    /// <summary>
    /// Foreign key to the parent jetty request
    /// </summary>
    public Guid JettyRequestId { get; set; }

    /// <summary>
    /// Tenant name for the item
    /// </summary>
    public string? TenantName { get; set; }

    /// <summary>
    /// Name of the item
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// Quantity of the item
    /// </summary>
    public decimal Qty { get; set; }

    /// <summary>
    /// Unit of measurement
    /// </summary>
    public string? UoM { get; set; }

    /// <summary>
    /// Additional notes for the item
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Status of the item
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Letter number for the item
    /// </summary>
    public string? LetterNo { get; set; }

    /// <summary>
    /// Letter date for the item (formatted as yyyy-MM-dd)
    /// </summary>
    public string? LetterDate { get; set; }

    /// <summary>
    /// Reference ID for linking to attachments
    /// </summary>
    public Guid? ReferenceId { get; set; }
}