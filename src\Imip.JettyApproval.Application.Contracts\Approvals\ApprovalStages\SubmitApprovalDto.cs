using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// DTO for submitting a document for approval
/// </summary>
public class SubmitApprovalDto
{
    /// <summary>
    /// Document ID (JettyRequestItem ID)
    /// </summary>
    [Required]
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Document type from header (Import, Export, LocalIn, LocalOut)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string DocumentType { get; set; } = string.Empty;

    /// <summary>
    /// Optional notes for the submission
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }
}
