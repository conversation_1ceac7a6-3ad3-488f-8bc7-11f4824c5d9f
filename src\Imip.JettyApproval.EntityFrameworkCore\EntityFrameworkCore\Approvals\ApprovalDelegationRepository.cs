using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Approvals.ApprovalDelegations;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Approvals;

public class ApprovalDelegationRepository : EfCoreRepository<JettyApprovalDbContext, ApprovalDelegation, Guid>, IApprovalDelegationRepository
{
    public ApprovalDelegationRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<ApprovalDelegation>> GetByApproverIdAsync(Guid approverId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalDelegation>()
            .Where(x => x.ApproverId == approverId)
            .ToListAsync();
    }

    public async Task<List<ApprovalDelegation>> GetBySubstituteIdAsync(Guid substituteId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalDelegation>()
            .Where(x => x.SubstituteId == substituteId)
            .ToListAsync();
    }

    public async Task<List<ApprovalDelegation>> GetActiveDelegationsAsync()
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalDelegation>()
            .Where(x => x.IsActive)
            .ToListAsync();
    }

    public async Task<List<ApprovalDelegation>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalDelegation>()
            .Where(x => x.StartDate >= startDate && x.EndDate <= endDate)
            .ToListAsync();
    }
}