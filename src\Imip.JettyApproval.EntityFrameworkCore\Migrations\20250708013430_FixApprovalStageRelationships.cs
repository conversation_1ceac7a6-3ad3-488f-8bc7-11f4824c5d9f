﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class FixApprovalStageRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalStages_ApprovalTemplates_ApprovalTemplateId1",
                table: "ApprovalStages");

            migrationBuilder.DropIndex(
                name: "IX_ApprovalStages_ApprovalTemplateId1",
                table: "ApprovalStages");

            migrationBuilder.DropColumn(
                name: "ApprovalTemplateId1",
                table: "ApprovalStages");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ApprovalTemplateId1",
                table: "ApprovalStages",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApprovalStages_ApprovalTemplateId1",
                table: "ApprovalStages",
                column: "ApprovalTemplateId1");

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalStages_ApprovalTemplates_ApprovalTemplateId1",
                table: "ApprovalStages",
                column: "ApprovalTemplateId1",
                principalTable: "ApprovalTemplates",
                principalColumn: "Id");
        }
    }
}
