using Imip.JettyApproval.JettyRequests;
using Imip.JettyApproval.Mapping.Mappers;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Permissions.Apps;
using Imip.JettyApproval.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.DependencyInjection;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service for JettyRequest entity
/// </summary>
[Authorize(JettyApprovalPermission.PolicyJettyRequest.Default)]
public class JettyRequestAppService :
    CrudAppService<JettyRequest, JettyRequestDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateJettyRequestDto, CreateUpdateJettyRequestDto>,
    IJettyRequestAppService,
    ITransientDependency
{
    private readonly IJettyRequestRepository _jettyRequestRepository;
    private readonly IJettyRequestItemRepository _jettyRequestItemRepository;
    private readonly JettyRequestMapper _mapper;
    private readonly JettyRequestItemMapper _jettyRequestItemMapper;
    private readonly ILogger<JettyRequestAppService> _logger;

    public JettyRequestAppService(
        IJettyRequestRepository jettyRequestRepository,
        IJettyRequestItemRepository jettyRequestItemRepository,
        JettyRequestMapper mapper,
        JettyRequestItemMapper jettyRequestItemMapper,
        ILogger<JettyRequestAppService> logger)
        : base(jettyRequestRepository)
    {
        _jettyRequestRepository = jettyRequestRepository;
        _jettyRequestItemRepository = jettyRequestItemRepository;
        _mapper = mapper;
        _jettyRequestItemMapper = jettyRequestItemMapper;
        _logger = logger;
    }

    // Generate next DocNum for a given postDate (YYMM + 4 digit increment, reset every month)
    public async Task<int> GenerateNextDocNumAsync(DateTime postDate)
    {
        var prefix = postDate.ToString("yyMM");
        var queryable = await _jettyRequestRepository.GetQueryableAsync();
        var maxDocNum = queryable
            .Where(x => x.DocNum.ToString().StartsWith(prefix))
            .OrderByDescending(x => x.DocNum)
            .Select(x => x.DocNum)
            .FirstOrDefault();

        int nextIncrement = 1;
        if (maxDocNum != 0)
        {
            var lastIncrementStr = maxDocNum.ToString().Length > 4 ? maxDocNum.ToString().Substring(4, 4) : "0000";
            if (int.TryParse(lastIncrementStr, out var lastIncrement))
            {
                nextIncrement = lastIncrement + 1;
            }
        }
        var docNum = int.Parse($"{prefix}{nextIncrement.ToString("D4")}");
        return docNum;
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequest.Create)]
    public override async Task<JettyRequestDto> CreateAsync(CreateUpdateJettyRequestDto input)
    {
        await CheckCreatePolicyAsync();

        DateTime dt = input.PostDate.HasValue ? input.PostDate.Value : DateTime.Now;
        input.DocNum = await GenerateNextDocNumAsync(dt);

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        // Insert the main entity first
        await _jettyRequestRepository.InsertAsync(entity, autoSave: false);

        // Handle items if they exist
        if (input.Items != null && input.Items.Any())
        {
            foreach (var itemDto in input.Items)
            {
                // Set the JettyRequestId to link the item to the parent
                itemDto.JettyRequestId = entity.Id;

                var itemEntity = _jettyRequestItemMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                await _jettyRequestItemRepository.InsertAsync(itemEntity, autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Get the entity with items to return complete data
        var entityWithItems = await _jettyRequestRepository.GetWithItemsAsync(entity.Id);
        return _mapper.MapToDto(entityWithItems);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequest.Edit)]
    public override async Task<JettyRequestDto> UpdateAsync(Guid id, CreateUpdateJettyRequestDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _jettyRequestRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _jettyRequestRepository.UpdateAsync(entity, autoSave: false);

        // Handle items update - update existing items and add new ones
        if (input.Items != null)
        {
            // Get existing items
            var existingItems = await _jettyRequestItemRepository.GetByJettyRequestIdAsync(id);

            // Process each item in the input
            for (int i = 0; i < input.Items.Count; i++)
            {
                var itemDto = input.Items[i];
                itemDto.JettyRequestId = id;

                if (i < existingItems.Count)
                {
                    // Update existing item
                    var existingItem = existingItems[i];
                    _jettyRequestItemMapper.MapToEntity(itemDto, existingItem);
                    await _jettyRequestItemRepository.UpdateAsync(existingItem, autoSave: false);
                }
                else
                {
                    // Add new item
                    var newItem = _jettyRequestItemMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                    await _jettyRequestItemRepository.InsertAsync(newItem, autoSave: false);
                }
            }

            // Remove excess existing items if input has fewer items
            for (int i = input.Items.Count; i < existingItems.Count; i++)
            {
                await _jettyRequestItemRepository.DeleteAsync(existingItems[i], autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Get the entity with items to return complete data
        var entityWithItems = await _jettyRequestRepository.GetWithItemsAsync(id);
        return _mapper.MapToDto(entityWithItems);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequest.Delete)]
    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _jettyRequestRepository.GetAsync(id);

        await _jettyRequestRepository.DeleteAsync(entity, autoSave: true);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequest.View)]
    public override async Task<JettyRequestDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _jettyRequestRepository.GetWithItemsAsync(id);
        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequest.View)]
    public override async Task<PagedResultDto<JettyRequestDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _jettyRequestRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<JettyRequestDto>(totalCount, dtos);
    }

    [Authorize(JettyApprovalPermission.PolicyJettyRequest.View)]
    public virtual async Task<PagedResultDto<JettyRequestDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _jettyRequestRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<JettyRequestDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<JettyRequest> ApplyDynamicQuery(IQueryable<JettyRequest> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<JettyRequest>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<JettyRequest>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }

}