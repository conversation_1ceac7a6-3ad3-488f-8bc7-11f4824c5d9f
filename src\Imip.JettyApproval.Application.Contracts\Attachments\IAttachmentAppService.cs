using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Application service interface for attachment operations
/// </summary>
public interface IAttachmentAppService : IApplicationService
{
    /// <summary>
    /// Gets a paged list of all attachments with optional filtering
    /// </summary>
    /// <param name="input">The input DTO with paging and filtering options</param>
    /// <returns>A paged result DTO containing a list of file upload result DTOs</returns>
    Task<PagedResultDto<FileUploadResultDto>> GetAllAsync(GetAttachmentsInput input);

    /// <summary>
    /// Uploads a file
    /// </summary>
    /// <param name="input">The file uploads DTO with metadata</param>
    /// <param name="fileName">The name of the file</param>
    /// <param name="contentType">The content type of the file</param>
    /// <param name="fileBytes">The file content as a byte array</param>
    /// <returns>The file upload result DTO</returns>
    Task<FileUploadResultDto> UploadFileAsync(FileUploadDto input, string fileName, string contentType, byte[] fileBytes);

    /// <summary>
    /// Uploads a file using a single DTO
    /// </summary>
    /// <param name="input">The file upload input DTO with all data including file content</param>
    /// <returns>The file upload result DTO</returns>
    Task<FileUploadResultDto> UploadWithFileContentAsync(FileUploadInputDto input);

    /// <summary>
    /// Downloads a file by ID
    /// </summary>
    /// <param name="id">The file ID</param>
    /// <returns>The file DTO</returns>
    Task<FileDto> DownloadAsync(Guid id);

    /// <summary>
    /// Gets a list of attachments by reference ID and type
    /// </summary>
    /// <param name="referenceId">The reference ID</param>
    /// <param name="referenceType">The reference type</param>
    /// <returns>List of file upload result DTOs</returns>
    Task<List<FileUploadResultDto>> GetByReferenceAsync(Guid referenceId, string referenceType);

    /// <summary>
    /// Deletes a file by ID
    /// </summary>
    /// <param name="id">The file ID</param>
    /// <returns>True if the file was deleted, false otherwise</returns>
    Task<bool> DeleteAsync(Guid id);

    /// <summary>
    /// Downloads multiple files as a zip archive
    /// </summary>
    /// <param name="input">The bulk download DTO</param>
    /// <returns>A byte array containing the zip file</returns>
    Task<byte[]> BulkDownloadAsync(BulkDownloadDto input);

}