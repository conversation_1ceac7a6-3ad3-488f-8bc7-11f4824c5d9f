/**
 * OIDC Token Manager for frontend
 * Follows OIDC standards for seamless token refresh
 */
class OidcTokenManager {
    constructor() {
        this.refreshInterval = null;
        this.refreshTime = 5 * 60 * 1000; // 5 minutes in milliseconds
        this.init();
    }

    init() {
        // Start automatic token refresh
        this.startAutomaticRefresh();
        
        // Add event listeners for user activity
        this.setupActivityListeners();
        
        // Handle page visibility changes
        this.setupVisibilityListener();
    }

    /**
     * Start automatic token refresh following OIDC standards
     */
    startAutomaticRefresh() {
        // Clear existing interval
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // Set up periodic refresh
        this.refreshInterval = setInterval(() => {
            this.refreshTokenSilently();
        }, this.refreshTime);
    }

    /**
     * Silent token refresh (OIDC standard approach)
     */
    async refreshTokenSilently() {
        try {
            const response = await fetch('/api/token/refresh', {
                method: 'GET',
                credentials: 'include', // Include cookies
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                console.log('Silent token refresh successful');
                return true;
            } else if (response.status === 401) {
                console.log('Token refresh failed, redirecting to login');
                this.redirectToLogin();
                return false;
            }
        } catch (error) {
            console.error('Error during silent token refresh:', error);
            return false;
        }
    }

    /**
     * Setup activity listeners to refresh tokens on user activity
     */
    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.onUserActivity();
            }, { passive: true });
        });
    }

    /**
     * Handle user activity - refresh token if needed
     */
    onUserActivity() {
        // Debounce the activity
        if (this.activityTimeout) {
            clearTimeout(this.activityTimeout);
        }

        this.activityTimeout = setTimeout(() => {
            this.refreshTokenSilently();
        }, 1000); // Wait 1 second after last activity
    }

    /**
     * Setup visibility change listener
     */
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // Page became visible, refresh token
                this.refreshTokenSilently();
            }
        });
    }

    /**
     * Redirect to login page
     */
    redirectToLogin() {
        const currentUrl = encodeURIComponent(window.location.href);
        window.location.href = `/Account/Login?returnUrl=${currentUrl}`;
    }

    /**
     * Handle API errors and refresh tokens if needed
     */
    async handleApiError(response) {
        if (response.status === 401) {
            // Try to refresh token
            const refreshSuccess = await this.refreshTokenSilently();
            if (refreshSuccess) {
                // Retry the original request
                return true;
            } else {
                // Redirect to login
                this.redirectToLogin();
                return false;
            }
        }
        return false;
    }

    /**
     * Enhanced fetch with automatic token refresh
     */
    async fetchWithTokenRefresh(url, options = {}) {
        try {
            const response = await fetch(url, {
                ...options,
                credentials: 'include'
            });

            if (response.status === 401) {
                const refreshSuccess = await this.refreshTokenSilently();
                if (refreshSuccess) {
                    // Retry the request
                    return await fetch(url, {
                        ...options,
                        credentials: 'include'
                    });
                } else {
                    this.redirectToLogin();
                    throw new Error('Authentication required');
                }
            }

            return response;
        } catch (error) {
            console.error('Fetch error:', error);
            throw error;
        }
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        if (this.activityTimeout) {
            clearTimeout(this.activityTimeout);
        }
    }
}

// Initialize the OIDC token manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.oidcTokenManager = new OidcTokenManager();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OidcTokenManager;
} 