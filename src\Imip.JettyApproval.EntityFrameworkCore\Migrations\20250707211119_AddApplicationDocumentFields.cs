﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class AddApplicationDocumentFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Barge",
                table: "JettyRequests",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DestinationPort",
                table: "JettyRequests",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Port<PERSON><PERSON>in",
                table: "JettyRequests",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LetterDate",
                table: "JettyRequestItems",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LetterNo",
                table: "JettyRequestItems",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ReferenceId",
                table: "JettyRequestItems",
                type: "uniqueidentifier",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Barge",
                table: "JettyRequests");

            migrationBuilder.DropColumn(
                name: "DestinationPort",
                table: "JettyRequests");

            migrationBuilder.DropColumn(
                name: "PortOrigin",
                table: "JettyRequests");

            migrationBuilder.DropColumn(
                name: "LetterDate",
                table: "JettyRequestItems");

            migrationBuilder.DropColumn(
                name: "LetterNo",
                table: "JettyRequestItems");

            migrationBuilder.DropColumn(
                name: "ReferenceId",
                table: "JettyRequestItems");
        }
    }
}
