using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service interface for JettyRequest entity
/// </summary>
public interface IJettyRequestAppService :
    ICrudAppService<
        JettyRequestDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateJettyRequestDto,
        CreateUpdateJettyRequestDto>
{
    Task<PagedResultDto<JettyRequestDto>> FilterListAsync(QueryParametersDto parameters);
}