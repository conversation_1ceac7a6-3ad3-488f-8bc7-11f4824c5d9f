﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-idjas-config
  namespace: imip-idjas-prod
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  App__SelfUrl: "${PROD_APP_URL}"
  App__ServerRootAddress: "${PROD_APP_URL}"
  App__ClientUrl: "${PROD_CLIENT_URL}"
  App__CorsOrigins: "${PROD_CORS_ORIGINS}"
  App__HealthCheckUrl: "/health-status"
  App__HealthUiCheckUrl: "${PROD_APP_URL}/health-status"
  App__IntranetMode: "true"
  App__UseHttps: "false"
  Seq__ServerUrl: "${SEQ_SERVER_URL}"
  AuthServer__Authority: "${PROD_AUTH_APP_URL}"
  AuthServer__ClientId: "${PROD_AUTH_CLIENT_ID}"
  AuthServer__ClientSecret: "${PROD_AUTH_CLIENT_SECRET}"
  AuthServer__RequireHttpsMetadata: "false"
  OpenIdConnect__Authority: "${PROD_AUTH_APP_URL}"
  OpenIdConnect__ClientId: "${PROD_AUTH_CLIENT_ID}"
  OpenIdConnect__ClientSecret: "${PROD_AUTH_CLIENT_SECRET}"
  OpenIdConnect__RequireHttpsMetadata: "false"
  OpenIdConnect__ResponseType: "code"
  OpenIdConnect__UsePkce: "true"
  OpenIdConnect__SaveTokens: "true"
  OpenIdConnect__GetClaimsFromUserInfoEndpoint: "true"
  OpenIdConnect__Scopes__0: "openid"
  OpenIdConnect__Scopes__1: "profile"
  OpenIdConnect__Scopes__2: "email"
  OpenIdConnect__Scopes__3: "offline_access"
  OpenIdConnect__PostLogoutRedirectUri: "${PROD_APP_URL}/signout-callback-oidc"
  OpenIdConnect__SignedOutRedirectUri: "${PROD_APP_URL}"
  OpenIdConnect__RedirectUri: "${PROD_APP_URL}/signin-oidc"

  ExternalAuth__ApiUrl: "${PROD_EXTERNAL_AUTH_URL}"
  ExternalAuth__Enabled: "true"
  AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
  Redis__IsEnabled: "true"
  Redis__Configuration: "**********:6379"
  # String Encryption Configuration
  StringEncryption__DefaultPassPhrase: "5uOIYyxxaxv32bzK"
  # Blob Storing Configuration
  BlobStoring__Default__Type: "SFTP"
  BlobStoring__Default__SFTP__Host: "**********"
  BlobStoring__Default__SFTP__Port: "22"
  BlobStoring__Default__SFTP__UserName: "ekbdev"
  BlobStoring__Default__SFTP__Password: "ekbdev#2024"
  BlobStoring__Default__SFTP__PrivateKeyPath: ""
  BlobStoring__Default__SFTP__PrivateKeyPassphrase: ""
  BlobStoring__Default__SFTP__BaseDirectory: "/ekb"
  BlobStoring__Default__SFTP__ConnectionTimeout: "30000"
  BlobStoring__Default__SFTP__OperationTimeout: "60000"
  BlobStoring__Default__SFTP__BufferSize: "4096"
  BlobStoring__Default__SFTP__CreateDirectoryIfNotExists: "true"
