using System;
using System.Collections.Generic;
using System.Linq;
using Imip.JettyApproval.JettyRequests;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for JettyRequest entity
/// </summary>
[Mapper]
public partial class JettyRequestMapper : IMapperlyMapper
{
    private readonly JettyRequestItemMapper _itemMapper;

    public JettyRequestMapper(JettyRequestItemMapper itemMapper)
    {
        _itemMapper = itemMapper;
    }

    // Entity to DTO mapping
    [MapProperty(nameof(JettyRequest.Id), nameof(JettyRequestDto.Id))]
    [MapperIgnoreSource(nameof(JettyRequest.IsDeleted))]
    [MapperIgnoreSource(nameof(JettyRequest.DeleterId))]
    [MapperIgnoreSource(nameof(JettyRequest.DeletionTime))]
    [MapperIgnoreSource(nameof(JettyRequest.LastModificationTime))]
    [MapperIgnoreSource(nameof(JettyRequest.LastModifierId))]
    [MapperIgnoreSource(nameof(JettyRequest.CreationTime))]
    [MapperIgnoreSource(nameof(JettyRequest.CreatorId))]
    [MapperIgnoreSource(nameof(JettyRequest.ExtraProperties))]
    [MapperIgnoreSource(nameof(JettyRequest.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(JettyRequest.Items))] // Handle Items mapping manually
    [MapperIgnoreTarget(nameof(JettyRequestDto.Items))] // Handle Items mapping manually
    private partial JettyRequestDto MapToDtoInternal(JettyRequest entity);

    // Public method with custom Items mapping
    public JettyRequestDto MapToDto(JettyRequest entity)
    {
        var dto = MapToDtoInternal(entity);
        // Map items using our custom item mapper to ensure proper date formatting
        dto.Items = entity.Items?.Select(_itemMapper.MapToDto).ToList() ?? new List<JettyRequestItemDto>();
        return dto;
    }

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(JettyRequest.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(JettyRequest.Items))] // Don't map items - they're handled separately
    public partial void MapToEntity(CreateUpdateJettyRequestDto dto, JettyRequest entity);

    // Custom mapping methods for complex scenarios
    public JettyRequest CreateEntityWithId(CreateUpdateJettyRequestDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (JettyRequest)Activator.CreateInstance(typeof(JettyRequest), true)!;

        // Set the ID using reflection
        var idProperty = typeof(JettyRequest).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs using custom mapping
    public List<JettyRequestDto> MapToDtoList(List<JettyRequest> entities)
    {
        return entities.Select(MapToDto).ToList();
    }

    // Map IEnumerable for LINQ scenarios using custom mapping
    public IEnumerable<JettyRequestDto> MapToDtoEnumerable(IEnumerable<JettyRequest> entities)
    {
        return entities.Select(MapToDto);
    }
}