using System;
using System.Collections.Generic;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for ApprovalCriteria entity
/// </summary>
[Mapper]
public partial class ApprovalCriteriaMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ApprovalCriteria.Id), nameof(ApprovalCriteriaDto.Id))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.IsDeleted))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.DeleterId))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.DeletionTime))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.LastModificationTime))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.LastModifierId))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.CreationTime))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.CreatorId))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.ExtraProperties))]
    [MapperIgnoreSource(nameof(ApprovalCriteria.ConcurrencyStamp))]
    public partial ApprovalCriteriaDto MapToDto(ApprovalCriteria entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ApprovalCriteria.Id))] // Don't change existing Id
    [MapperIgnoreSource(nameof(CreateUpdateApprovalCriteriaDto.ApprovalId))] // Ignore ApprovalId from DTO
    public partial void MapToEntity(CreateUpdateApprovalCriteriaDto dto, ApprovalCriteria entity);

    // Custom mapping methods for complex scenarios
    public ApprovalCriteria CreateEntityWithId(CreateUpdateApprovalCriteriaDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ApprovalCriteria)Activator.CreateInstance(typeof(ApprovalCriteria), true)!;

        // Set the ID using reflection since it's protected
        var idProperty = typeof(ApprovalCriteria).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ApprovalCriteriaDto> MapToDtoList(List<ApprovalCriteria> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ApprovalCriteriaDto> MapToDtoEnumerable(IEnumerable<ApprovalCriteria> entities);
}