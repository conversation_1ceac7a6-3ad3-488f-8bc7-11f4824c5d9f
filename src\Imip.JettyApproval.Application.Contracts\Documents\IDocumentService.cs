using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// Interface for document service
/// </summary>
public interface IDocumentService : IApplicationService
{
    /// <summary>
    /// Gets all document templates
    /// </summary>
    Task<List<DocumentTemplateDto>> GetAllTemplatesAsync();

    /// <summary>
    /// Gets document templates by document type
    /// </summary>
    Task<List<DocumentTemplateDto>> GetTemplatesByTypeAsync(DocumentType documentType);

    /// <summary>
    /// Gets a document template by ID
    /// </summary>
    Task<DocumentTemplateDto> GetTemplateByIdAsync(Guid id);

    /// <summary>
    /// Creates a new document template
    /// </summary>
    Task<DocumentTemplateDto> CreateTemplateAsync(CreateDocumentTemplateDto input);


    /// <summary>
    /// Uploads a new document template with file
    /// </summary>
    Task<DocumentTemplateDto> UploadTemplateAsync(UploadDocumentTemplateDto input);


    /// <summary>
    /// Updates a document template
    /// </summary>
    Task<DocumentTemplateDto> UpdateTemplateAsync(Guid id, UpdateDocumentTemplateDto input);

    /// <summary>
    /// Deletes a document template
    /// </summary>
    Task DeleteTemplateAsync(Guid id);

    /// <summary>
    /// Converts a DOCX template to PDF using the provided data model
    /// </summary>
    Task<FileDto> ConvertDocxToPdfAsync(DocxToPdfConversionDto input);

    Task<PagedResultDto<DocumentTemplateDto>> FilterListAsync(QueryParametersDto parameters);
}