using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// DTO for creating and updating ApprovalApprover entity
/// </summary>
public class CreateUpdateApprovalApproverDto
{
    /// <summary>
    /// ID of the approval approver (empty for new records, existing ID for updates)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    // [Required]
    public Guid? ApprovalId { get; set; }

    /// <summary>
    /// ID of the approver
    /// </summary>
    [Required]
    public Guid ApproverId { get; set; }

    /// <summary>
    /// Sequence number for approval order
    /// </summary>
    [Range(0, int.MaxValue)]
    public int Sequence { get; set; }

    /// <summary>
    /// Status of the approver
    /// </summary>
    [StringLength(50)]
    public string? Status { get; set; }
}