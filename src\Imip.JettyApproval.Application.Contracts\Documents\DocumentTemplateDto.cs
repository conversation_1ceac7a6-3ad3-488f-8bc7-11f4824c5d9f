using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// DTO for document template
/// </summary>
public class DocumentTemplateDto
{
    /// <summary>
    /// The unique identifier for the document template
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The name of the template
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// The document type
    /// </summary>
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// The document type name
    /// </summary>
    public string? DocumentTypeName { get; set; }

    /// <summary>
    /// The attachment ID that contains the DOCX template
    /// </summary>
    public Guid AttachmentId { get; set; }

    /// <summary>
    /// Description of the template
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is the default template for the document type
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// The creation time of the template
    /// </summary>
    public DateTime CreationTime { get; set; }
}
