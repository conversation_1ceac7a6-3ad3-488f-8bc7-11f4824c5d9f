import { Button } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/lib/useToast";
import React, { useState } from "react";

interface ApprovalActionsProps {
  approvalId: string;
  isOpen: boolean;
  onClose: () => void;
  action: "approve" | "reject";
  onSuccess?: () => void;
}

const ApprovalActions: React.FC<ApprovalActionsProps> = ({
  approvalId,
  isOpen,
  onClose,
  action,
  onSuccess,
}) => {
  const { toast } = useToast();
  const [notes, setNotes] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const endpoint = action === "approve"
        ? `/api/idjas/approval/approve/${approvalId}`
        : `/api/idjas/approval/reject/${approvalId}`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} approval`);
      }

      toast({
        title: "Success",
        description: `Approval ${action}d successfully`,
        variant: "default",
      });

      onSuccess?.();
      onClose();
      setNotes("");
    } catch (error) {
      console.error(`Error ${action}ing approval:`, error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${action} approval`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setNotes("");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {action === "approve" ? "Approve" : "Reject"} Request
          </DialogTitle>
          <DialogDescription>
            {action === "approve" 
              ? "Are you sure you want to approve this request? You can add optional notes below."
              : "Please provide a reason for rejecting this request."
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="notes" className="text-sm font-medium">
              {action === "approve" ? "Notes (Optional)" : "Rejection Reason"}
            </label>
            <Textarea
              id="notes"
              placeholder={
                action === "approve" 
                  ? "Add any additional notes..."
                  : "Please explain why this request is being rejected..."
              }
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              disabled={isLoading}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant={action === "approve" ? "default" : "destructive"}
            onClick={handleSubmit}
            disabled={isLoading || (action === "reject" && !notes.trim())}
          >
            {isLoading ? "Processing..." : action === "approve" ? "Approve" : "Reject"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApprovalActions;
