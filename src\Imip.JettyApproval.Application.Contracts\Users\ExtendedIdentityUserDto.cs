using System.ComponentModel.DataAnnotations;
using Volo.Abp.Identity;

namespace Imip.JettyApproval.Users;

public class ExtendedIdentityUserDto : IdentityUserDto
{
    public string? Company { get; set; }
    public string? Department { get; set; }
    public string? Position { get; set; }
    public bool MustChangePassword { get; set; }
    public bool ActiveDirectoryLogin { get; set; }
    public ConcurrentLoginPreventionMode ConcurrentLoginPreventionMode { get; set; }
}


/// <summary>
/// Defines the concurrent login prevention modes.
/// </summary>
public enum ConcurrentLoginPreventionMode
{
    /// <summary>
    /// No restriction on concurrent login. This is the default.
    /// </summary>
    [Display(Name = "Disabled")]
    Disabled = 0,

    /// <summary>
    /// Only one session of the same type can exist. Same type means we can restrict single login with a browser,
    /// but we may still can login with a mobile application without affecting the browser session.
    /// So, for each device type, we may allow a single login.
    /// </summary>
    [Display(Name = "Logout From Same Type Devices")]
    LogoutFromSameTypeDevices = 1,

    /// <summary>
    /// All other sessions will be logged out when a new session is created.
    /// </summary>
    [Display(Name = "Logout From All Devices")]
    LogoutFromAllDevices = 2
}