﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class FixApprovalForeignKeyRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalApprovers_ApprovalTemplates_ApprovalTemplateId",
                table: "ApprovalApprovers");

            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalCriterias_ApprovalTemplates_ApprovalTemplateId",
                table: "ApprovalCriterias");

            migrationBuilder.DropIndex(
                name: "IX_ApprovalCriterias_ApprovalTemplateId",
                table: "ApprovalCriterias");

            migrationBuilder.DropIndex(
                name: "IX_ApprovalApprovers_ApprovalTemplateId",
                table: "ApprovalApprovers");

            migrationBuilder.DropColumn(
                name: "ApprovalTemplateId",
                table: "ApprovalCriterias");

            migrationBuilder.DropColumn(
                name: "ApprovalTemplateId",
                table: "ApprovalApprovers");

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalApprovers_ApprovalTemplates_ApprovalId",
                table: "ApprovalApprovers",
                column: "ApprovalId",
                principalTable: "ApprovalTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalCriterias_ApprovalTemplates_ApprovalId",
                table: "ApprovalCriterias",
                column: "ApprovalId",
                principalTable: "ApprovalTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalApprovers_ApprovalTemplates_ApprovalId",
                table: "ApprovalApprovers");

            migrationBuilder.DropForeignKey(
                name: "FK_ApprovalCriterias_ApprovalTemplates_ApprovalId",
                table: "ApprovalCriterias");

            migrationBuilder.AddColumn<Guid>(
                name: "ApprovalTemplateId",
                table: "ApprovalCriterias",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "ApprovalTemplateId",
                table: "ApprovalApprovers",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_ApprovalCriterias_ApprovalTemplateId",
                table: "ApprovalCriterias",
                column: "ApprovalTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_ApprovalApprovers_ApprovalTemplateId",
                table: "ApprovalApprovers",
                column: "ApprovalTemplateId");

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalApprovers_ApprovalTemplates_ApprovalTemplateId",
                table: "ApprovalApprovers",
                column: "ApprovalTemplateId",
                principalTable: "ApprovalTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ApprovalCriterias_ApprovalTemplates_ApprovalTemplateId",
                table: "ApprovalCriterias",
                column: "ApprovalTemplateId",
                principalTable: "ApprovalTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
