using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services;

/// <summary>
/// Service for silent token refresh following OIDC standards
/// Refreshes tokens in the background without interrupting user experience
/// </summary>
public class SilentTokenRefreshService : IDisposable
{
    private readonly ITokenService _tokenService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IConfiguration _configuration;
    private readonly ILogger<SilentTokenRefreshService> _logger;
    private readonly Timer _refreshTimer;
    private readonly TimeSpan _refreshInterval = TimeSpan.FromMinutes(5); // Refresh 5 minutes before expiration

    public SilentTokenRefreshService(
        ITokenService tokenService,
        IHttpContextAccessor httpContextAccessor,
        IConfiguration configuration,
        ILogger<SilentTokenRefreshService> logger)
    {
        _tokenService = tokenService;
        _httpContextAccessor = httpContextAccessor;
        _configuration = configuration;
        _logger = logger;

        // Start the background refresh timer
        _refreshTimer = new Timer(RefreshTokenCallback, null, TimeSpan.Zero, _refreshInterval);
    }

    private async void RefreshTokenCallback(object state)
    {
        try
        {
            await RefreshTokenSilentlyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in silent token refresh");
        }
    }

    /// <summary>
    /// Refreshes the token silently in the background
    /// </summary>
    public async Task RefreshTokenSilentlyAsync()
    {
        try
        {
            var context = _httpContextAccessor.HttpContext;
            if (context?.User?.Identity?.IsAuthenticated != true)
            {
                return; // No authenticated user, skip refresh
            }

            // Check if token needs refresh
            var hasValidToken = await _tokenService.HasValidTokenAsync();
            if (hasValidToken)
            {
                _logger.LogDebug("Token is still valid, no refresh needed");
                return;
            }

            // Check if refresh token is expired
            var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();
            if (isRefreshTokenExpired)
            {
                _logger.LogWarning("Refresh token is expired, cannot perform silent refresh");
                return;
            }

            // Perform silent refresh
            _logger.LogInformation("Performing silent token refresh");
            var newToken = await _tokenService.RefreshAccessTokenAsync();

            if (!string.IsNullOrEmpty(newToken))
            {
                _logger.LogInformation("Silent token refresh successful");
            }
            else
            {
                _logger.LogWarning("Silent token refresh failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during silent token refresh");
        }
    }

    /// <summary>
    /// Manually trigger a token refresh (useful for API calls)
    /// </summary>
    public async Task<bool> TryRefreshTokenAsync()
    {
        try
        {
            var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();
            if (isRefreshTokenExpired)
            {
                return false;
            }

            var newToken = await _tokenService.RefreshAccessTokenAsync();
            return !string.IsNullOrEmpty(newToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual token refresh");
            return false;
        }
    }

    public void Dispose()
    {
        _refreshTimer?.Dispose();
    }
}