using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Approvals.ApprovalStages;

namespace Imip.JettyApproval.Approvals.ApprovalTemplates;

/// <summary>
/// DTO for creating and updating ApprovalTemplate entity
/// </summary>
public class CreateUpdateApprovalTemplateDto
{
    /// <summary>
    /// Name of the approval template
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the approval template
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Code for the approval template
    /// </summary>
    [StringLength(50)]
    public string? Code { get; set; }

    /// <summary>
    /// Collection of approval approvers
    /// </summary>
    public List<CreateUpdateApprovalApproverDto> Approvers { get; set; } = new List<CreateUpdateApprovalApproverDto>();

    /// <summary>
    /// Collection of approval criterias
    /// </summary>
    public List<CreateUpdateApprovalCriteriaDto> Criterias { get; set; } = new List<CreateUpdateApprovalCriteriaDto>();

    /// <summary>
    /// Collection of approval stages
    /// </summary>
    // public List<CreateUpdateApprovalStageDto> Stages { get; set; } = new List<CreateUpdateApprovalStageDto>();
}