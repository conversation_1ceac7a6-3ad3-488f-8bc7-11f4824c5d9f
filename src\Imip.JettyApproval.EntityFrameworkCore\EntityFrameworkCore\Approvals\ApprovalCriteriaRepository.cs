using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Approvals;

public class ApprovalCriteriaRepository : EfCoreRepository<JettyApprovalDbContext, ApprovalCriteria, Guid>, IApprovalCriteriaRepository
{
    public ApprovalCriteriaRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<ApprovalCriteria>> GetByApprovalIdAsync(Guid approvalId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalCriteria>()
            .Where(x => x.ApprovalId == approvalId)
            .ToListAsync();
    }

    public async Task<List<ApprovalCriteria>> GetByDocumentTypeAsync(string documentType)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalCriteria>()
            .Where(x => x.DocumentType == documentType)
            .ToListAsync();
    }
}