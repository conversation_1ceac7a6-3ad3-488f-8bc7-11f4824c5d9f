using System;
using System.Threading.Tasks;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// Application service interface for ApprovalCriteria entity
/// </summary>
public interface IApprovalCriteriaAppService :
    ICrudAppService<
        ApprovalCriteriaDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateApprovalCriteriaDto,
        CreateUpdateApprovalCriteriaDto>
{
    Task<PagedResultDto<ApprovalCriteriaDto>> FilterListAsync(QueryParametersDto parameters);
}