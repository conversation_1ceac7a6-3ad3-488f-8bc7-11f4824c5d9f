import { useMutation } from '@tanstack/react-query';
import { postApiEkbJetty } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { FilterRequestDto, JettyDto, PagedResultDtoOfJettyDto } from '@/client/types.gen';

export const useJettyDataWithFilter = () => {
  return useMutation<JettyDto[], Error, FilterRequestDto>({
    mutationFn: async (filterRequest: FilterRequestDto) => {
      try {
        const response = await postApiEkbJetty({ 
          body: filterRequest
        });
        
        // Extract items from the paged result
        const pagedResult = response.data as PagedResultDtoOfJettyDto;
        return pagedResult?.items || [];
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Jetty data';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('Jetty API Error:', error);
        toast({
          title: 'Error loading Jetty data',
          description: message,
          variant: 'destructive',
        });
        
        throw error; // Re-throw for mutation error handling
      }
    },
    onError: (error: unknown) => {
      console.error('Jetty mutation error:', error);
    }
  });
}; 