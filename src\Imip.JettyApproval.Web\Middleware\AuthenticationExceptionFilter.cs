using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System;
using Volo.Abp;

namespace Imip.JettyApproval.Web.Middleware;

/// <summary>
/// Custom exception filter to handle authentication-related exceptions
/// </summary>
public class AuthenticationExceptionFilter : IExceptionFilter
{
    private readonly ILogger<AuthenticationExceptionFilter> _logger;

    public AuthenticationExceptionFilter(ILogger<AuthenticationExceptionFilter> logger)
    {
        _logger = logger;
    }

    public void OnException(ExceptionContext context)
    {
        if (context.Exception is UnauthorizedAccessException unauthorizedEx)
        {
            _logger.LogWarning("Handling UnauthorizedAccessException: {Message}", unauthorizedEx.Message);

            // Check if this is an API request
            if (context.HttpContext.Request.Path.StartsWithSegments("/api"))
            {
                // For API requests, return a proper error response
                var errorResponse = new
                {
                    error = new
                    {
                        code = "AUTHENTICATION_REQUIRED",
                        message = "Authentication required. Please log in again.",
                        details = unauthorizedEx.Message
                    }
                };

                context.Result = new JsonResult(errorResponse)
                {
                    StatusCode = 401
                };
            }
            else
            {
                // For web requests, redirect to login
                context.Result = new RedirectToActionResult("Login", "Account", new { returnUrl = context.HttpContext.Request.Path });
            }

            context.ExceptionHandled = true;
        }
        else if (context.Exception is UserFriendlyException userFriendlyEx)
        {
            _logger.LogWarning("Handling UserFriendlyException: {Message}", userFriendlyEx.Message);

            // For API requests, return the user-friendly error
            if (context.HttpContext.Request.Path.StartsWithSegments("/api"))
            {
                var errorResponse = new
                {
                    error = new
                    {
                        code = "USER_FRIENDLY_ERROR",
                        message = userFriendlyEx.Message,
                        details = userFriendlyEx.Details
                    }
                };

                context.Result = new JsonResult(errorResponse)
                {
                    StatusCode = 400
                };
            }

            context.ExceptionHandled = true;
        }
    }
}