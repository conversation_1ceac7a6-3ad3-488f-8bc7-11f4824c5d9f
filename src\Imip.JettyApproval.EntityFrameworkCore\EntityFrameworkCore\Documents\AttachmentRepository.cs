using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Attachments;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Documents;

public class AttachmentRepository : EfCoreRepository<JettyApprovalDbContext, Attachment, Guid>, IAttachmentRepository
{
    public AttachmentRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    /// <summary>
    /// Gets attachments by reference ID and type
    /// </summary>
    public async Task<List<Attachment>> GetByReferenceAsync(Guid referenceId, string referenceType)
    {
        var dbContext = await GetDbContextAsync();

        return await dbContext.Set<Attachment>()
            .Where(a => a.ReferenceId == referenceId && a.ReferenceType == referenceType)
            .ToListAsync();
    }
}
