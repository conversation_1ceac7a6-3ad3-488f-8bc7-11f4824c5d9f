using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Documents;
using Imip.JettyApproval.DocumentTemplates;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Documents;

/// <summary>
/// Repository implementation for document templates
/// </summary>
public class DocumentTemplateRepository : EfCoreRepository<JettyApprovalDbContext, DocumentTemplate, Guid>, IDocumentTemplateRepository
{
    public DocumentTemplateRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    /// <summary>
    /// Gets document templates by document type
    /// </summary>
    public async Task<List<DocumentTemplate>> GetByDocumentTypeAsync(DocumentType documentType)
    {
        var dbContext = await GetDbContextAsync();

        return await dbContext.Set<DocumentTemplate>()
            .Where(t => t.DocumentType == documentType)
            .OrderByDescending(t => t.IsDefault)
            .ThenBy(t => t.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Gets the default template for a document type
    /// </summary>
    public async Task<DocumentTemplate?> GetDefaultTemplateAsync(DocumentType documentType)
    {
        var dbContext = await GetDbContextAsync();

        return await dbContext.Set<DocumentTemplate>()
            .Where(t => t.DocumentType == documentType && t.IsDefault)
            .FirstOrDefaultAsync();
    }
}
