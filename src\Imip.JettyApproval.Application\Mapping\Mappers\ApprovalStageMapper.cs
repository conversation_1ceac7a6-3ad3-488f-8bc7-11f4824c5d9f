using System;
using System.Collections.Generic;
using Imip.JettyApproval.Approvals.ApprovalStages;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.JettyRequests;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for ApprovalStage entity
/// </summary>
[Mapper]
public partial class ApprovalStageMapper : IMapperlyMapper
{
    private readonly JettyRequestItemMapper _jettyRequestItemMapper;

    public ApprovalStageMapper(JettyRequestItemMapper jettyRequestItemMapper)
    {
        _jettyRequestItemMapper = jettyRequestItemMapper;
    }
    // Entity to DTO mapping
    [MapProperty(nameof(ApprovalStage.Id), nameof(ApprovalStageDto.Id))]
    [MapperIgnoreSource(nameof(ApprovalStage.IsDeleted))]
    [MapperIgnoreSource(nameof(ApprovalStage.DeleterId))]
    [MapperIgnoreSource(nameof(ApprovalStage.DeletionTime))]
    [MapperIgnoreSource(nameof(ApprovalStage.LastModificationTime))]
    [MapperIgnoreSource(nameof(ApprovalStage.LastModifierId))]
    [MapperIgnoreSource(nameof(ApprovalStage.CreationTime))]
    [MapperIgnoreSource(nameof(ApprovalStage.CreatorId))]
    [MapperIgnoreSource(nameof(ApprovalStage.ExtraProperties))]
    [MapperIgnoreSource(nameof(ApprovalStage.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(ApprovalStage.Approver))]
    [MapperIgnoreSource(nameof(ApprovalStage.Requester))]
    public partial ApprovalStageDto MapToDto(ApprovalStage entity);

    // Custom mapping for ApprovalTemplate
    public partial ApprovalTemplateDto? MapApprovalTemplate(Approvals.ApprovalTemplates.ApprovalTemplate? template);

    // Custom mapping for JettyRequestItem using the dedicated mapper for proper date formatting
    public JettyRequestItemDto? MapJettyRequestItem(JettyRequestItem? item)
    {
        return item != null ? _jettyRequestItemMapper.MapToDto(item) : null;
    }

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ApprovalStage.Id))] // Don't change existing Id
    public partial void MapToEntity(CreateUpdateApprovalStageDto dto, ApprovalStage entity);

    // Custom mapping methods for complex scenarios
    public ApprovalStage CreateEntityWithId(CreateUpdateApprovalStageDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ApprovalStage)Activator.CreateInstance(typeof(ApprovalStage), true)!;

        // Set the ID using reflection
        var idProperty = typeof(ApprovalStage).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ApprovalStageDto> MapToDtoList(List<ApprovalStage> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ApprovalStageDto> MapToDtoEnumerable(IEnumerable<ApprovalStage> entities);
}