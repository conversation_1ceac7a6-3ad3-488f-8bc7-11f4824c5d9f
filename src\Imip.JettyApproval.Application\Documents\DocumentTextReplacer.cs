using System;
using System.Collections.Generic;
using System.Linq;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// Utility class for replacing text placeholders in Word documents
/// </summary>
public static class DocumentTextReplacer
{
    /// <summary>
    /// Replaces text in the document, handling cases where placeholders might be split across multiple Text elements
    /// </summary>
    public static void ReplaceTextInDocument(Document document, Dictionary<string, string> placeholders)
    {
        try
        {
            // First, handle simple cases where the placeholder is entirely within a single Text element
            foreach (var text in document.Descendants<Text>())
            {
                if (text.Text.Contains("{{"))
                {
                    string originalText = text.Text;
                    string newText = originalText;

                    foreach (var placeholder in placeholders)
                    {
                        if (newText.Contains(placeholder.Key))
                        {
                            newText = newText.Replace(placeholder.Key, placeholder.Value);
                        }
                    }

                    if (newText != originalText)
                    {
                        text.Text = newText;
                    }
                }
            }

            // Try a simpler approach for handling split placeholders
            ReplaceSplitPlaceholdersSimpleApproach(document, placeholders);

            // If placeholders still exist, try the more complex approach
            bool placeholdersExist = CheckIfPlaceholdersExist(document, placeholders);
            if (placeholdersExist)
            {
                ReplaceSplitPlaceholdersComplexApproach(document, placeholders);
            }

            // Also handle table cells specifically
            ReplaceTextInTableCells(document, placeholders);
        }
        catch (Exception)
        {
            // If any exception occurs, fall back to the simplest approach
            ReplaceSplitPlaceholdersSimpleApproach(document, placeholders);
        }
    }

    /// <summary>
    /// Checks if any placeholders still exist in the document
    /// </summary>
    private static bool CheckIfPlaceholdersExist(Document document, Dictionary<string, string> placeholders)
    {
        string documentText = string.Join("", document.Descendants<Text>().Select(t => t.Text));
        return placeholders.Keys.Any(placeholder => documentText.Contains(placeholder));
    }

    /// <summary>
    /// Simple approach to replace placeholders that might be split across multiple Text elements
    /// </summary>
    private static void ReplaceSplitPlaceholdersSimpleApproach(Document document, Dictionary<string, string> placeholders)
    {
        // Process each paragraph
        foreach (var paragraph in document.Descendants<Paragraph>())
        {
            // Get all text elements in this paragraph
            var textElements = paragraph.Descendants<Text>().ToList();
            if (textElements.Count <= 1) continue;

            // Combine all text in the paragraph
            string combinedText = string.Join("", textElements.Select(t => t.Text));

            // Check if the combined text contains any placeholder
            bool containsPlaceholder = false;
            foreach (var placeholderKey in placeholders.Keys)
            {
                if (combinedText.Contains(placeholderKey))
                {
                    containsPlaceholder = true;
                    break;
                }
            }

            if (!containsPlaceholder) continue;

            // Replace placeholders in the combined text
            string newCombinedText = combinedText;
            foreach (var placeholder in placeholders)
            {
                if (newCombinedText.Contains(placeholder.Key))
                {
                    newCombinedText = newCombinedText.Replace(placeholder.Key, placeholder.Value);
                }
            }

            // If the text changed, update the paragraph
            if (newCombinedText != combinedText)
            {
                // Clear all existing text elements
                foreach (var text in textElements)
                {
                    text.Text = "";
                }

                // Add the new text to the first element
                if (textElements.Count > 0)
                {
                    textElements[0].Text = newCombinedText;
                }
            }
        }
    }

    /// <summary>
    /// Complex approach to replace placeholders that might be split across multiple Text elements
    /// </summary>
    private static void ReplaceSplitPlaceholdersComplexApproach(Document document, Dictionary<string, string> placeholders)
    {
        // Process each paragraph
        foreach (var paragraph in document.Descendants<Paragraph>())
        {
            // Get all text elements in this paragraph
            var textElements = paragraph.Descendants<Text>().ToList();
            if (textElements.Count <= 1) continue;

            // Combine all text in the paragraph to check for placeholders
            string combinedText = string.Join("", textElements.Select(t => t.Text));

            // Check if the combined text contains any placeholder
            bool containsPlaceholder = false;
            foreach (var placeholderKey in placeholders.Keys)
            {
                if (combinedText.Contains(placeholderKey))
                {
                    containsPlaceholder = true;
                    break;
                }
            }

            if (!containsPlaceholder) continue;

            // Process each placeholder that might be split across multiple Text elements
            foreach (var placeholder in placeholders)
            {
                if (!combinedText.Contains(placeholder.Key)) continue;

                // Find the starting and ending Text elements for this placeholder
                int placeholderStart = combinedText.IndexOf(placeholder.Key);
                int placeholderEnd = placeholderStart + placeholder.Key.Length - 1;

                int currentPosition = 0;
                int startElementIndex = -1;
                int endElementIndex = -1;

                // Find which Text elements contain the start and end of the placeholder
                for (int i = 0; i < textElements.Count; i++)
                {
                    int textLength = textElements[i].Text.Length;

                    if (startElementIndex == -1 && placeholderStart >= currentPosition && placeholderStart < currentPosition + textLength)
                    {
                        startElementIndex = i;
                    }

                    if (endElementIndex == -1 && placeholderEnd >= currentPosition && placeholderEnd < currentPosition + textLength)
                    {
                        endElementIndex = i;
                        break;
                    }

                    currentPosition += textLength;
                }

                // If we found both the start and end elements
                if (startElementIndex != -1 && endElementIndex != -1)
                {
                    // Handle the case where the placeholder is split across multiple Text elements
                    if (startElementIndex == endElementIndex)
                    {
                        // Placeholder is within a single Text element (already handled above, but we'll do it again for completeness)
                        textElements[startElementIndex].Text = textElements[startElementIndex].Text.Replace(placeholder.Key, placeholder.Value);
                    }
                    else
                    {
                        // Calculate positions within the start and end elements
                        int startPos = placeholderStart - currentPosition + textElements[startElementIndex].Text.Length * startElementIndex;
                        int startElementPlaceholderPos = startPos - (currentPosition - textElements[startElementIndex].Text.Length);

                        // Replace the part of the placeholder in the start element
                        string startText = textElements[startElementIndex].Text;
                        textElements[startElementIndex].Text = string.Concat(startText.AsSpan(0, startElementPlaceholderPos), placeholder.Value);

                        // Remove the placeholder parts from middle elements
                        for (int i = startElementIndex + 1; i < endElementIndex; i++)
                        {
                            textElements[i].Text = "";
                        }

                        // Remove the part of the placeholder from the end element
                        string endText = textElements[endElementIndex].Text;
                        int endElementPlaceholderEndPos = placeholderEnd - (currentPosition - textElements[endElementIndex].Text.Length) + 1;
                        if (endElementPlaceholderEndPos < endText.Length)
                        {
                            textElements[endElementIndex].Text = endText[endElementPlaceholderEndPos..];
                        }
                        else
                        {
                            textElements[endElementIndex].Text = "";
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// Specifically handles replacing text in table cells, which might have different formatting
    /// </summary>
    private static void ReplaceTextInTableCells(Document document, Dictionary<string, string> placeholders)
    {
        // Find all tables in the document
        var tables = document.Descendants<Table>().ToList();

        foreach (var table in tables)
        {
            // Process each cell in the table
            foreach (var cell in table.Descendants<TableCell>())
            {
                // Get all text in this cell
                var textElements = cell.Descendants<Text>().ToList();
                if (textElements.Count == 0) continue;

                // Combine all text in the cell
                string combinedText = string.Join("", textElements.Select(t => t.Text));

                // Check if the combined text contains any placeholder
                bool containsPlaceholder = false;
                foreach (var placeholder in placeholders.Keys)
                {
                    if (combinedText.Contains(placeholder))
                    {
                        containsPlaceholder = true;
                        break;
                    }
                }

                if (!containsPlaceholder) continue;

                // Replace placeholders in the combined text
                string newCombinedText = combinedText;
                foreach (var placeholder in placeholders)
                {
                    if (newCombinedText.Contains(placeholder.Key))
                    {
                        newCombinedText = newCombinedText.Replace(placeholder.Key, placeholder.Value);
                    }
                }

                // If the text changed, update the cell
                if (newCombinedText != combinedText)
                {
                    // If there's only one text element, just update it
                    if (textElements.Count == 1)
                    {
                        textElements[0].Text = newCombinedText;
                    }
                    else
                    {
                        // Otherwise, clear all text elements and add the new text to the first one
                        foreach (var text in textElements)
                        {
                            text.Text = "";
                        }

                        if (textElements.Count > 0)
                        {
                            textElements[0].Text = newCombinedText;
                        }
                        else
                        {
                            // If there are no text elements, create a new one
                            var paragraph = cell.Descendants<Paragraph>().FirstOrDefault();
                            if (paragraph != null)
                            {
                                var run = new Run(new Text(newCombinedText));
                                paragraph.AppendChild(run);
                            }
                        }
                    }
                }
            }
        }
    }
}
