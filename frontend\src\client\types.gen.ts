// This file is auto-generated by @hey-api/openapi-ts

export type AbpLoginResult = {
    result?: LoginResultType;
    readonly description?: string | null;
};

export type AgentDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    status?: string | null;
    type?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    npwpNo?: string | null;
    bdmSapcode?: string | null;
    taxCode?: string | null;
    addressNpwp?: string | null;
    address?: string | null;
    sapcodeS4?: string | null;
};

export type ApplicationAuthConfigurationDto = {
    grantedPolicies?: {
        [key: string]: boolean;
    } | null;
};

export type ApplicationConfigurationDto = {
    localization?: ApplicationLocalizationConfigurationDto;
    auth?: ApplicationAuthConfigurationDto;
    setting?: ApplicationSettingConfigurationDto;
    currentUser?: CurrentUserDto;
    features?: ApplicationFeatureConfigurationDto;
    globalFeatures?: ApplicationGlobalFeatureConfigurationDto;
    multiTenancy?: MultiTenancyInfoDto;
    currentTenant?: CurrentTenantDto;
    timing?: TimingDto;
    clock?: ClockDto;
    objectExtensions?: ObjectExtensionsDto;
    extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type ApplicationDocumentGenerationDto = {
    jettyRequestItemId: string;
    templateId?: string | null;
    customFilename?: string | null;
    generatePdf?: boolean;
};

export type ApplicationFeatureConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type ApplicationGlobalFeatureConfigurationDto = {
    enabledFeatures?: Array<string> | null;
};

export type ApplicationLocalizationConfigurationDto = {
    values?: {
        [key: string]: {
            [key: string]: string;
        };
    } | null;
    resources?: {
        [key: string]: ApplicationLocalizationResourceDto;
    } | null;
    languages?: Array<LanguageInfo> | null;
    currentCulture?: CurrentCultureDto;
    defaultResourceName?: string | null;
    languagesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
    languageFilesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
};

export type ApplicationLocalizationResourceDto = {
    texts?: {
        [key: string]: string;
    } | null;
    baseResources?: Array<string> | null;
};

export type ApplicationSettingConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type ApprovalActionDto = {
    notes?: string | null;
};

export type ApprovalApproverDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    approvalId?: string;
    approverId?: string;
    sequence?: number;
    status?: string | null;
};

export type ApprovalCriteriaDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    approvalId?: string;
    documentType?: string | null;
};

export type ApprovalDelegationDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    approverId?: string;
    substituteId?: string;
    startDate?: string;
    endDate?: string;
    isActive?: boolean;
    notes?: string | null;
};

export type ApprovalStageDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    approvalTemplateId?: string;
    approverId?: string;
    actionDate?: string | null;
    documentId?: string | null;
    requesterId?: string | null;
    requestDate?: string | null;
    status?: ApprovalStatus;
    notes?: string | null;
    approvalTemplate?: ApprovalTemplateDto;
    jettyRequestItem?: JettyRequestItemDto;
    approverUserName?: string | null;
    requesterUserName?: string | null;
};

export type ApprovalStatus = 0 | 1 | 2 | 3;

export type ApprovalTemplateDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    name?: string | null;
    description?: string | null;
    code?: string | null;
    approvers?: Array<ApprovalApproverDto> | null;
    criterias?: Array<ApprovalCriteriaDto> | null;
    stages?: Array<ApprovalStageDto> | null;
};

export type BulkDownloadDto = {
    fileIds: Array<string>;
    zipFileName?: string | null;
};

export type BusinessPartnerDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    status?: string | null;
    alias?: string | null;
    image?: string | null;
    direction?: string | null;
    regionType?: string | null;
    address?: string | null;
    tenant?: string | null;
    npwp1?: string | null;
    npwp2?: string | null;
};

export type CargoDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    status?: string | null;
    alias?: string | null;
    flag?: string | null;
    grossWeight?: number;
    type?: string | null;
    loaQty?: number | null;
};

export type CargoShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    alias?: string | null;
    type?: string | null;
    grossWeight?: number | null;
};

export type ChangePasswordInput = {
    currentPassword?: string | null;
    newPassword: string;
};

export type ClockDto = {
    kind?: string | null;
};

export type ConcurrentLoginPreventionMode = 0 | 1 | 2;

export type CreateDocumentTemplateDto = {
    name: string;
    documentType: DocumentType;
    attachmentId: string;
    description?: string | null;
    isDefault?: boolean;
};

export type CreateUpdateApprovalApproverDto = {
    id?: string | null;
    approvalId?: string | null;
    approverId: string;
    sequence?: number;
    status?: string | null;
};

export type CreateUpdateApprovalCriteriaDto = {
    id?: string | null;
    approvalId?: string | null;
    documentType?: string | null;
};

export type CreateUpdateApprovalDelegationDto = {
    approverId: string;
    substituteId: string;
    startDate: string;
    endDate: string;
    isActive?: boolean;
    notes?: string | null;
};

export type CreateUpdateApprovalStageDto = {
    id?: string;
    approvalTemplateId: string;
    approverId: string;
    actionDate?: string | null;
    documentId?: string | null;
    requesterId?: string | null;
    requestDate?: string | null;
    status?: ApprovalStatus;
    notes?: string | null;
};

export type CreateUpdateApprovalTemplateDto = {
    name: string;
    description?: string | null;
    code?: string | null;
    approvers?: Array<CreateUpdateApprovalApproverDto> | null;
    criterias?: Array<CreateUpdateApprovalCriteriaDto> | null;
};

export type CreateUpdateJettyRequestDto = {
    docNum: number;
    vesselType: string;
    referenceId?: string | null;
    vesselName: string;
    voyage?: string | null;
    jetty?: string | null;
    arrivalDate?: string | null;
    departureDate?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    postDate?: string | null;
    barge?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    items?: Array<CreateUpdateJettyRequestItemDto> | null;
};

export type CreateUpdateJettyRequestItemDto = {
    jettyRequestId?: string | null;
    tenantName?: string | null;
    itemName?: string | null;
    qty?: number;
    uoM?: string | null;
    notes?: string | null;
    status: JettyRequestItemStatus;
    letterNo?: string | null;
    letterDate?: string | null;
    referenceId?: string | null;
};

export type CurrentCultureDto = {
    displayName?: string | null;
    englishName?: string | null;
    threeLetterIsoLanguageName?: string | null;
    twoLetterIsoLanguageName?: string | null;
    isRightToLeft?: boolean;
    cultureName?: string | null;
    name?: string | null;
    nativeName?: string | null;
    dateTimeFormat?: DateTimeFormatDto;
};

export type CurrentTenantDto = {
    id?: string | null;
    name?: string | null;
    isAvailable?: boolean;
};

export type CurrentUserDto = {
    isAuthenticated?: boolean;
    id?: string | null;
    tenantId?: string | null;
    impersonatorUserId?: string | null;
    impersonatorTenantId?: string | null;
    impersonatorUserName?: string | null;
    impersonatorTenantName?: string | null;
    userName?: string | null;
    name?: string | null;
    surName?: string | null;
    email?: string | null;
    emailVerified?: boolean;
    phoneNumber?: string | null;
    phoneNumberVerified?: boolean;
    roles?: Array<string> | null;
    sessionId?: string | null;
};

export type DateTimeFormatDto = {
    calendarAlgorithmType?: string | null;
    dateTimeFormatLong?: string | null;
    shortDatePattern?: string | null;
    fullDateTimePattern?: string | null;
    dateSeparator?: string | null;
    shortTimePattern?: string | null;
    longTimePattern?: string | null;
};

export type DelimiterConfigDto = {
    start: string;
    end: string;
};

export type DestinationPortDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    country?: string | null;
    docType?: string | null;
};

export type DocumentTemplateDto = {
    id?: string;
    name?: string | null;
    documentType?: DocumentType;
    documentTypeName?: string | null;
    attachmentId?: string;
    description?: string | null;
    isDefault?: boolean;
    creationTime?: string;
};

export type DocumentType = 1 | 2 | 3 | 4 | 5 | 6 | 99;

export type DocxToPdfConversionDto = {
    documentType: DocumentType;
    templateId?: string | null;
    config: TemplateConfigDto;
    model: unknown;
};

export type EntityExtensionDto = {
    properties?: {
        [key: string]: ExtensionPropertyDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type ExtendedIdentityUserDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
    company?: string | null;
    department?: string | null;
    position?: string | null;
    mustChangePassword?: boolean;
    activeDirectoryLogin?: boolean;
    concurrentLoginPreventionMode?: ConcurrentLoginPreventionMode;
};

export type ExtensionEnumDto = {
    fields?: Array<ExtensionEnumFieldDto> | null;
    localizationResource?: string | null;
};

export type ExtensionEnumFieldDto = {
    name?: string | null;
    value?: unknown;
};

export type ExtensionPropertyApiCreateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiDto = {
    onGet?: ExtensionPropertyApiGetDto;
    onCreate?: ExtensionPropertyApiCreateDto;
    onUpdate?: ExtensionPropertyApiUpdateDto;
};

export type ExtensionPropertyApiGetDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiUpdateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyAttributeDto = {
    typeSimple?: string | null;
    config?: {
        [key: string]: unknown;
    } | null;
};

export type ExtensionPropertyDto = {
    type?: string | null;
    typeSimple?: string | null;
    displayName?: LocalizableStringDto;
    api?: ExtensionPropertyApiDto;
    ui?: ExtensionPropertyUiDto;
    policy?: ExtensionPropertyPolicyDto;
    attributes?: Array<ExtensionPropertyAttributeDto> | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
    defaultValue?: unknown;
};

export type ExtensionPropertyFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyGlobalFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPermissionPolicyDto = {
    permissionNames?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPolicyDto = {
    globalFeatures?: ExtensionPropertyGlobalFeaturePolicyDto;
    features?: ExtensionPropertyFeaturePolicyDto;
    permissions?: ExtensionPropertyPermissionPolicyDto;
};

export type ExtensionPropertyUiDto = {
    onTable?: ExtensionPropertyUiTableDto;
    onCreateForm?: ExtensionPropertyUiFormDto;
    onEditForm?: ExtensionPropertyUiFormDto;
    lookup?: ExtensionPropertyUiLookupDto;
};

export type ExtensionPropertyUiFormDto = {
    isVisible?: boolean;
};

export type ExtensionPropertyUiLookupDto = {
    url?: string | null;
    resultListPropertyName?: string | null;
    displayPropertyName?: string | null;
    valuePropertyName?: string | null;
    filterParamName?: string | null;
};

export type ExtensionPropertyUiTableDto = {
    isVisible?: boolean;
};

export type FileDto = {
    fileName?: string | null;
    contentType?: string | null;
    content?: string | null;
};

export type FileUploadInputDto = {
    description?: string | null;
    referenceId?: string | null;
    referenceType?: string | null;
    fileName: string;
    contentType: string;
    fileContent: string;
};

export type FileUploadResultDto = {
    id?: string;
    fileName?: string | null;
    contentType?: string | null;
    size?: number;
    url?: string | null;
    streamUrl?: string | null;
    uploadTime?: string;
};

export type FilterCondition = {
    fieldName: string;
    operator: FilterOperator;
    value: unknown;
};

export type FilterGroup = {
    operator: LogicalOperator;
    conditions: Array<FilterCondition>;
};

export type FilterOperator = 'Equals' | 'NotEquals' | 'Contains' | 'StartsWith' | 'EndsWith' | 'GreaterThan' | 'GreaterThanOrEqual' | 'LessThan' | 'LessThanOrEqual' | 'In' | 'NotIn' | 'Between' | 'NotBetween' | 'IsNull' | 'IsNotNull' | 'IsEmpty' | 'IsNotEmpty' | 'IsTrue' | 'IsFalse' | 'IsNullOrEmpty' | 'IsNotNullOrEmpty' | 'IsNullOrWhiteSpace' | 'IsNotNullOrWhiteSpace' | 'IsNumeric' | 'IsAlpha' | 'IsAlphaNumeric' | 'IsEmail' | 'IsUrl' | 'IsIp' | 'IsIpv4' | 'IsIpv6' | 'IsGuid' | 'IsGuidEmpty' | 'IsGuidNotEmpty' | 'IsGuidNull' | 'IsGuidNotNull' | 'IsGuidNullOrEmpty' | 'IsGuidNotNullOrEmpty' | 'IsGuidNullOrWhiteSpace' | 'IsGuidNotNullOrWhiteSpace' | 'IsGuidNumeric' | 'IsGuidAlpha' | 'IsGuidAlphaNumeric';

export type FilterRequestDto = {
    maxResultCount?: number;
    skipCount?: number;
    filterGroup?: FilterGroup;
};

export type FindTenantResultDto = {
    success?: boolean;
    tenantId?: string | null;
    name?: string | null;
    normalizedName?: string | null;
    isActive?: boolean;
};

export type GetUsersInput = {
    maxResultCount?: number;
    skipCount?: number;
    sorting?: string | null;
    filterGroup?: FilterGroup;
    sortFields?: Array<SortInfo> | null;
    searchKeyword?: string | null;
    isActive?: boolean | null;
    isLockedOut?: boolean | null;
};

export type IanaTimeZone = {
    timeZoneName?: string | null;
};

export type IdentityUserDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
};

export type JettyDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    alias?: string | null;
    max?: number;
    deleted?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    port?: string | null;
};

export type JettyRequestDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    docNum?: number;
    vesselType?: string | null;
    referenceId?: string | null;
    vesselName?: string | null;
    voyage?: string | null;
    jetty?: string | null;
    arrivalDate?: string | null;
    departureDate?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    postDate?: string | null;
    barge?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    items?: Array<JettyRequestItemDto> | null;
};

export type JettyRequestItemDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    jettyRequestId?: string;
    tenantName?: string | null;
    itemName?: string | null;
    qty?: number;
    uoM?: string | null;
    notes?: string | null;
    status?: string | null;
    letterNo?: string | null;
    letterDate?: string | null;
    referenceId?: string | null;
};

export type JettyRequestItemStatus = 0 | 1 | 2 | 3 | 4;

export type JettyShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    alias?: string | null;
    port?: string | null;
    max?: number | null;
};

export type LanguageInfo = {
    cultureName?: string | null;
    uiCultureName?: string | null;
    displayName?: string | null;
    readonly twoLetterISOLanguageName?: string | null;
};

export type LocalizableStringDto = {
    name?: string | null;
    resource?: string | null;
};

export type LogicalOperator = 'And' | 'Or';

export type LoginResultType = 1 | 2 | 3 | 4 | 5;

export type MasterTenantDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    flags?: string | null;
    fullName?: string | null;
    letterPerson?: string | null;
    letterRole?: string | null;
    npwp?: string | null;
    address?: string | null;
    nib?: string | null;
    phone?: string | null;
    status?: string | null;
    noAndDateNotaris?: string | null;
    descNotaris?: string | null;
    sapcode?: string | null;
    isExternal?: string | null;
    billing?: string | null;
    billingPrice?: number | null;
    esignUserId?: string | null;
    token?: string | null;
    sapcodeBdt?: string | null;
    sapcodeUsd?: string | null;
    coordinate?: string | null;
    boundaries?: string | null;
    isTenant?: string | null;
    channelId?: string | null;
    usePrivy?: string | null;
    sapcodeS4?: string | null;
    skbpph?: string | null;
    companyGroup?: string | null;
    factoryLocation?: string | null;
    masterGroupId?: number | null;
};

export type ModuleExtensionDto = {
    entities?: {
        [key: string]: EntityExtensionDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type MultiTenancyInfoDto = {
    isEnabled?: boolean;
};

export type NameValue = {
    name?: string | null;
    value?: string | null;
};

export type ObjectExtensionsDto = {
    modules?: {
        [key: string]: ModuleExtensionDto;
    } | null;
    enums?: {
        [key: string]: ExtensionEnumDto;
    } | null;
};

export type PagedResultDtoOfAgentDto = {
    items?: Array<AgentDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfApprovalApproverDto = {
    items?: Array<ApprovalApproverDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfApprovalCriteriaDto = {
    items?: Array<ApprovalCriteriaDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfApprovalDelegationDto = {
    items?: Array<ApprovalDelegationDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfApprovalStageDto = {
    items?: Array<ApprovalStageDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfApprovalTemplateDto = {
    items?: Array<ApprovalTemplateDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfBusinessPartnerDto = {
    items?: Array<BusinessPartnerDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfCargoDto = {
    items?: Array<CargoDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfDestinationPortDto = {
    items?: Array<DestinationPortDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfDocumentTemplateDto = {
    items?: Array<DocumentTemplateDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfExtendedIdentityUserDto = {
    items?: Array<ExtendedIdentityUserDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfJettyDto = {
    items?: Array<JettyDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfJettyRequestDto = {
    items?: Array<JettyRequestDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfJettyRequestItemDto = {
    items?: Array<JettyRequestItemDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfMasterTenantDto = {
    items?: Array<MasterTenantDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfSurveyorDto = {
    items?: Array<SurveyorDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTenantDto = {
    items?: Array<TenantDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfVesselHeaderDto = {
    items?: Array<VesselHeaderDto> | null;
    totalCount?: number;
};

export type ProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    isExternal?: boolean;
    hasPassword?: boolean;
    concurrencyStamp?: string | null;
};

export type QueryParametersDto = {
    sorting?: string | null;
    page?: number;
    sort?: Array<SortInfo> | null;
    filterGroup?: FilterGroup;
    readonly skipCount?: number;
    maxResultCount?: number;
};

export type RegisterDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    emailAddress: string;
    password: string;
    appName: string;
};

export type RemoteServiceErrorInfo = {
    code?: string | null;
    message?: string | null;
    details?: string | null;
    data?: {
        [key: string]: unknown;
    } | null;
    validationErrors?: Array<RemoteServiceValidationErrorInfo> | null;
};

export type RemoteServiceErrorResponse = {
    error?: RemoteServiceErrorInfo;
};

export type RemoteServiceValidationErrorInfo = {
    message?: string | null;
    members?: Array<string> | null;
};

export type ResetPasswordDto = {
    userId?: string;
    resetToken: string;
    password: string;
};

export type SendPasswordResetCodeDto = {
    email: string;
    appName: string;
    returnUrl?: string | null;
    returnUrlHash?: string | null;
};

export type SortInfo = {
    field?: string | null;
    desc?: boolean;
};

export type SubmitApprovalDto = {
    documentId: string;
    documentType: string;
    notes?: string | null;
};

export type SurveyorDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    address?: string | null;
    npwp?: string | null;
    isActive?: string | null;
    createdBy?: number;
    updatedBy?: number | null;
};

export type TemplateConfigDto = {
    delimiter: DelimiterConfigDto;
};

export type TenantCreateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    adminEmailAddress: string;
    adminPassword: string;
};

export type TenantDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    concurrencyStamp?: string | null;
};

export type TenantShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    fullName?: string | null;
};

export type TenantUpdateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    concurrencyStamp?: string | null;
};

export type TimeZone = {
    iana?: IanaTimeZone;
    windows?: WindowsTimeZone;
};

export type TimingDto = {
    timeZone?: TimeZone;
};

export type TokenValidationRequest = {
    token?: string | null;
};

export type UpdateDocumentTemplateDto = {
    name: string;
    documentType: DocumentType;
    description?: string | null;
    isDefault?: boolean;
};

export type UpdateProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    concurrencyStamp?: string | null;
};

export type UpdateUserNameRequest = {
    name?: string | null;
};

export type UploadDocumentTemplateDto = {
    name: string;
    documentType: DocumentType;
    description?: string | null;
    isDefault?: boolean;
    file: Blob | File;
};

export type UserLoginInfo = {
    userNameOrEmailAddress: string;
    password: string;
    rememberMe?: boolean;
};

export type VerifyPasswordResetTokenInput = {
    userId?: string;
    resetToken: string;
};

export type VesselHeaderDto = {
    id?: string;
    docEntry?: number;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    vesselType?: string | null;
    items?: Array<VesselItemDto> | null;
    cargo?: CargoShortDto;
    barge?: CargoShortDto;
    jetty?: JettyShortDto;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    agentName?: string | null;
};

export type VesselItemDto = {
    id?: string;
    docEntry?: number;
    docNum?: number;
    tenantName?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    cargo?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    vesselType?: string | null;
    tenant?: TenantShortDto;
    letterNo?: string | null;
    letterDate?: string | null;
};

export type VesselListRequestDto = {
    vesselType?: string | null;
    fromDate?: string | null;
    toDate?: string | null;
    vesselName?: string | null;
    voyage?: string | null;
    tenantName?: string | null;
    filterGroup?: FilterGroup;
};

export type VesselQueryRequestDto = {
    maxResultCount?: number;
    skipCount?: number;
    vesselType?: string | null;
    filterGroup?: FilterGroup;
};

export type WindowsTimeZone = {
    timeZoneId?: string | null;
};

export type GetApiAbpApplicationConfigurationData = {
    body?: never;
    path?: never;
    query?: {
        IncludeLocalizationResources?: boolean;
    };
    url: '/api/abp/application-configuration';
};

export type GetApiAbpApplicationConfigurationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpApplicationConfigurationError = GetApiAbpApplicationConfigurationErrors[keyof GetApiAbpApplicationConfigurationErrors];

export type GetApiAbpApplicationConfigurationResponses = {
    /**
     * OK
     */
    200: ApplicationConfigurationDto;
};

export type GetApiAbpApplicationConfigurationResponse = GetApiAbpApplicationConfigurationResponses[keyof GetApiAbpApplicationConfigurationResponses];

export type GetApiAbpMultiTenancyTenantsByNameByNameData = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-name/{name}';
};

export type GetApiAbpMultiTenancyTenantsByNameByNameErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameError = GetApiAbpMultiTenancyTenantsByNameByNameErrors[keyof GetApiAbpMultiTenancyTenantsByNameByNameErrors];

export type GetApiAbpMultiTenancyTenantsByNameByNameResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameResponse = GetApiAbpMultiTenancyTenantsByNameByNameResponses[keyof GetApiAbpMultiTenancyTenantsByNameByNameResponses];

export type GetApiAbpMultiTenancyTenantsByIdByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-id/{id}';
};

export type GetApiAbpMultiTenancyTenantsByIdByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdError = GetApiAbpMultiTenancyTenantsByIdByIdErrors[keyof GetApiAbpMultiTenancyTenantsByIdByIdErrors];

export type GetApiAbpMultiTenancyTenantsByIdByIdResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdResponse = GetApiAbpMultiTenancyTenantsByIdByIdResponses[keyof GetApiAbpMultiTenancyTenantsByIdByIdResponses];

export type GetAccountExternalLoginData = {
    body?: never;
    path?: never;
    query?: {
        provider?: string;
        returnUrl?: string;
    };
    url: '/Account/ExternalLogin';
};

export type GetAccountExternalLoginResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAccountExternalLoginCallbackData = {
    body?: never;
    path?: never;
    query?: {
        returnUrl?: string;
        remoteError?: string;
    };
    url: '/Account/ExternalLoginCallback';
};

export type GetAccountExternalLoginCallbackResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAccountLoginData = {
    body?: never;
    path?: never;
    query?: {
        returnUrl?: string;
    };
    url: '/Account/Login';
};

export type GetAccountLoginResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAccountLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/Account/Logout';
};

export type GetAccountLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAccountLogoutRedirectData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/Account/LogoutRedirect';
};

export type GetAccountLogoutRedirectResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAccountSignoutCallbackOidcData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/Account/signout-callback-oidc';
};

export type GetAccountSignoutCallbackOidcResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountRegisterData = {
    body?: RegisterDto;
    path?: never;
    query?: never;
    url: '/api/account/register';
};

export type PostApiAccountRegisterErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountRegisterError = PostApiAccountRegisterErrors[keyof PostApiAccountRegisterErrors];

export type PostApiAccountRegisterResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type PostApiAccountRegisterResponse = PostApiAccountRegisterResponses[keyof PostApiAccountRegisterResponses];

export type PostApiAccountSendPasswordResetCodeData = {
    body?: SendPasswordResetCodeDto;
    path?: never;
    query?: never;
    url: '/api/account/send-password-reset-code';
};

export type PostApiAccountSendPasswordResetCodeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountSendPasswordResetCodeError = PostApiAccountSendPasswordResetCodeErrors[keyof PostApiAccountSendPasswordResetCodeErrors];

export type PostApiAccountSendPasswordResetCodeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountVerifyPasswordResetTokenData = {
    body?: VerifyPasswordResetTokenInput;
    path?: never;
    query?: never;
    url: '/api/account/verify-password-reset-token';
};

export type PostApiAccountVerifyPasswordResetTokenErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountVerifyPasswordResetTokenError = PostApiAccountVerifyPasswordResetTokenErrors[keyof PostApiAccountVerifyPasswordResetTokenErrors];

export type PostApiAccountVerifyPasswordResetTokenResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAccountVerifyPasswordResetTokenResponse = PostApiAccountVerifyPasswordResetTokenResponses[keyof PostApiAccountVerifyPasswordResetTokenResponses];

export type PostApiAccountResetPasswordData = {
    body?: ResetPasswordDto;
    path?: never;
    query?: never;
    url: '/api/account/reset-password';
};

export type PostApiAccountResetPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountResetPasswordError = PostApiAccountResetPasswordErrors[keyof PostApiAccountResetPasswordErrors];

export type PostApiAccountResetPasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/';
};

export type GetResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin';
};

export type GetAdminResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApplicationData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/application';
};

export type GetApplicationResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApplicationCreateData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/application/create';
};

export type GetApplicationCreateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApplicationStatusData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/application/status';
};

export type GetApplicationStatusResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApplicationListData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/application/list';
};

export type GetApplicationListResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApplicationDraftData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/application/draft';
};

export type GetApplicationDraftResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApplicationByIdEditData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/application/{id}/edit';
};

export type GetApplicationByIdEditResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApprovalData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/approval';
};

export type GetApprovalResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApprovalHistoryData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/approval/history';
};

export type GetApprovalHistoryResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetJettyData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/jetty';
};

export type GetJettyResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetJettyScheduleData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/jetty/schedule';
};

export type GetJettyScheduleResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetJettyDockedVesselData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/jetty/docked-vessel';
};

export type GetJettyDockedVesselResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetSettingsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/settings';
};

export type GetSettingsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetReportData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/report';
};

export type GetReportResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApprovalTemplateData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/approval-template';
};

export type GetApprovalTemplateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetDocumentTemplateData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/document-template';
};

export type GetDocumentTemplateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentData = {
    body?: ApplicationDocumentGenerationDto;
    path?: never;
    query?: never;
    url: '/api/idjas/application-document/generate-application-document-as-attachment';
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentError = PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentErrors[keyof PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentErrors];

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentResponses = {
    /**
     * OK
     */
    200: FileUploadResultDto;
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentResponse = PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentResponses[keyof PostApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachmentResponses];

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentData = {
    body?: ApplicationDocumentGenerationDto;
    path?: never;
    query?: never;
    url: '/api/idjas/application-document/generate-application-document';
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentError = PostApiIdjasApplicationDocumentGenerateApplicationDocumentErrors[keyof PostApiIdjasApplicationDocumentGenerateApplicationDocumentErrors];

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentResponses = {
    /**
     * OK
     */
    200: FileDto;
};

export type PostApiIdjasApplicationDocumentGenerateApplicationDocumentResponse = PostApiIdjasApplicationDocumentGenerateApplicationDocumentResponses[keyof PostApiIdjasApplicationDocumentGenerateApplicationDocumentResponses];

export type PostApiIdjasApprovalSubmitData = {
    body?: SubmitApprovalDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval/submit';
};

export type PostApiIdjasApprovalSubmitResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdjasApprovalApproveByIdData = {
    body?: ApprovalActionDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval/approve/{id}';
};

export type PostApiIdjasApprovalApproveByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdjasApprovalRejectByIdData = {
    body?: ApprovalActionDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval/reject/{id}';
};

export type PostApiIdjasApprovalRejectByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasApprovalApproverData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/approval-approver';
};

export type GetApiIdjasApprovalApproverErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalApproverError = GetApiIdjasApprovalApproverErrors[keyof GetApiIdjasApprovalApproverErrors];

export type GetApiIdjasApprovalApproverResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalApproverDto;
};

export type GetApiIdjasApprovalApproverResponse = GetApiIdjasApprovalApproverResponses[keyof GetApiIdjasApprovalApproverResponses];

export type PostApiIdjasApprovalApproverData = {
    body?: CreateUpdateApprovalApproverDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-approver';
};

export type PostApiIdjasApprovalApproverErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalApproverError = PostApiIdjasApprovalApproverErrors[keyof PostApiIdjasApprovalApproverErrors];

export type PostApiIdjasApprovalApproverResponses = {
    /**
     * OK
     */
    200: ApprovalApproverDto;
};

export type PostApiIdjasApprovalApproverResponse = PostApiIdjasApprovalApproverResponses[keyof PostApiIdjasApprovalApproverResponses];

export type DeleteApiIdjasApprovalApproverByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-approver/{id}';
};

export type DeleteApiIdjasApprovalApproverByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasApprovalApproverByIdError = DeleteApiIdjasApprovalApproverByIdErrors[keyof DeleteApiIdjasApprovalApproverByIdErrors];

export type DeleteApiIdjasApprovalApproverByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasApprovalApproverByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-approver/{id}';
};

export type GetApiIdjasApprovalApproverByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalApproverByIdError = GetApiIdjasApprovalApproverByIdErrors[keyof GetApiIdjasApprovalApproverByIdErrors];

export type GetApiIdjasApprovalApproverByIdResponses = {
    /**
     * OK
     */
    200: ApprovalApproverDto;
};

export type GetApiIdjasApprovalApproverByIdResponse = GetApiIdjasApprovalApproverByIdResponses[keyof GetApiIdjasApprovalApproverByIdResponses];

export type PutApiIdjasApprovalApproverByIdData = {
    body?: CreateUpdateApprovalApproverDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-approver/{id}';
};

export type PutApiIdjasApprovalApproverByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasApprovalApproverByIdError = PutApiIdjasApprovalApproverByIdErrors[keyof PutApiIdjasApprovalApproverByIdErrors];

export type PutApiIdjasApprovalApproverByIdResponses = {
    /**
     * OK
     */
    200: ApprovalApproverDto;
};

export type PutApiIdjasApprovalApproverByIdResponse = PutApiIdjasApprovalApproverByIdResponses[keyof PutApiIdjasApprovalApproverByIdResponses];

export type PostApiIdjasApprovalApproverFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-approver/filter-list';
};

export type PostApiIdjasApprovalApproverFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalApproverFilterListError = PostApiIdjasApprovalApproverFilterListErrors[keyof PostApiIdjasApprovalApproverFilterListErrors];

export type PostApiIdjasApprovalApproverFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalApproverDto;
};

export type PostApiIdjasApprovalApproverFilterListResponse = PostApiIdjasApprovalApproverFilterListResponses[keyof PostApiIdjasApprovalApproverFilterListResponses];

export type GetApiIdjasApprovalCriteriaData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/approval-criteria';
};

export type GetApiIdjasApprovalCriteriaErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalCriteriaError = GetApiIdjasApprovalCriteriaErrors[keyof GetApiIdjasApprovalCriteriaErrors];

export type GetApiIdjasApprovalCriteriaResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalCriteriaDto;
};

export type GetApiIdjasApprovalCriteriaResponse = GetApiIdjasApprovalCriteriaResponses[keyof GetApiIdjasApprovalCriteriaResponses];

export type PostApiIdjasApprovalCriteriaData = {
    body?: CreateUpdateApprovalCriteriaDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-criteria';
};

export type PostApiIdjasApprovalCriteriaErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalCriteriaError = PostApiIdjasApprovalCriteriaErrors[keyof PostApiIdjasApprovalCriteriaErrors];

export type PostApiIdjasApprovalCriteriaResponses = {
    /**
     * OK
     */
    200: ApprovalCriteriaDto;
};

export type PostApiIdjasApprovalCriteriaResponse = PostApiIdjasApprovalCriteriaResponses[keyof PostApiIdjasApprovalCriteriaResponses];

export type DeleteApiIdjasApprovalCriteriaByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-criteria/{id}';
};

export type DeleteApiIdjasApprovalCriteriaByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasApprovalCriteriaByIdError = DeleteApiIdjasApprovalCriteriaByIdErrors[keyof DeleteApiIdjasApprovalCriteriaByIdErrors];

export type DeleteApiIdjasApprovalCriteriaByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasApprovalCriteriaByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-criteria/{id}';
};

export type GetApiIdjasApprovalCriteriaByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalCriteriaByIdError = GetApiIdjasApprovalCriteriaByIdErrors[keyof GetApiIdjasApprovalCriteriaByIdErrors];

export type GetApiIdjasApprovalCriteriaByIdResponses = {
    /**
     * OK
     */
    200: ApprovalCriteriaDto;
};

export type GetApiIdjasApprovalCriteriaByIdResponse = GetApiIdjasApprovalCriteriaByIdResponses[keyof GetApiIdjasApprovalCriteriaByIdResponses];

export type PutApiIdjasApprovalCriteriaByIdData = {
    body?: CreateUpdateApprovalCriteriaDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-criteria/{id}';
};

export type PutApiIdjasApprovalCriteriaByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasApprovalCriteriaByIdError = PutApiIdjasApprovalCriteriaByIdErrors[keyof PutApiIdjasApprovalCriteriaByIdErrors];

export type PutApiIdjasApprovalCriteriaByIdResponses = {
    /**
     * OK
     */
    200: ApprovalCriteriaDto;
};

export type PutApiIdjasApprovalCriteriaByIdResponse = PutApiIdjasApprovalCriteriaByIdResponses[keyof PutApiIdjasApprovalCriteriaByIdResponses];

export type PostApiIdjasApprovalCriteriaFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-criteria/filter-list';
};

export type PostApiIdjasApprovalCriteriaFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalCriteriaFilterListError = PostApiIdjasApprovalCriteriaFilterListErrors[keyof PostApiIdjasApprovalCriteriaFilterListErrors];

export type PostApiIdjasApprovalCriteriaFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalCriteriaDto;
};

export type PostApiIdjasApprovalCriteriaFilterListResponse = PostApiIdjasApprovalCriteriaFilterListResponses[keyof PostApiIdjasApprovalCriteriaFilterListResponses];

export type GetApiIdjasApprovalDelegationData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/approval-delegation';
};

export type GetApiIdjasApprovalDelegationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalDelegationError = GetApiIdjasApprovalDelegationErrors[keyof GetApiIdjasApprovalDelegationErrors];

export type GetApiIdjasApprovalDelegationResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalDelegationDto;
};

export type GetApiIdjasApprovalDelegationResponse = GetApiIdjasApprovalDelegationResponses[keyof GetApiIdjasApprovalDelegationResponses];

export type PostApiIdjasApprovalDelegationData = {
    body?: CreateUpdateApprovalDelegationDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-delegation';
};

export type PostApiIdjasApprovalDelegationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalDelegationError = PostApiIdjasApprovalDelegationErrors[keyof PostApiIdjasApprovalDelegationErrors];

export type PostApiIdjasApprovalDelegationResponses = {
    /**
     * OK
     */
    200: ApprovalDelegationDto;
};

export type PostApiIdjasApprovalDelegationResponse = PostApiIdjasApprovalDelegationResponses[keyof PostApiIdjasApprovalDelegationResponses];

export type DeleteApiIdjasApprovalDelegationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-delegation/{id}';
};

export type DeleteApiIdjasApprovalDelegationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasApprovalDelegationByIdError = DeleteApiIdjasApprovalDelegationByIdErrors[keyof DeleteApiIdjasApprovalDelegationByIdErrors];

export type DeleteApiIdjasApprovalDelegationByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasApprovalDelegationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-delegation/{id}';
};

export type GetApiIdjasApprovalDelegationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalDelegationByIdError = GetApiIdjasApprovalDelegationByIdErrors[keyof GetApiIdjasApprovalDelegationByIdErrors];

export type GetApiIdjasApprovalDelegationByIdResponses = {
    /**
     * OK
     */
    200: ApprovalDelegationDto;
};

export type GetApiIdjasApprovalDelegationByIdResponse = GetApiIdjasApprovalDelegationByIdResponses[keyof GetApiIdjasApprovalDelegationByIdResponses];

export type PutApiIdjasApprovalDelegationByIdData = {
    body?: CreateUpdateApprovalDelegationDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-delegation/{id}';
};

export type PutApiIdjasApprovalDelegationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasApprovalDelegationByIdError = PutApiIdjasApprovalDelegationByIdErrors[keyof PutApiIdjasApprovalDelegationByIdErrors];

export type PutApiIdjasApprovalDelegationByIdResponses = {
    /**
     * OK
     */
    200: ApprovalDelegationDto;
};

export type PutApiIdjasApprovalDelegationByIdResponse = PutApiIdjasApprovalDelegationByIdResponses[keyof PutApiIdjasApprovalDelegationByIdResponses];

export type PostApiIdjasApprovalDelegationFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-delegation/filter-list';
};

export type PostApiIdjasApprovalDelegationFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalDelegationFilterListError = PostApiIdjasApprovalDelegationFilterListErrors[keyof PostApiIdjasApprovalDelegationFilterListErrors];

export type PostApiIdjasApprovalDelegationFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalDelegationDto;
};

export type PostApiIdjasApprovalDelegationFilterListResponse = PostApiIdjasApprovalDelegationFilterListResponses[keyof PostApiIdjasApprovalDelegationFilterListResponses];

export type GetApiIdjasApprovalStageData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/approval-stage';
};

export type GetApiIdjasApprovalStageErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalStageError = GetApiIdjasApprovalStageErrors[keyof GetApiIdjasApprovalStageErrors];

export type GetApiIdjasApprovalStageResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalStageDto;
};

export type GetApiIdjasApprovalStageResponse = GetApiIdjasApprovalStageResponses[keyof GetApiIdjasApprovalStageResponses];

export type PostApiIdjasApprovalStageData = {
    body?: CreateUpdateApprovalStageDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-stage';
};

export type PostApiIdjasApprovalStageErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalStageError = PostApiIdjasApprovalStageErrors[keyof PostApiIdjasApprovalStageErrors];

export type PostApiIdjasApprovalStageResponses = {
    /**
     * OK
     */
    200: ApprovalStageDto;
};

export type PostApiIdjasApprovalStageResponse = PostApiIdjasApprovalStageResponses[keyof PostApiIdjasApprovalStageResponses];

export type DeleteApiIdjasApprovalStageByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-stage/{id}';
};

export type DeleteApiIdjasApprovalStageByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasApprovalStageByIdError = DeleteApiIdjasApprovalStageByIdErrors[keyof DeleteApiIdjasApprovalStageByIdErrors];

export type DeleteApiIdjasApprovalStageByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasApprovalStageByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-stage/{id}';
};

export type GetApiIdjasApprovalStageByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalStageByIdError = GetApiIdjasApprovalStageByIdErrors[keyof GetApiIdjasApprovalStageByIdErrors];

export type GetApiIdjasApprovalStageByIdResponses = {
    /**
     * OK
     */
    200: ApprovalStageDto;
};

export type GetApiIdjasApprovalStageByIdResponse = GetApiIdjasApprovalStageByIdResponses[keyof GetApiIdjasApprovalStageByIdResponses];

export type PutApiIdjasApprovalStageByIdData = {
    body?: CreateUpdateApprovalStageDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-stage/{id}';
};

export type PutApiIdjasApprovalStageByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasApprovalStageByIdError = PutApiIdjasApprovalStageByIdErrors[keyof PutApiIdjasApprovalStageByIdErrors];

export type PutApiIdjasApprovalStageByIdResponses = {
    /**
     * OK
     */
    200: ApprovalStageDto;
};

export type PutApiIdjasApprovalStageByIdResponse = PutApiIdjasApprovalStageByIdResponses[keyof PutApiIdjasApprovalStageByIdResponses];

export type PostApiIdjasApprovalStageFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-stage/filter-list';
};

export type PostApiIdjasApprovalStageFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalStageFilterListError = PostApiIdjasApprovalStageFilterListErrors[keyof PostApiIdjasApprovalStageFilterListErrors];

export type PostApiIdjasApprovalStageFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalStageDto;
};

export type PostApiIdjasApprovalStageFilterListResponse = PostApiIdjasApprovalStageFilterListResponses[keyof PostApiIdjasApprovalStageFilterListResponses];

export type PostApiIdjasApprovalStageSubmitForApprovalData = {
    body?: SubmitApprovalDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-stage/submit-for-approval';
};

export type PostApiIdjasApprovalStageSubmitForApprovalErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalStageSubmitForApprovalError = PostApiIdjasApprovalStageSubmitForApprovalErrors[keyof PostApiIdjasApprovalStageSubmitForApprovalErrors];

export type PostApiIdjasApprovalStageSubmitForApprovalResponses = {
    /**
     * OK
     */
    200: Array<ApprovalStageDto>;
};

export type PostApiIdjasApprovalStageSubmitForApprovalResponse = PostApiIdjasApprovalStageSubmitForApprovalResponses[keyof PostApiIdjasApprovalStageSubmitForApprovalResponses];

export type GetApiIdjasApprovalTemplateData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/approval-template';
};

export type GetApiIdjasApprovalTemplateErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalTemplateError = GetApiIdjasApprovalTemplateErrors[keyof GetApiIdjasApprovalTemplateErrors];

export type GetApiIdjasApprovalTemplateResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalTemplateDto;
};

export type GetApiIdjasApprovalTemplateResponse = GetApiIdjasApprovalTemplateResponses[keyof GetApiIdjasApprovalTemplateResponses];

export type PostApiIdjasApprovalTemplateData = {
    body?: CreateUpdateApprovalTemplateDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-template';
};

export type PostApiIdjasApprovalTemplateErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalTemplateError = PostApiIdjasApprovalTemplateErrors[keyof PostApiIdjasApprovalTemplateErrors];

export type PostApiIdjasApprovalTemplateResponses = {
    /**
     * OK
     */
    200: ApprovalTemplateDto;
};

export type PostApiIdjasApprovalTemplateResponse = PostApiIdjasApprovalTemplateResponses[keyof PostApiIdjasApprovalTemplateResponses];

export type DeleteApiIdjasApprovalTemplateByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-template/{id}';
};

export type DeleteApiIdjasApprovalTemplateByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasApprovalTemplateByIdError = DeleteApiIdjasApprovalTemplateByIdErrors[keyof DeleteApiIdjasApprovalTemplateByIdErrors];

export type DeleteApiIdjasApprovalTemplateByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasApprovalTemplateByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-template/{id}';
};

export type GetApiIdjasApprovalTemplateByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasApprovalTemplateByIdError = GetApiIdjasApprovalTemplateByIdErrors[keyof GetApiIdjasApprovalTemplateByIdErrors];

export type GetApiIdjasApprovalTemplateByIdResponses = {
    /**
     * OK
     */
    200: ApprovalTemplateDto;
};

export type GetApiIdjasApprovalTemplateByIdResponse = GetApiIdjasApprovalTemplateByIdResponses[keyof GetApiIdjasApprovalTemplateByIdResponses];

export type PutApiIdjasApprovalTemplateByIdData = {
    body?: CreateUpdateApprovalTemplateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/approval-template/{id}';
};

export type PutApiIdjasApprovalTemplateByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasApprovalTemplateByIdError = PutApiIdjasApprovalTemplateByIdErrors[keyof PutApiIdjasApprovalTemplateByIdErrors];

export type PutApiIdjasApprovalTemplateByIdResponses = {
    /**
     * OK
     */
    200: ApprovalTemplateDto;
};

export type PutApiIdjasApprovalTemplateByIdResponse = PutApiIdjasApprovalTemplateByIdResponses[keyof PutApiIdjasApprovalTemplateByIdResponses];

export type PostApiIdjasApprovalTemplateFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/approval-template/filter-list';
};

export type PostApiIdjasApprovalTemplateFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasApprovalTemplateFilterListError = PostApiIdjasApprovalTemplateFilterListErrors[keyof PostApiIdjasApprovalTemplateFilterListErrors];

export type PostApiIdjasApprovalTemplateFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfApprovalTemplateDto;
};

export type PostApiIdjasApprovalTemplateFilterListResponse = PostApiIdjasApprovalTemplateFilterListResponses[keyof PostApiIdjasApprovalTemplateFilterListResponses];

export type GetApiAppToAppDataData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/app-to-app/data';
};

export type GetApiAppToAppDataResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAppToAppDataData = {
    body?: unknown;
    path?: never;
    query?: never;
    url: '/api/app-to-app/data';
};

export type PostApiAppToAppDataResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAppToAppCustomData = {
    body?: never;
    path?: never;
    query?: {
        appUrl?: string;
        endpoint?: string;
    };
    url: '/api/app-to-app/custom';
};

export type GetApiAppToAppCustomResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAttachmentDownloadByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/attachment/download/{id}';
};

export type GetApiAttachmentDownloadByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentDownloadByIdError = GetApiAttachmentDownloadByIdErrors[keyof GetApiAttachmentDownloadByIdErrors];

export type GetApiAttachmentStreamByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/attachment/stream/{id}';
};

export type GetApiAttachmentStreamByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentStreamByIdError = GetApiAttachmentStreamByIdErrors[keyof GetApiAttachmentStreamByIdErrors];

export type PostApiAttachmentUploadData = {
    body?: FileUploadInputDto;
    path?: never;
    query?: never;
    url: '/api/attachment/upload';
};

export type PostApiAttachmentUploadErrors = {
    /**
     * Bad Request
     */
    400: unknown;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Unsupported Media Type
     */
    415: unknown;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAttachmentUploadError = PostApiAttachmentUploadErrors[keyof PostApiAttachmentUploadErrors];

export type PostApiAttachmentUploadResponses = {
    /**
     * OK
     */
    200: FileUploadResultDto;
};

export type PostApiAttachmentUploadResponse = PostApiAttachmentUploadResponses[keyof PostApiAttachmentUploadResponses];

export type GetApiAttachmentByReferenceData = {
    body?: never;
    path?: never;
    query?: {
        referenceId?: string;
        referenceType?: string;
    };
    url: '/api/attachment/by-reference';
};

export type GetApiAttachmentByReferenceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentByReferenceError = GetApiAttachmentByReferenceErrors[keyof GetApiAttachmentByReferenceErrors];

export type GetApiAttachmentByReferenceResponses = {
    /**
     * OK
     */
    200: Array<FileUploadResultDto>;
};

export type GetApiAttachmentByReferenceResponse = GetApiAttachmentByReferenceResponses[keyof GetApiAttachmentByReferenceResponses];

export type DeleteApiAttachmentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/attachment/{id}';
};

export type DeleteApiAttachmentByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiAttachmentByIdError = DeleteApiAttachmentByIdErrors[keyof DeleteApiAttachmentByIdErrors];

export type DeleteApiAttachmentByIdResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type DeleteApiAttachmentByIdResponse = DeleteApiAttachmentByIdResponses[keyof DeleteApiAttachmentByIdResponses];

export type PostApiAttachmentBulkDownloadData = {
    body?: BulkDownloadDto;
    path?: never;
    query?: never;
    url: '/api/attachment/bulk-download';
};

export type PostApiAttachmentBulkDownloadErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAttachmentBulkDownloadError = PostApiAttachmentBulkDownloadErrors[keyof PostApiAttachmentBulkDownloadErrors];

export type PostApiAttachmentUploadFormData = {
    body?: {
        File: Blob | File;
        Description?: string;
        ReferenceId?: string;
        ReferenceType?: string;
    };
    path?: never;
    query?: never;
    url: '/api/attachment/upload-form';
};

export type PostApiAttachmentUploadFormErrors = {
    /**
     * Bad Request
     */
    400: unknown;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Unsupported Media Type
     */
    415: unknown;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAttachmentUploadFormError = PostApiAttachmentUploadFormErrors[keyof PostApiAttachmentUploadFormErrors];

export type PostApiAttachmentUploadFormResponses = {
    /**
     * OK
     */
    200: FileUploadResultDto;
};

export type PostApiAttachmentUploadFormResponse = PostApiAttachmentUploadFormResponses[keyof PostApiAttachmentUploadFormResponses];

export type GetApiDashboardStatisticsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/dashboard/statistics';
};

export type GetApiDashboardStatisticsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiDashboardPendingApprovalsData = {
    body?: never;
    path?: never;
    query?: {
        maxResults?: number;
    };
    url: '/api/dashboard/pending-approvals';
};

export type GetApiDashboardPendingApprovalsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiDashboardJettyStatusData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/dashboard/jetty-status';
};

export type GetApiDashboardJettyStatusResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiDebugCurrentUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/debug/current-user';
};

export type GetApiDebugCurrentUserResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiDebugClearUserCacheData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/debug/clear-user-cache';
};

export type PostApiDebugClearUserCacheResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiDebugUpdateUserNameData = {
    body?: UpdateUserNameRequest;
    path?: never;
    query?: never;
    url: '/api/debug/update-user-name';
};

export type PostApiDebugUpdateUserNameResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiDebugForceUserSyncData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/debug/force-user-sync';
};

export type PostApiDebugForceUserSyncResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdjasDocumentTemplatesUploadData = {
    body?: {
        Name: string;
        DocumentType: DocumentType;
        Description?: string;
        IsDefault?: boolean;
        File: Blob | File;
    };
    path?: never;
    query?: never;
    url: '/api/idjas/document/templates/upload';
};

export type PostApiIdjasDocumentTemplatesUploadErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasDocumentTemplatesUploadError = PostApiIdjasDocumentTemplatesUploadErrors[keyof PostApiIdjasDocumentTemplatesUploadErrors];

export type PostApiIdjasDocumentTemplatesUploadResponses = {
    /**
     * OK
     */
    200: DocumentTemplateDto;
};

export type PostApiIdjasDocumentTemplatesUploadResponse = PostApiIdjasDocumentTemplatesUploadResponses[keyof PostApiIdjasDocumentTemplatesUploadResponses];

export type PostApiIdjasDocumentGenerateApplicationData = {
    body?: ApplicationDocumentGenerationDto;
    path?: never;
    query?: never;
    url: '/api/idjas/document/generate-application';
};

export type PostApiIdjasDocumentGenerateApplicationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasDocumentGenerateApplicationError = PostApiIdjasDocumentGenerateApplicationErrors[keyof PostApiIdjasDocumentGenerateApplicationErrors];

export type GetApiIdjasDocumentTemplatesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/idjas/document/templates';
};

export type GetApiIdjasDocumentTemplatesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasDocumentTemplatesError = GetApiIdjasDocumentTemplatesErrors[keyof GetApiIdjasDocumentTemplatesErrors];

export type GetApiIdjasDocumentTemplatesResponses = {
    /**
     * OK
     */
    200: Array<DocumentTemplateDto>;
};

export type GetApiIdjasDocumentTemplatesResponse = GetApiIdjasDocumentTemplatesResponses[keyof GetApiIdjasDocumentTemplatesResponses];

export type GetApiIdjasDocumentTemplatesByTypeData = {
    body?: never;
    path?: never;
    query?: {
        documentType?: DocumentType;
    };
    url: '/api/idjas/document/templates-by-type';
};

export type GetApiIdjasDocumentTemplatesByTypeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasDocumentTemplatesByTypeError = GetApiIdjasDocumentTemplatesByTypeErrors[keyof GetApiIdjasDocumentTemplatesByTypeErrors];

export type GetApiIdjasDocumentTemplatesByTypeResponses = {
    /**
     * OK
     */
    200: Array<DocumentTemplateDto>;
};

export type GetApiIdjasDocumentTemplatesByTypeResponse = GetApiIdjasDocumentTemplatesByTypeResponses[keyof GetApiIdjasDocumentTemplatesByTypeResponses];

export type GetApiIdjasDocumentByIdTemplateByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/document/{id}/template-by-id';
};

export type GetApiIdjasDocumentByIdTemplateByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasDocumentByIdTemplateByIdError = GetApiIdjasDocumentByIdTemplateByIdErrors[keyof GetApiIdjasDocumentByIdTemplateByIdErrors];

export type GetApiIdjasDocumentByIdTemplateByIdResponses = {
    /**
     * OK
     */
    200: DocumentTemplateDto;
};

export type GetApiIdjasDocumentByIdTemplateByIdResponse = GetApiIdjasDocumentByIdTemplateByIdResponses[keyof GetApiIdjasDocumentByIdTemplateByIdResponses];

export type PostApiIdjasDocumentTemplateData = {
    body?: CreateDocumentTemplateDto;
    path?: never;
    query?: never;
    url: '/api/idjas/document/template';
};

export type PostApiIdjasDocumentTemplateErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasDocumentTemplateError = PostApiIdjasDocumentTemplateErrors[keyof PostApiIdjasDocumentTemplateErrors];

export type PostApiIdjasDocumentTemplateResponses = {
    /**
     * OK
     */
    200: DocumentTemplateDto;
};

export type PostApiIdjasDocumentTemplateResponse = PostApiIdjasDocumentTemplateResponses[keyof PostApiIdjasDocumentTemplateResponses];

export type PostApiIdjasDocumentUploadTemplateData = {
    body?: UploadDocumentTemplateDto;
    path?: never;
    query?: never;
    url: '/api/idjas/document/upload-template';
};

export type PostApiIdjasDocumentUploadTemplateErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasDocumentUploadTemplateError = PostApiIdjasDocumentUploadTemplateErrors[keyof PostApiIdjasDocumentUploadTemplateErrors];

export type PostApiIdjasDocumentUploadTemplateResponses = {
    /**
     * OK
     */
    200: DocumentTemplateDto;
};

export type PostApiIdjasDocumentUploadTemplateResponse = PostApiIdjasDocumentUploadTemplateResponses[keyof PostApiIdjasDocumentUploadTemplateResponses];

export type DeleteApiIdjasDocumentByIdTemplateData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/document/{id}/template';
};

export type DeleteApiIdjasDocumentByIdTemplateErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasDocumentByIdTemplateError = DeleteApiIdjasDocumentByIdTemplateErrors[keyof DeleteApiIdjasDocumentByIdTemplateErrors];

export type DeleteApiIdjasDocumentByIdTemplateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PutApiIdjasDocumentByIdTemplateData = {
    body?: UpdateDocumentTemplateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/document/{id}/template';
};

export type PutApiIdjasDocumentByIdTemplateErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasDocumentByIdTemplateError = PutApiIdjasDocumentByIdTemplateErrors[keyof PutApiIdjasDocumentByIdTemplateErrors];

export type PutApiIdjasDocumentByIdTemplateResponses = {
    /**
     * OK
     */
    200: DocumentTemplateDto;
};

export type PutApiIdjasDocumentByIdTemplateResponse = PutApiIdjasDocumentByIdTemplateResponses[keyof PutApiIdjasDocumentByIdTemplateResponses];

export type PostApiIdjasDocumentFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/document/filter-list';
};

export type PostApiIdjasDocumentFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasDocumentFilterListError = PostApiIdjasDocumentFilterListErrors[keyof PostApiIdjasDocumentFilterListErrors];

export type PostApiIdjasDocumentFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDocumentTemplateDto;
};

export type PostApiIdjasDocumentFilterListResponse = PostApiIdjasDocumentFilterListResponses[keyof PostApiIdjasDocumentFilterListResponses];

export type PostApiIdjasDocumentConvertDocxToPdfData = {
    body?: DocxToPdfConversionDto;
    path?: never;
    query?: never;
    url: '/api/idjas/document/convert-docx-to-pdf';
};

export type PostApiIdjasDocumentConvertDocxToPdfErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasDocumentConvertDocxToPdfError = PostApiIdjasDocumentConvertDocxToPdfErrors[keyof PostApiIdjasDocumentConvertDocxToPdfErrors];

export type PostApiIdjasDocumentConvertDocxToPdfResponses = {
    /**
     * OK
     */
    200: FileDto;
};

export type PostApiIdjasDocumentConvertDocxToPdfResponse = PostApiIdjasDocumentConvertDocxToPdfResponses[keyof PostApiIdjasDocumentConvertDocxToPdfResponses];

export type PostApiAccountDynamicClaimsRefreshData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/dynamic-claims/refresh';
};

export type PostApiAccountDynamicClaimsRefreshErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountDynamicClaimsRefreshError = PostApiAccountDynamicClaimsRefreshErrors[keyof PostApiAccountDynamicClaimsRefreshErrors];

export type PostApiAccountDynamicClaimsRefreshResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiEkbVesselHeaderData = {
    body?: VesselListRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/vessel-header';
};

export type PostApiEkbVesselHeaderResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfVesselHeaderDto;
};

export type PostApiEkbVesselHeaderResponse = PostApiEkbVesselHeaderResponses[keyof PostApiEkbVesselHeaderResponses];

export type PostApiEkbAgentData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/agent';
};

export type PostApiEkbAgentResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfAgentDto;
};

export type PostApiEkbAgentResponse = PostApiEkbAgentResponses[keyof PostApiEkbAgentResponses];

export type PostApiEkbTenantData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/tenant';
};

export type PostApiEkbTenantResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfMasterTenantDto;
};

export type PostApiEkbTenantResponse = PostApiEkbTenantResponses[keyof PostApiEkbTenantResponses];

export type PostApiEkbJettyData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/jetty';
};

export type PostApiEkbJettyResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyDto;
};

export type PostApiEkbJettyResponse = PostApiEkbJettyResponses[keyof PostApiEkbJettyResponses];

export type PostApiEkbCargoData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/cargo';
};

export type PostApiEkbCargoResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfCargoDto;
};

export type PostApiEkbCargoResponse = PostApiEkbCargoResponses[keyof PostApiEkbCargoResponses];

export type PostApiEkbBusinessPartnerData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/business-partner';
};

export type PostApiEkbBusinessPartnerResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBusinessPartnerDto;
};

export type PostApiEkbBusinessPartnerResponse = PostApiEkbBusinessPartnerResponses[keyof PostApiEkbBusinessPartnerResponses];

export type PostApiEkbDestinationPortData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/destination-port';
};

export type PostApiEkbDestinationPortResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDestinationPortDto;
};

export type PostApiEkbDestinationPortResponse = PostApiEkbDestinationPortResponses[keyof PostApiEkbDestinationPortResponses];

export type PostApiEkbSurveyorData = {
    body?: FilterRequestDto;
    path?: never;
    query?: never;
    url: '/api/Ekb/surveyor';
};

export type PostApiEkbSurveyorResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfSurveyorDto;
};

export type PostApiEkbSurveyorResponse = PostApiEkbSurveyorResponses[keyof PostApiEkbSurveyorResponses];

export type PostApiExternalVesselSearchVesselsData = {
    body?: VesselQueryRequestDto;
    path?: never;
    query?: never;
    url: '/api/ExternalVessel/search-vessels';
};

export type PostApiExternalVesselSearchVesselsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdentityServerUserQueryData = {
    body?: GetUsersInput;
    path?: never;
    query?: never;
    url: '/api/IdentityServer/user-query';
};

export type PostApiIdentityServerUserQueryResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfExtendedIdentityUserDto;
};

export type PostApiIdentityServerUserQueryResponse = PostApiIdentityServerUserQueryResponses[keyof PostApiIdentityServerUserQueryResponses];

export type PostApiIdjasJettyRequestGenerateNextDocNumData = {
    body?: never;
    path?: never;
    query?: {
        postDate?: string;
    };
    url: '/api/idjas/jetty-request/generate-next-doc-num';
};

export type PostApiIdjasJettyRequestGenerateNextDocNumErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasJettyRequestGenerateNextDocNumError = PostApiIdjasJettyRequestGenerateNextDocNumErrors[keyof PostApiIdjasJettyRequestGenerateNextDocNumErrors];

export type PostApiIdjasJettyRequestGenerateNextDocNumResponses = {
    /**
     * OK
     */
    200: number;
};

export type PostApiIdjasJettyRequestGenerateNextDocNumResponse = PostApiIdjasJettyRequestGenerateNextDocNumResponses[keyof PostApiIdjasJettyRequestGenerateNextDocNumResponses];

export type GetApiIdjasJettyRequestData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/jetty-request';
};

export type GetApiIdjasJettyRequestErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasJettyRequestError = GetApiIdjasJettyRequestErrors[keyof GetApiIdjasJettyRequestErrors];

export type GetApiIdjasJettyRequestResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyRequestDto;
};

export type GetApiIdjasJettyRequestResponse = GetApiIdjasJettyRequestResponses[keyof GetApiIdjasJettyRequestResponses];

export type PostApiIdjasJettyRequestData = {
    body?: CreateUpdateJettyRequestDto;
    path?: never;
    query?: never;
    url: '/api/idjas/jetty-request';
};

export type PostApiIdjasJettyRequestErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasJettyRequestError = PostApiIdjasJettyRequestErrors[keyof PostApiIdjasJettyRequestErrors];

export type PostApiIdjasJettyRequestResponses = {
    /**
     * OK
     */
    200: JettyRequestDto;
};

export type PostApiIdjasJettyRequestResponse = PostApiIdjasJettyRequestResponses[keyof PostApiIdjasJettyRequestResponses];

export type DeleteApiIdjasJettyRequestByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/jetty-request/{id}';
};

export type DeleteApiIdjasJettyRequestByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasJettyRequestByIdError = DeleteApiIdjasJettyRequestByIdErrors[keyof DeleteApiIdjasJettyRequestByIdErrors];

export type DeleteApiIdjasJettyRequestByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasJettyRequestByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/jetty-request/{id}';
};

export type GetApiIdjasJettyRequestByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasJettyRequestByIdError = GetApiIdjasJettyRequestByIdErrors[keyof GetApiIdjasJettyRequestByIdErrors];

export type GetApiIdjasJettyRequestByIdResponses = {
    /**
     * OK
     */
    200: JettyRequestDto;
};

export type GetApiIdjasJettyRequestByIdResponse = GetApiIdjasJettyRequestByIdResponses[keyof GetApiIdjasJettyRequestByIdResponses];

export type PutApiIdjasJettyRequestByIdData = {
    body?: CreateUpdateJettyRequestDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/jetty-request/{id}';
};

export type PutApiIdjasJettyRequestByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasJettyRequestByIdError = PutApiIdjasJettyRequestByIdErrors[keyof PutApiIdjasJettyRequestByIdErrors];

export type PutApiIdjasJettyRequestByIdResponses = {
    /**
     * OK
     */
    200: JettyRequestDto;
};

export type PutApiIdjasJettyRequestByIdResponse = PutApiIdjasJettyRequestByIdResponses[keyof PutApiIdjasJettyRequestByIdResponses];

export type PostApiIdjasJettyRequestFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/jetty-request/filter-list';
};

export type PostApiIdjasJettyRequestFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasJettyRequestFilterListError = PostApiIdjasJettyRequestFilterListErrors[keyof PostApiIdjasJettyRequestFilterListErrors];

export type PostApiIdjasJettyRequestFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyRequestDto;
};

export type PostApiIdjasJettyRequestFilterListResponse = PostApiIdjasJettyRequestFilterListResponses[keyof PostApiIdjasJettyRequestFilterListResponses];

export type GetApiIdjasJettyRequestItemData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/idjas/jetty-request-item';
};

export type GetApiIdjasJettyRequestItemErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasJettyRequestItemError = GetApiIdjasJettyRequestItemErrors[keyof GetApiIdjasJettyRequestItemErrors];

export type GetApiIdjasJettyRequestItemResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyRequestItemDto;
};

export type GetApiIdjasJettyRequestItemResponse = GetApiIdjasJettyRequestItemResponses[keyof GetApiIdjasJettyRequestItemResponses];

export type PostApiIdjasJettyRequestItemData = {
    body?: CreateUpdateJettyRequestItemDto;
    path?: never;
    query?: never;
    url: '/api/idjas/jetty-request-item';
};

export type PostApiIdjasJettyRequestItemErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasJettyRequestItemError = PostApiIdjasJettyRequestItemErrors[keyof PostApiIdjasJettyRequestItemErrors];

export type PostApiIdjasJettyRequestItemResponses = {
    /**
     * OK
     */
    200: JettyRequestItemDto;
};

export type PostApiIdjasJettyRequestItemResponse = PostApiIdjasJettyRequestItemResponses[keyof PostApiIdjasJettyRequestItemResponses];

export type DeleteApiIdjasJettyRequestItemByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/jetty-request-item/{id}';
};

export type DeleteApiIdjasJettyRequestItemByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdjasJettyRequestItemByIdError = DeleteApiIdjasJettyRequestItemByIdErrors[keyof DeleteApiIdjasJettyRequestItemByIdErrors];

export type DeleteApiIdjasJettyRequestItemByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdjasJettyRequestItemByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/jetty-request-item/{id}';
};

export type GetApiIdjasJettyRequestItemByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdjasJettyRequestItemByIdError = GetApiIdjasJettyRequestItemByIdErrors[keyof GetApiIdjasJettyRequestItemByIdErrors];

export type GetApiIdjasJettyRequestItemByIdResponses = {
    /**
     * OK
     */
    200: JettyRequestItemDto;
};

export type GetApiIdjasJettyRequestItemByIdResponse = GetApiIdjasJettyRequestItemByIdResponses[keyof GetApiIdjasJettyRequestItemByIdResponses];

export type PutApiIdjasJettyRequestItemByIdData = {
    body?: CreateUpdateJettyRequestItemDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/idjas/jetty-request-item/{id}';
};

export type PutApiIdjasJettyRequestItemByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdjasJettyRequestItemByIdError = PutApiIdjasJettyRequestItemByIdErrors[keyof PutApiIdjasJettyRequestItemByIdErrors];

export type PutApiIdjasJettyRequestItemByIdResponses = {
    /**
     * OK
     */
    200: JettyRequestItemDto;
};

export type PutApiIdjasJettyRequestItemByIdResponse = PutApiIdjasJettyRequestItemByIdResponses[keyof PutApiIdjasJettyRequestItemByIdResponses];

export type PostApiIdjasJettyRequestItemFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/idjas/jetty-request-item/filter-list';
};

export type PostApiIdjasJettyRequestItemFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdjasJettyRequestItemFilterListError = PostApiIdjasJettyRequestItemFilterListErrors[keyof PostApiIdjasJettyRequestItemFilterListErrors];

export type PostApiIdjasJettyRequestItemFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyRequestItemDto;
};

export type PostApiIdjasJettyRequestItemFilterListResponse = PostApiIdjasJettyRequestItemFilterListResponses[keyof PostApiIdjasJettyRequestItemFilterListResponses];

export type PostApiAccountLoginData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/login';
};

export type PostApiAccountLoginErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountLoginError = PostApiAccountLoginErrors[keyof PostApiAccountLoginErrors];

export type PostApiAccountLoginResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountLoginResponse = PostApiAccountLoginResponses[keyof PostApiAccountLoginResponses];

export type GetApiAccountLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/logout';
};

export type GetApiAccountLogoutErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountLogoutError = GetApiAccountLogoutErrors[keyof GetApiAccountLogoutErrors];

export type GetApiAccountLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountCheckPasswordData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/check-password';
};

export type PostApiAccountCheckPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountCheckPasswordError = PostApiAccountCheckPasswordErrors[keyof PostApiAccountCheckPasswordErrors];

export type PostApiAccountCheckPasswordResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountCheckPasswordResponse = PostApiAccountCheckPasswordResponses[keyof PostApiAccountCheckPasswordResponses];

export type GetApiAccountMyProfileData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type GetApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountMyProfileError = GetApiAccountMyProfileErrors[keyof GetApiAccountMyProfileErrors];

export type GetApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type GetApiAccountMyProfileResponse = GetApiAccountMyProfileResponses[keyof GetApiAccountMyProfileResponses];

export type PutApiAccountMyProfileData = {
    body?: UpdateProfileDto;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type PutApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiAccountMyProfileError = PutApiAccountMyProfileErrors[keyof PutApiAccountMyProfileErrors];

export type PutApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type PutApiAccountMyProfileResponse = PutApiAccountMyProfileResponses[keyof PutApiAccountMyProfileResponses];

export type PostApiAccountMyProfileChangePasswordData = {
    body?: ChangePasswordInput;
    path?: never;
    query?: never;
    url: '/api/account/my-profile/change-password';
};

export type PostApiAccountMyProfileChangePasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountMyProfileChangePasswordError = PostApiAccountMyProfileChangePasswordErrors[keyof PostApiAccountMyProfileChangePasswordErrors];

export type PostApiAccountMyProfileChangePasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type DeleteApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdError = DeleteApiMultiTenancyTenantsByIdErrors[keyof DeleteApiMultiTenancyTenantsByIdErrors];

export type DeleteApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type GetApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdError = GetApiMultiTenancyTenantsByIdErrors[keyof GetApiMultiTenancyTenantsByIdErrors];

export type GetApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type GetApiMultiTenancyTenantsByIdResponse = GetApiMultiTenancyTenantsByIdResponses[keyof GetApiMultiTenancyTenantsByIdResponses];

export type PutApiMultiTenancyTenantsByIdData = {
    body?: TenantUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type PutApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdError = PutApiMultiTenancyTenantsByIdErrors[keyof PutApiMultiTenancyTenantsByIdErrors];

export type PutApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PutApiMultiTenancyTenantsByIdResponse = PutApiMultiTenancyTenantsByIdResponses[keyof PutApiMultiTenancyTenantsByIdResponses];

export type GetApiMultiTenancyTenantsData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/multi-tenancy/tenants';
};

export type GetApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsError = GetApiMultiTenancyTenantsErrors[keyof GetApiMultiTenancyTenantsErrors];

export type GetApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTenantDto;
};

export type GetApiMultiTenancyTenantsResponse = GetApiMultiTenancyTenantsResponses[keyof GetApiMultiTenancyTenantsResponses];

export type PostApiMultiTenancyTenantsData = {
    body?: TenantCreateDto;
    path?: never;
    query?: never;
    url: '/api/multi-tenancy/tenants';
};

export type PostApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiMultiTenancyTenantsError = PostApiMultiTenancyTenantsErrors[keyof PostApiMultiTenancyTenantsErrors];

export type PostApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PostApiMultiTenancyTenantsResponse = PostApiMultiTenancyTenantsResponses[keyof PostApiMultiTenancyTenantsResponses];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError = DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringError = GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse = GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        defaultConnectionString?: string;
    };
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringError = PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenAccessData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token/access';
};

export type GetApiTokenAccessResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenRefreshData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token/refresh';
};

export type GetApiTokenRefreshResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenStatusData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token/status';
};

export type GetApiTokenStatusResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenValidData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token/valid';
};

export type GetApiTokenValidResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenExpirationData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token/expiration';
};

export type GetApiTokenExpirationResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenDebugData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token/debug';
};

export type GetApiTokenDebugResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiTokenValidationValidateData = {
    body?: TokenValidationRequest;
    path?: never;
    query?: never;
    url: '/api/token-validation/validate';
};

export type PostApiTokenValidationValidateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiTokenValidationIsValidData = {
    body?: TokenValidationRequest;
    path?: never;
    query?: never;
    url: '/api/token-validation/is-valid';
};

export type PostApiTokenValidationIsValidResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTokenValidationProtectedData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/token-validation/protected';
};

export type GetApiTokenValidationProtectedResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type ClientOptions = {
    baseUrl: `${string}://swagger.json` | (string & {});
};