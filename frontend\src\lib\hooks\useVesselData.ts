import { useMutation } from '@tanstack/react-query';
import { postApiEkbVesselHeader } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { VesselQueryRequestDto, FilterGroup, PagedResultDtoOfVesselHeaderDto } from '@/client/types.gen';

interface UseVesselDataProps {
  vesselType?: string;
  filterGroup?: FilterGroup;
  skipCount?: number;
  maxResultCount?: number;
}

export const useVesselData = () => {
  return useMutation<PagedResultDtoOfVesselHeaderDto, Error, UseVesselDataProps>({
    mutationFn: async ({ vesselType, filterGroup, skipCount = 0, maxResultCount = 50 }) => {
      // Create vessel query request
      const vesselRequest: VesselQueryRequestDto = {
        vesselType,
        filterGroup,
        skipCount,
        maxResultCount,
      };

      const res = await postApiEkbVesselHeader({ body: vesselRequest });
      if (!res?.data?.items) {
        toast({ title: 'No vessels found', variant: 'destructive' });
        return { items: [], totalCount: 0 };
      }
      return res.data;
    },
    onError: (error: unknown) => {
      console.error('Vessel mutation error:', error);
      toast({
        title: 'Error loading vessel data',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    }
  });
}; 