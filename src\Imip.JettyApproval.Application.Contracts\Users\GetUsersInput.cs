using System.Collections.Generic;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Users;

public class GetUsersInput : PagedAndSortedResultRequestDto
{
    public FilterGroup? FilterGroup { get; set; }
    public List<SortInfo>? SortFields { get; set; }
    public string? SearchKeyword { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsLockedOut { get; set; }
}