import { postApiIdjasApprovalTemplateFilterList } from "@/client/sdk.gen";
import type { ApprovalTemplateDto, FilterCondition, FilterGroup, FilterOperator, LogicalOperator } from "@/client/types.gen";
import { DataTable } from "@/components/data-table/DataTable";
import FilterSortBar, { type SortDirection } from "@/components/filter-sort-bar";
import { Button } from "@/components/ui/button";
import TableSkeleton from "@/components/ui/table-skeleton";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { type ColumnDef, type PaginationState } from "@tanstack/react-table";
import { Pencil, Plus, RefreshCw } from "lucide-react";
import React, { useMemo, useState } from "react";
import ApprovalTemplateDialog from "./approval-template-dialog";

const FILTER_FIELDS = [
  { value: "name", label: "Name" },
  { value: "description", label: "Description" },
  { value: "code", label: "Code" },
  // Add more fields as needed
];
const FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [
  { value: "Equals", label: "Equals" },
  { value: "Contains", label: "Contains" },
  { value: "NotEquals", label: "Not Equals" },
  { value: "GreaterThan", label: ">" },
  { value: "LessThan", label: "<" },
];

const ApprovalTemplateTable: React.FC = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ApprovalTemplateDto | null>(null);
  const queryClient = useQueryClient();

  // Reset to first page when filters or sorts change
  React.useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [filters, sorts]);

  // Build filter group for backend
  const filterGroup: FilterGroup | undefined = useMemo(() => {
    if (!filters.length) return undefined;
    return {
      operator: "And" as LogicalOperator,
      conditions: filters,
    };
  }, [filters]);

  // Build sorting string for backend
  const sortingStr = useMemo(() => {
    if (!sorts.length) return undefined;
    return sorts.map(s => `${s.field} ${s.direction}`).join(", ");
  }, [sorts]);

  // React Query for data
  const {
    data: queryData,
    isLoading,
    error,
    refetch,
    isFetching,
  } = useQuery({
    queryKey: [
      "approval-templates",
      pagination.pageIndex,
      pagination.pageSize,
      sortingStr,
      filterGroup,
    ],
    queryFn: async () => {
      const res = await postApiIdjasApprovalTemplateFilterList({
        body: {
          page: pagination.pageIndex + 1,
          maxResultCount: pagination.pageSize,
          sorting: sortingStr,
          filterGroup,
        },
      });
      // Type guard for paged result
      function isPagedResult(obj: unknown): obj is { items: ApprovalTemplateDto[]; totalCount: number } {
        return !!obj && typeof obj === 'object' && 'items' in obj && 'totalCount' in obj;
      }
      const data = res?.data ?? res;
      if (isPagedResult(data)) {
        return {
          items: data.items ?? [],
          totalCount: data.totalCount ?? 0,
        };
      }
      return { items: [], totalCount: 0 };
    },
  });

  const data = queryData ?? { items: [], totalCount: 0 };

  // Refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await queryClient.invalidateQueries({ queryKey: [
      "approval-templates",
      pagination.pageIndex,
      pagination.pageSize,
      sortingStr,
      filterGroup,
    ] });
    setIsRefreshing(false);
  };

  // New Approval Template button
  const handleNew = () => {
    setEditingTemplate(null);
    setDialogOpen(true);
  };

  // Edit button handler
  const handleEdit = (template: ApprovalTemplateDto) => {
    setEditingTemplate(template);
    setDialogOpen(true);
  };

  // Table columns (must be inside component to access handleEdit)
  const columns: ColumnDef<ApprovalTemplateDto>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "code",
      header: "Code",
      cell: info => info.getValue() ?? "-",
    },
    {
      id: "edit",
      header: "",
      cell: ({ row }) => (
        <Button
          onClick={() => handleEdit(row.original)}
          aria-label="Edit Approval Template"
          tabIndex={0}
          variant="outline"
          size="icon"
          className="ml-2 h-8 w-8"
        >
          <Pencil className="w-4 h-4" aria-hidden="true" />
        </Button>
      ),
      enableSorting: false,
      enableColumnFilter: false,
    },
    // Add more columns as needed
  ];

  // Error UI
  if (error) {
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center">
        <div className="mx-auto h-12 w-12 text-destructive mb-4">!</div>
        <h3 className="font-semibold text-destructive mb-2">Error loading data</h3>
        <p className="text-sm text-muted-foreground mb-4">{String((error as Error).message)}</p>
        <Button onClick={handleRefresh} variant="destructive">Retry</Button>
      </div>
    );
  }

  return (
    <div className="bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2">
      <div className="text-xl font-bold px-2 pt-2 pb-1">Approval Template List</div>
      <FilterSortBar
        filterFields={FILTER_FIELDS}
        operators={FILTER_OPERATORS}
        filters={filters}
        sorts={sorts}
        onFiltersChange={setFilters}
        onSortsChange={setSorts}
      >
        <div className="ml-auto flex items-center gap-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="icon"
            className="h-10 w-10"
            disabled={isLoading || isRefreshing || isFetching}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing || isFetching ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            onClick={handleNew}
          >
            <Plus className="h-5 w-5" /> New Approval Template
          </Button>
        </div>
      </FilterSortBar>
      {isLoading ? (
        <TableSkeleton columns={columns} />
      ) : (
        <DataTable
          title=""
          columns={columns}
          data={data.items}
          totalCount={data.totalCount}
          isLoading={isLoading}
          manualPagination={true}
          pageSize={pagination.pageSize}
          onPaginationChange={setPagination}
          hideDefaultFilterbar={true}
          enableRowSelection={false}
          manualSorting={true}
        />
      )}
      <ApprovalTemplateDialog
        open={dialogOpen}
        onOpenChange={(open) => {
          setDialogOpen(open);
          if (!open) refetch(); // Refresh data on close
        }}
        template={editingTemplate}
      />
    </div>
  );
};

export default ApprovalTemplateTable; 