using System;
using System.Collections.Generic;
using Imip.JettyApproval.DocumentTemplates;
using Imip.JettyApproval.Documents;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for DocumentTemplate entity
/// </summary>
[Mapper]
public partial class DocumentTemplateMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(DocumentTemplate.Id), nameof(DocumentTemplateDto.Id))]
    [MapperIgnoreSource(nameof(DocumentTemplate.IsDeleted))]
    [MapperIgnoreSource(nameof(DocumentTemplate.DeleterId))]
    [MapperIgnoreSource(nameof(DocumentTemplate.DeletionTime))]
    [MapperIgnoreSource(nameof(DocumentTemplate.LastModificationTime))]
    [MapperIgnoreSource(nameof(DocumentTemplate.LastModifierId))]
    [MapperIgnoreSource(nameof(DocumentTemplate.CreationTime))]
    [MapperIgnoreSource(nameof(DocumentTemplate.CreatorId))]
    [MapperIgnoreSource(nameof(DocumentTemplate.ExtraProperties))]
    [MapperIgnoreSource(nameof(DocumentTemplate.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(DocumentTemplateDto.DocumentTypeName))] // Handled in service
    public partial DocumentTemplateDto MapToDto(DocumentTemplate entity);

    // DTO to Entity mapping for creation
    public partial void MapToEntity(CreateDocumentTemplateDto dto, DocumentTemplate entity);

    // DTO to Entity mapping for updates
    public partial void MapToEntity(UpdateDocumentTemplateDto dto, DocumentTemplate entity);

    // Custom mapping methods for complex scenarios
    public DocumentTemplate CreateEntityWithId(CreateDocumentTemplateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (DocumentTemplate)Activator.CreateInstance(typeof(DocumentTemplate), true)!;
        MapToEntity(dto, entity);
        return entity;
    }

    // Map list of entities to DTOs
    public partial List<DocumentTemplateDto> MapToDtoList(List<DocumentTemplate> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<DocumentTemplateDto> MapToDtoEnumerable(IEnumerable<DocumentTemplate> entities);
}