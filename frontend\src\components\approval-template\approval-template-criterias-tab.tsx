import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2 } from "lucide-react";
import React from "react";
import type { FieldArrayWithId, UseFieldArrayAppend } from "react-hook-form";
import { Controller, useFormContext } from "react-hook-form";
import type { FormValues } from "./approval-template-dialog";

type CriteriasTabProps = {
  fields: FieldArrayWithId<FormValues, "criterias", "id">[];
  append: UseFieldArrayAppend<FormValues, "criterias">;
  remove: (index: number) => void;
};

const DOCUMENT_TYPE_OPTIONS = [
  { value: "Import", label: "Import" },
  { value: "Export", label: "Export" },
  { value: "LocalIn", label: "Local In" },
  { value: "LocalOut", label: "Local Out" },
];

const CriteriasTab: React.FC<CriteriasTabProps> = ({ fields, append, remove }) => {
  const { control } = useFormContext();

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <div className="font-semibold">Criterias</div>
        <Button type="button" onClick={() => append({ approvalId: null, documentType: undefined, id: "00000000-0000-0000-0000-000000000000" })} size="sm">
          Add Criteria
        </Button>
      </div>
      <div className="space-y-2">
        {fields.length === 0 && <div className="text-muted-foreground text-sm">No criterias added.</div>}
        {fields.map((field, idx) => (
          <div key={field.id} className="flex flex-col md:flex-row items-center gap-2 border rounded p-2">
            <span className="font-semibold text-sm">Criteria #{idx + 1}</span>
            <input type="hidden" {...control.register(`criterias.${idx}.id`)} />
            <input type="hidden" {...control.register(`criterias.${idx}.approvalId`)} />
            <Controller
              name={`criterias.${idx}.documentType`}
              control={control}
              render={({ field }) => (
                <Select value={field.value || ""} onValueChange={field.onChange}>
                  <SelectTrigger className="w-100">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {DOCUMENT_TYPE_OPTIONS.map(opt => (
                      <SelectItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            <Button type="button" variant="ghost" size="icon" onClick={() => remove(idx)} aria-label="Remove Criteria">
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CriteriasTab; 