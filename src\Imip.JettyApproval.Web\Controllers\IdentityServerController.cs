using System.Threading.Tasks;
using Imip.JettyApproval.Users;
using Imip.JettyApproval.Web.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Route("api/[controller]")]
public class IdentityServerController : AbpController
{
    private readonly AppToAppService _appToAppService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IdentityServerController> _logger;

    public IdentityServerController(AppToAppService appToAppService, IConfiguration configuration, ILogger<IdentityServerController> logger)
    {
        _appToAppService = appToAppService;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpPost("user-query")]
    public async Task<PagedResultDto<ExtendedIdentityUserDto>> UserQuery([FromBody] GetUsersInput request)
    {
        var apiUrl = _configuration["ExternalApps:IdentityServer:BaseUrl"];
        var endpoint = "api/user-query/users";
        var result = await _appToAppService.CallOtherAppAsync<PagedResultDto<ExtendedIdentityUserDto>>(apiUrl, endpoint, request);
        return result;
    }
}
