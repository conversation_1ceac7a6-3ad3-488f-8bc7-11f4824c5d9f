using System;
using System.Collections.Generic;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

[Mapper]
public partial class ApprovalTemplateMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ApprovalTemplate.Id), nameof(ApprovalTemplateDto.Id))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.IsDeleted))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.DeleterId))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.DeletionTime))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.LastModificationTime))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.LastModifierId))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.CreationTime))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.CreatorId))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.ExtraProperties))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.ConcurrencyStamp))]
    [MapperIgnoreSource(nameof(ApprovalTemplate.Stages))] // Ignore stages to prevent circular reference
    public partial ApprovalTemplateDto MapToDto(ApprovalTemplate entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ApprovalTemplate.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ApprovalTemplate.Approvers))] // Handle approvers manually
    [MapperIgnoreTarget(nameof(ApprovalTemplate.Criterias))] // Handle criterias manually
    public partial void MapToEntity(CreateUpdateApprovalTemplateDto dto, ApprovalTemplate entity);

    // Custom mapping methods for complex scenarios
    public ApprovalTemplate CreateEntityWithId(CreateUpdateApprovalTemplateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ApprovalTemplate)Activator.CreateInstance(typeof(ApprovalTemplate), true)!;

        // Set the ID using reflection since it's protected
        var idProperty = typeof(ApprovalTemplate).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map basic properties (excluding collections)
        MapToEntity(dto, entity);

        // Manually map approvers (ignoring ApprovalId from DTO)
        if (dto.Approvers != null)
        {
            foreach (var approverDto in dto.Approvers)
            {
                var approver = new ApprovalApprover(
                    Guid.Empty, // Will be set later in the service
                    approverDto.ApproverId,
                    approverDto.Sequence,
                    approverDto.Status
                );

                // Set ID if provided (for updates)
                if (approverDto.Id.HasValue && approverDto.Id.Value != Guid.Empty)
                {
                    var approverIdProperty = typeof(ApprovalApprover).GetProperty("Id");
                    approverIdProperty?.SetValue(approver, approverDto.Id.Value);
                }

                entity.Approvers.Add(approver);
            }
        }

        // Manually map criterias (ignoring ApprovalId from DTO)
        if (dto.Criterias != null)
        {
            foreach (var criteriaDto in dto.Criterias)
            {
                var criteria = new ApprovalCriteria(
                    Guid.Empty, // Will be set later in the service
                    criteriaDto.DocumentType
                );

                // Set ID if provided (for updates)
                if (criteriaDto.Id.HasValue && criteriaDto.Id.Value != Guid.Empty)
                {
                    var criteriaIdProperty = typeof(ApprovalCriteria).GetProperty("Id");
                    criteriaIdProperty?.SetValue(criteria, criteriaDto.Id.Value);
                }

                entity.Criterias.Add(criteria);
            }
        }

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ApprovalTemplateDto> MapToDtoList(List<ApprovalTemplate> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ApprovalTemplateDto> MapToDtoEnumerable(IEnumerable<ApprovalTemplate> entities);
}
