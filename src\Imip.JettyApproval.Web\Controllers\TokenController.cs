using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Users;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;

namespace Imip.JettyApproval.Web.Controllers;

[Authorize]
public class TokenController : AbpController
{
    private readonly ITokenService _tokenService;
    private readonly ILogger<TokenController> _logger;
    private readonly ICurrentUser _currentUser;

    public TokenController(ITokenService tokenService, ILogger<TokenController> logger, ICurrentUser currentUser)
    {
        _tokenService = tokenService;
        _logger = logger;
        _currentUser = currentUser;
    }

    [HttpGet("/api/token/access")]
    public async Task<IActionResult> GetAccessToken()
    {
        var token = await _tokenService.GetAccessTokenAsync();
        if (string.IsNullOrEmpty(token))
        {
            return NotFound("No access token found");
        }

        return Ok(new { access_token = token });
    }

    [HttpGet("/api/token/refresh")]
    public async Task<IActionResult> RefreshToken()
    {
        try
        {
            // Add debugging information
            var refreshToken = await _tokenService.GetRefreshTokenAsync();
            var hasValidToken = await _tokenService.HasValidTokenAsync();
            var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

            // Log the debugging information
            _logger.LogInformation("Token refresh attempt - Has refresh token: {HasRefreshToken}, Has valid token: {HasValidToken}, Refresh token expired: {RefreshTokenExpired}",
                !string.IsNullOrEmpty(refreshToken), hasValidToken, isRefreshTokenExpired);

            var newToken = await _tokenService.RefreshAccessTokenAsync();
            if (string.IsNullOrEmpty(newToken))
            {
                _logger.LogWarning("Token refresh failed - no new token returned");
                return BadRequest("Failed to refresh token");
            }

            _logger.LogInformation("Token refresh successful");
            return Ok(new { access_token = newToken });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during token refresh");
            return BadRequest($"Token refresh failed: {ex.Message}");
        }
    }

    [HttpGet("/api/token/status")]
    public async Task<IActionResult> GetTokenStatus()
    {
        var hasValidToken = await _tokenService.HasValidTokenAsync();
        var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

        return Ok(new
        {
            has_valid_token = hasValidToken,
            refresh_token_expired = isRefreshTokenExpired
        });
    }

    [HttpGet("/api/token/valid")]
    public async Task<IActionResult> GetValidToken()
    {
        var token = await _tokenService.GetValidAccessTokenAsync();
        if (string.IsNullOrEmpty(token))
        {
            return BadRequest("Failed to get valid token");
        }

        return Ok(new { access_token = token });
    }

    [HttpGet("/api/token/expiration")]
    public async Task<IActionResult> GetTokenExpirationInfo()
    {
        var accessToken = await _tokenService.GetAccessTokenAsync();
        var refreshToken = await _tokenService.GetRefreshTokenAsync();

        var accessTokenValid = await _tokenService.HasValidTokenAsync();
        var refreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

        // Add debugging information
        _logger.LogInformation("Token expiration info - Access token: {HasAccessToken}, Refresh token: {HasRefreshToken}, Access valid: {AccessValid}, Refresh expired: {RefreshExpired}",
            !string.IsNullOrEmpty(accessToken),
            !string.IsNullOrEmpty(refreshToken),
            accessTokenValid,
            refreshTokenExpired);

        return Ok(new
        {
            access_token_valid = accessTokenValid,
            refresh_token_expired = refreshTokenExpired,
            has_access_token = !string.IsNullOrEmpty(accessToken),
            has_refresh_token = !string.IsNullOrEmpty(refreshToken),
            access_token_length = accessToken?.Length ?? 0,
            refresh_token_length = refreshToken?.Length ?? 0
        });
    }

    [HttpGet("/api/token/debug")]
    public async Task<IActionResult> DebugTokenInfo()
    {
        try
        {
            var context = HttpContext;
            var authenticateResult = await context.AuthenticateAsync("Cookies");

            var debugInfo = new
            {
                is_authenticated = authenticateResult.Succeeded,
                has_properties = authenticateResult.Properties != null,
                properties_count = authenticateResult.Properties?.Items?.Count ?? 0,
                token_properties = authenticateResult.Properties?.Items?
                    .Where(kvp => kvp.Key.Contains("token", StringComparison.OrdinalIgnoreCase))
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.Substring(0, Math.Min(20, kvp.Value?.Length ?? 0)) + "...")
                    ?? new Dictionary<string, string>(),
                current_user_id = _currentUser.Id,
                current_user_name = _currentUser.UserName
            };

            _logger.LogInformation("Debug token info: {@DebugInfo}", debugInfo);
            return Ok(debugInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in debug token info");
            return BadRequest($"Debug failed: {ex.Message}");
        }
    }

    [HttpGet("/api/token/identity-server-info")]
    public async Task<IActionResult> GetIdentityServerInfo()
    {
        try
        {
            var configuration = HttpContext.RequestServices.GetRequiredService<IConfiguration>();
            var authority = configuration["AuthServer:Authority"];

            using var httpClient = new HttpClient();
            var discoveryUrl = $"{authority}/.well-known/openid-configuration";

            _logger.LogInformation("Fetching OpenID Connect discovery document from: {Url}", discoveryUrl);

            var response = await httpClient.GetAsync(discoveryUrl);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var discoveryDoc = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(content);

                var info = new
                {
                    issuer = discoveryDoc.TryGetProperty("issuer", out var issuer) ? issuer.GetString() : "Unknown",
                    authorization_endpoint = discoveryDoc.TryGetProperty("authorization_endpoint", out var auth) ? auth.GetString() : "Unknown",
                    token_endpoint = discoveryDoc.TryGetProperty("token_endpoint", out var token) ? token.GetString() : "Unknown",
                    scopes_supported = discoveryDoc.TryGetProperty("scopes_supported", out var scopes) ? scopes.EnumerateArray().Select(s => s.GetString()).ToArray() : new string[0],
                    grant_types_supported = discoveryDoc.TryGetProperty("grant_types_supported", out var grants) ? grants.EnumerateArray().Select(g => g.GetString()).ToArray() : new string[0],
                    response_types_supported = discoveryDoc.TryGetProperty("response_types_supported", out var responses) ? responses.EnumerateArray().Select(r => r.GetString()).ToArray() : new string[0]
                };

                _logger.LogInformation("Identity server info: {@Info}", info);
                return Ok(info);
            }
            else
            {
                _logger.LogError("Failed to fetch discovery document: {StatusCode}", response.StatusCode);
                return BadRequest($"Failed to fetch discovery document: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching identity server info");
            return BadRequest($"Failed to fetch identity server info: {ex.Message}");
        }
    }
}