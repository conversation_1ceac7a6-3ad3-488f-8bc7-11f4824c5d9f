# GitLab CI/CD Configuration
# Using Docker 24.0.7 with <PERSON><PERSON><PERSON><PERSON> disabled to avoid compatibility issues
# All deployments target k8s-worker1 node

stages:
  - build
  - migrate
  - deploy

# Global variables
variables:
  # Disable git clean to avoid permission issues
  GIT_CLEAN_FLAGS: none
  GIT_STRATEGY: fetch

  # Set umask to ensure files are created with permissive permissions
  UMASK: "0000"

  # Ensure dotnet creates files with proper permissions
  DOTNET_CLI_HOME: "/tmp/dotnet_cli_home"

  # Docker configuration for GitLab.com shared runners
  DOCKER_DRIVER: overlay2
  # DOCKER_TLS_CERTDIR is set in individual jobs

  # NuGet package caching
  NUGET_PACKAGES_DIRECTORY: ".nuget"

  # Common variables
  SEQ_SERVER_URL: "http://**********:5341"
  SEQ_API_KEY: "mq5CGPBuhwoP31LDRia7"
  CERT_PASSPHRASE: "1e008fd6-560a-4c2f-be3b-4eb7b0207372"
  ENCRYPTION_PASSPHRASE: "VjK20WzlSovQGAQh"
  DEV_EXTERNAL_AUTH_URL: "http://192.168.234.113/api/common/RequestAuthenticationToken"
  PROD_EXTERNAL_AUTH_URL: "http://192.168.234.113/api/common/RequestAuthenticationToken"

  # Auth Server Client credentials
  DEV_AUTH_CLIENT_ID: "IdjasDev"
  DEV_AUTH_CLIENT_SECRET: "GUUJOI5DjU1MVv2jWtt9ZfSOod6wWet0"
  PROD_AUTH_CLIENT_ID: "IdjasProd"
  PROD_AUTH_CLIENT_SECRET: "EY8c4ytLP3lljLfDgF6PkPQo94D128wn"

  # Redis configuration
  DEV_REDIS_CONFIGURATION: "**********:6379"
  PROD_REDIS_CONFIGURATION: "**********:6379"



  # Target node for deployment
  TARGET_NODE: "k8s-worker1"

  # Certificate variables - the actual certificate will be stored in GitLab CI/CD variables
  # IDENTITY_SERVER_CERT: Base64 encoded .pfx file stored in GitLab CI/CD variables

  # Development environment variables
  DEV_DB_CONNECTION: "Server=**************;Database=idjas_dev;User ID=idjas_dev;Password=${DB_PASSWORD_DEV};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
  DEV_APP_URL: "https://idjas-dev.imip.co.id"
  DEV_AUTH_APP_URL: "https://identity.imip.co.id"
  DEV_CLIENT_URL: "https://idjas-dev.imip.co.id"
  DEV_CORS_ORIGINS: "https://idjas-dev.imip.co.id"

  # Production environment variables
  PROD_DB_CONNECTION: "Server=**************;Database=idjas_prd;User ID=idjas_prd;Password=${DB_PASSWORD_PROD};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
  PROD_APP_URL: "https://idjas.imip.co.id"
  PROD_AUTH_APP_URL: "https://identity.imip.co.id"
  PROD_CLIENT_URL: "https://idjas.imip.co.id"
  PROD_CORS_ORIGINS: "https://idjas.imip.co.id"

# Build stage for development

# Build .NET applications for development using Docker
build-dotnet-dev:
  stage: build
  tags:
    - docker-builder # Use your self-hosted runner to avoid GitLab minutes
  variables:
    BUILD_DIR: "/tmp/gitlab-build-${CI_PIPELINE_ID}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-nuget
    paths:
      - ${NUGET_PACKAGES_DIRECTORY}
    policy: pull-push
  before_script:
    # Set umask to ensure files are created with permissive permissions
    - umask 0000

    # Clean up any previous build artifacts to avoid permission issues
    - BUILD_DIR="/tmp/gitlab-build-${CI_PIPELINE_ID}"
    - rm -rf $BUILD_DIR || true
    - mkdir -p $BUILD_DIR
    - chmod 777 $BUILD_DIR
    - mkdir -p $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context
    - chmod -R 777 $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context

    # Create a clean copy of the repository to avoid permission issues
    - mkdir -p $BUILD_DIR/repo
    - chmod 777 $BUILD_DIR/repo
    - cp -r * $BUILD_DIR/repo/ || true

    # Create dotnet CLI home directory with proper permissions
    - mkdir -p $DOTNET_CLI_HOME
    - chmod 777 $DOTNET_CLI_HOME
  script:
    # Create NuGet packages directory if it doesn't exist
    - mkdir -p ${NUGET_PACKAGES_DIRECTORY}

    # Build Web Application using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/web-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.JettyApproval.Web/Imip.JettyApproval.Web.csproj && dotnet publish src/Imip.JettyApproval.Web/Imip.JettyApproval.Web.csproj -c Release -o /output"

    # Build DB Migrator using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/migrator-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.JettyApproval.DbMigrator/Imip.JettyApproval.DbMigrator.csproj && dotnet publish src/Imip.JettyApproval.DbMigrator/Imip.JettyApproval.DbMigrator.csproj -c Release -o /output"

    # Create a Docker build context with the published files
    - mkdir -p $BUILD_DIR/docker-context/web $BUILD_DIR/docker-context/db-migrator
    - cp -r $BUILD_DIR/web-build/* $BUILD_DIR/docker-context/web/
    - cp -r $BUILD_DIR/migrator-build/* $BUILD_DIR/docker-context/db-migrator/
    - cp $BUILD_DIR/repo/src/Imip.JettyApproval.Web/entrypoint.sh $BUILD_DIR/docker-context/web/
    - cp $BUILD_DIR/repo/src/Imip.JettyApproval.Web/Dockerfile.new $BUILD_DIR/docker-context/Dockerfile
  artifacts:
    paths:
      - /tmp/gitlab-build-${CI_PIPELINE_ID}/docker-context/
    expire_in: 1 hour
  only:
    - dev

# Build Docker images for development on dedicated builder
build-docker-dev:
  stage: build
  tags:
    - docker-builder # This targets your new runner on imdevapp20
  variables:
    DOCKER_DEFAULT_PLATFORM: "linux/amd64" # Explicitly set the target platform
    CLEANUP_OLDER_THAN: "168h" # 7 days in hours format (compatible with Docker)
  before_script:
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    # Cleanup old images to optimize storage
    - echo "Cleaning up old Docker images to optimize storage..."
    - docker image prune -a --filter "until=${CLEANUP_OLDER_THAN}" --force || true
    - docker system prune --volumes -f || true
  script:
    # Set build directory
    - BUILD_DIR="/tmp/gitlab-build-${CI_PIPELINE_ID}"

    # Build Docker images with explicit platform using the prepared context
    - cd $BUILD_DIR/docker-context
    # Build and push web image
    - echo "Building web image with tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA}..."
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=web .
    - docker images | grep $CI_REGISTRY_IMAGE/web || echo "Web image not found in local registry"
    - echo "Pushing web image to registry..."
    - docker push $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push web image"; exit 1; }

    # Copy DB migrator Dockerfile and build
    - cp -f $BUILD_DIR/repo/src/Imip.JettyApproval.DbMigrator/Dockerfile.new ./Dockerfile || cp -f $CI_PROJECT_DIR/src/Imip.JettyApproval.DbMigrator/Dockerfile.new ./Dockerfile
    - echo "Building db-migrator image with tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA}..."
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=db-migrator .
    - docker images | grep $CI_REGISTRY_IMAGE/db-migrator || echo "DB migrator image not found in local registry"
    - echo "Pushing db-migrator image to registry..."
    - docker push $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push db-migrator image"; exit 1; }

    # Verify images exist in registry before tagging
    - echo "Verifying images exist in registry before tagging..."
    - docker pull $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull web image from registry"; exit 1; }
    - docker pull $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull db-migrator image from registry"; exit 1; }

    # Tag with branch name for easier reference
    - echo "Tagging images with branch name $CI_COMMIT_REF_SLUG..."
    - docker tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
  after_script:
    # Clean up build directory
    - BUILD_DIR="/tmp/gitlab-build-${CI_PIPELINE_ID}"
    - |
      echo "Cleaning up build directory: $BUILD_DIR"
      if [ -d "$BUILD_DIR" ]; then
        echo "Setting proper permissions before cleanup..."
        # First, try to fix permissions on problematic directories
        find $BUILD_DIR -type d -name "bin" -o -name "obj" | xargs -I{} chmod -R 777 {} 2>/dev/null || true

        echo "Attempting to remove build directory without changing permissions..."
        # Try to remove without changing permissions first
        rm -rf $BUILD_DIR || {
          echo "Standard removal failed, trying with sudo..."
          # Try with sudo if available
          if command -v sudo &> /dev/null; then
            sudo chmod -R 777 $BUILD_DIR 2>/dev/null || true
            sudo rm -rf $BUILD_DIR || {
              echo "Sudo removal failed, trying with Docker..."
              # If that fails, try using Docker to clean up (runs as root)
              docker run --rm -v $BUILD_DIR:/data alpine:latest sh -c "chmod -R 777 /data && rm -rf /data/*" || {
                echo "Docker removal failed, trying with find command..."
                # If Docker fails, try using find to delete files with current permissions
                find $BUILD_DIR -type f -exec chmod 666 {} \; 2>/dev/null || true
                find $BUILD_DIR -type d -exec chmod 777 {} \; 2>/dev/null || true
                find $BUILD_DIR -type f -exec rm -f {} \; 2>/dev/null || true
                find $BUILD_DIR -type d -empty -delete 2>/dev/null || true
                # Final attempt with force
                rm -rf $BUILD_DIR 2>/dev/null || echo "Failed to remove build directory completely, some files may remain."
              }
            }
          else
            echo "Sudo not available, trying with find command..."
            # If sudo is not available, try using find to delete files with current permissions
            find $BUILD_DIR -type f -exec chmod 666 {} \; 2>/dev/null || true
            find $BUILD_DIR -type d -exec chmod 777 {} \; 2>/dev/null || true
            find $BUILD_DIR -type f -exec rm -f {} \; 2>/dev/null || true
            find $BUILD_DIR -type d -empty -delete 2>/dev/null || true
            # Final attempt with force
            rm -rf $BUILD_DIR 2>/dev/null || echo "Failed to remove build directory completely, some files may remain."
          fi
        }
      else
        echo "Build directory not found, skipping cleanup"
      fi

      # If all else fails, just ignore the errors and continue
      echo "Note: Any remaining permission errors during cleanup can be safely ignored"

    # Additional cleanup after build to free up space
    - |
      echo "Removing build-specific images to free up space..."
      echo "Checking for web image..."
      if docker images | grep -q "$CI_REGISTRY_IMAGE/web.*$CI_COMMIT_SHORT_SHA"; then
        docker rmi $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || echo "Failed to remove web image, continuing..."
      else
        echo "Web image not found locally"
      fi

    - |
      echo "Checking for db-migrator image..."
      if docker images | grep -q "$CI_REGISTRY_IMAGE/db-migrator.*$CI_COMMIT_SHORT_SHA"; then
        docker rmi $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || echo "Failed to remove db-migrator image, continuing..."
      else
        echo "DB migrator image not found locally"
      fi

    # Clean up dangling images and containers
    - |
      echo "Cleaning up dangling images and containers..."
      docker system prune -f || echo "Failed to prune Docker system, continuing..."
  needs:
    - build-dotnet-dev
  only:
    - dev

# Build stage for production

# Build .NET applications for production using Docker
build-dotnet-prod:
  stage: build
  tags:
    - docker-builder-prod # Use your self-hosted runner to avoid GitLab minutes
  variables:
    BUILD_DIR: "/tmp/gitlab-build-${CI_PIPELINE_ID}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-nuget
    paths:
      - ${NUGET_PACKAGES_DIRECTORY}
    policy: pull-push
  before_script:
    # Set umask to ensure files are created with permissive permissions
    - umask 0000

    # Clean up any previous build artifacts to avoid permission issues
    - BUILD_DIR="/tmp/gitlab-build-${CI_PIPELINE_ID}"
    - rm -rf $BUILD_DIR || true
    - mkdir -p $BUILD_DIR
    - chmod 777 $BUILD_DIR
    - mkdir -p $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context
    - chmod -R 777 $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context

    # Create a clean copy of the repository to avoid permission issues
    - mkdir -p $BUILD_DIR/repo
    - chmod 777 $BUILD_DIR/repo
    - cp -r * $BUILD_DIR/repo/ || true

    # Create dotnet CLI home directory with proper permissions
    - mkdir -p $DOTNET_CLI_HOME
    - chmod 777 $DOTNET_CLI_HOME
  script:
    # Create NuGet packages directory if it doesn't exist
    - mkdir -p ${NUGET_PACKAGES_DIRECTORY}

    # Build Web Application using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/web-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.JettyApproval.Web/Imip.JettyApproval.Web.csproj && dotnet publish src/Imip.JettyApproval.Web/Imip.JettyApproval.Web.csproj -c Release -o /output"

    # Build DB Migrator using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/migrator-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.JettyApproval.DbMigrator/Imip.JettyApproval.DbMigrator.csproj && dotnet publish src/Imip.JettyApproval.DbMigrator/Imip.JettyApproval.DbMigrator.csproj -c Release -o /output"

    # Create a Docker build context with the published files
    - mkdir -p $BUILD_DIR/docker-context/web $BUILD_DIR/docker-context/db-migrator
    - cp -r $BUILD_DIR/web-build/* $BUILD_DIR/docker-context/web/
    - cp -r $BUILD_DIR/migrator-build/* $BUILD_DIR/docker-context/db-migrator/
    - cp $BUILD_DIR/repo/src/Imip.JettyApproval.Web/entrypoint.sh $BUILD_DIR/docker-context/web/
    - cp $BUILD_DIR/repo/src/Imip.JettyApproval.Web/Dockerfile.new $BUILD_DIR/docker-context/Dockerfile
  artifacts:
    paths:
      - /tmp/gitlab-build-${CI_PIPELINE_ID}/docker-context/
    expire_in: 1 hour
  only:
    - main

# Build Docker images for production on dedicated builder
build-docker-prod:
  stage: build
  tags:
    - docker-builder-prod
  variables:
    DOCKER_DEFAULT_PLATFORM: "linux/amd64" # Explicitly set the target platform
    CLEANUP_OLDER_THAN: "168h" # 7 days in hours format (compatible with Docker)
  before_script:
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    # Cleanup old images to optimize storage
    - echo "Cleaning up old Docker images to optimize storage..."
    - docker image prune -a --filter "until=${CLEANUP_OLDER_THAN}" --force || true
    - docker system prune --volumes -f || true
  script:
    # Set build directory
    - BUILD_DIR="/tmp/gitlab-build-${CI_PIPELINE_ID}"

    # Build Docker images with explicit platform using the prepared context
    - cd $BUILD_DIR/docker-context
    # Build and push web image
    - echo "Building web image with tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA}..."
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=web .
    - docker images | grep $CI_REGISTRY_IMAGE/web || echo "Web image not found in local registry"
    - echo "Pushing web image to registry..."
    - docker push $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push web image"; exit 1; }

    # Copy DB migrator Dockerfile and build
    - cp -f $BUILD_DIR/repo/src/Imip.JettyApproval.DbMigrator/Dockerfile.new ./Dockerfile || cp -f $CI_PROJECT_DIR/src/Imip.JettyApproval.DbMigrator/Dockerfile.new ./Dockerfile
    - echo "Building db-migrator image with tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA}..."
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=db-migrator .
    - docker images | grep $CI_REGISTRY_IMAGE/db-migrator || echo "DB migrator image not found in local registry"
    - echo "Pushing db-migrator image to registry..."
    - docker push $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push db-migrator image"; exit 1; }

    # Verify images exist in registry before tagging
    - echo "Verifying images exist in registry before tagging..."
    - docker pull $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull web image from registry"; exit 1; }
    - docker pull $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull db-migrator image from registry"; exit 1; }

    # Tag with branch name for easier reference
    - echo "Tagging images with branch name $CI_COMMIT_REF_SLUG..."
    - docker tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
  after_script:
    # Clean up build directory
    - BUILD_DIR="/tmp/gitlab-build-${CI_PIPELINE_ID}"
    - |
      echo "Cleaning up build directory: $BUILD_DIR"
      if [ -d "$BUILD_DIR" ]; then
        echo "Setting proper permissions before cleanup..."
        # First, try to fix permissions on problematic directories
        find $BUILD_DIR -type d -name "bin" -o -name "obj" | xargs -I{} chmod -R 777 {} 2>/dev/null || true

        echo "Attempting to remove build directory without changing permissions..."
        # Try to remove without changing permissions first
        rm -rf $BUILD_DIR || {
          echo "Standard removal failed, trying with sudo..."
          # Try with sudo if available
          if command -v sudo &> /dev/null; then
            sudo chmod -R 777 $BUILD_DIR 2>/dev/null || true
            sudo rm -rf $BUILD_DIR || {
              echo "Sudo removal failed, trying with Docker..."
              # If that fails, try using Docker to clean up (runs as root)
              docker run --rm -v $BUILD_DIR:/data alpine:latest sh -c "chmod -R 777 /data && rm -rf /data/*" || {
                echo "Docker removal failed, trying with find command..."
                # If Docker fails, try using find to delete files with current permissions
                find $BUILD_DIR -type f -exec chmod 666 {} \; 2>/dev/null || true
                find $BUILD_DIR -type d -exec chmod 777 {} \; 2>/dev/null || true
                find $BUILD_DIR -type f -exec rm -f {} \; 2>/dev/null || true
                find $BUILD_DIR -type d -empty -delete 2>/dev/null || true
                # Final attempt with force
                rm -rf $BUILD_DIR 2>/dev/null || echo "Failed to remove build directory completely, some files may remain."
              }
            }
          else
            echo "Sudo not available, trying with find command..."
            # If sudo is not available, try using find to delete files with current permissions
            find $BUILD_DIR -type f -exec chmod 666 {} \; 2>/dev/null || true
            find $BUILD_DIR -type d -exec chmod 777 {} \; 2>/dev/null || true
            find $BUILD_DIR -type f -exec rm -f {} \; 2>/dev/null || true
            find $BUILD_DIR -type d -empty -delete 2>/dev/null || true
            # Final attempt with force
            rm -rf $BUILD_DIR 2>/dev/null || echo "Failed to remove build directory completely, some files may remain."
          fi
        }
      else
        echo "Build directory not found, skipping cleanup"
      fi

      # If all else fails, just ignore the errors and continue
      echo "Note: Any remaining permission errors during cleanup can be safely ignored"

    # Additional cleanup after build to free up space
    - |
      echo "Removing build-specific images to free up space..."
      echo "Checking for web image..."
      if docker images | grep -q "$CI_REGISTRY_IMAGE/web.*$CI_COMMIT_SHORT_SHA"; then
        docker rmi $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || echo "Failed to remove web image, continuing..."
      else
        echo "Web image not found locally"
      fi

    - |
      echo "Checking for db-migrator image..."
      if docker images | grep -q "$CI_REGISTRY_IMAGE/db-migrator.*$CI_COMMIT_SHORT_SHA"; then
        docker rmi $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || echo "Failed to remove db-migrator image, continuing..."
      else
        echo "DB migrator image not found locally"
      fi

    # Clean up dangling images and containers
    - |
      echo "Cleaning up dangling images and containers..."
      docker system prune -f || echo "Failed to prune Docker system, continuing..."
  needs:
    - build-dotnet-prod
  only:
    - main

# Create ConfigMap and Secrets for Development
prepare_dev_config:
  stage: migrate
  tags:
    - jettyapprovaldev # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Create namespace if it doesn't exist
    - kubectl create namespace imip-idjas-dev --dry-run=client -o yaml | kubectl apply -f -

    # Apply PersistentVolumeClaim for data protection
    - kubectl apply -f k8s/dev/pvc.yaml

    # Export OpenIddict configuration variables for development
    - export DEV_APP_URL="${DEV_APP_URL}"
    - export DEV_AUTH_APP_URL="${DEV_AUTH_APP_URL}"
    - export DEV_CLIENT_URL="${DEV_CLIENT_URL}"
    - export DEV_CORS_ORIGINS="${DEV_CORS_ORIGINS}"

    # Apply ConfigMap
    - echo "Applying ConfigMap from k8s/dev/configmap.yaml..."
    - envsubst < k8s/dev/configmap.yaml | kubectl apply -f -

    # Apply Secrets
    - echo "Applying Secrets from k8s/dev/secrets.yaml..."
    - envsubst < k8s/dev/secrets.yaml | kubectl apply -f -

    # Apply Certificate Secret
    - echo "Applying Certificate Secret from k8s/dev/certificate-secret.yaml..."
    - envsubst < k8s/dev/certificate-secret.yaml | kubectl apply -f -

    # Create GitLab registry credentials secret
    - echo "Creating GitLab registry credentials secret..."
    - |
      # Create a base64 encoded Docker config JSON for the registry credentials
      REGISTRY_AUTH=$(echo -n "{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$GITLAB_REGISTRY_TOKEN\",\"email\":\"$GITLAB_USER_EMAIL\",\"auth\":\"$(echo -n "$CI_REGISTRY_USER:$GITLAB_REGISTRY_TOKEN" | base64)\"}}}" | base64 -w 0)
      export GITLAB_REGISTRY_AUTH=$REGISTRY_AUTH
      envsubst < k8s/dev/registry-credentials.yaml | kubectl apply -f -
  only:
    - dev

# Run DB Migrator for Development
migrate_dev:
  stage: migrate
  tags:
    - jettyapprovaldev # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Login to Docker registry
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Recreating GitLab registry credentials secret..."
      kubectl delete secret gitlab-registry-credentials -n imip-idjas-dev --ignore-not-found

      # Create a base64 encoded Docker config JSON for the registry credentials
      REGISTRY_AUTH=$(echo -n "{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$GITLAB_REGISTRY_TOKEN\",\"email\":\"$GITLAB_USER_EMAIL\",\"auth\":\"$(echo -n "$CI_REGISTRY_USER:$GITLAB_REGISTRY_TOKEN" | base64)\"}}}" | base64 -w 0)
      export GITLAB_REGISTRY_AUTH=$REGISTRY_AUTH
      envsubst < k8s/dev/registry-credentials.yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-idjas-dev

    # Apply DB Migrator job with proper variable substitution
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/dev/db-migrator-job.yaml
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/dev/db-migrator-job.yaml
    # Verify the namespace in the file
    - echo "Verifying namespace in db-migrator-job.yaml:"
    - grep "namespace:" k8s/dev/db-migrator-job.yaml
    - kubectl apply -f k8s/dev/db-migrator-job.yaml

    # Check migration job status and wait for completion
    - |
      # Verify that we have a valid commit SHA
      if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
        echo "Error: CI_COMMIT_SHORT_SHA is empty. Cannot proceed with migration job."
        exit 1
      fi

      echo "Using commit SHA: $CI_COMMIT_SHORT_SHA"
      JOB_NAME="imip-idjas-db-migrator-$CI_COMMIT_SHORT_SHA"
      echo "Job name: $JOB_NAME"

      # Wait for the job to start
      echo "Waiting for the migration job to start..."
      sleep 10

      # Check if the job exists
      if ! kubectl get "job/$JOB_NAME" -n imip-idjas-dev &>/dev/null; then
        echo "Error: Migration job $JOB_NAME not found. Deployment cannot proceed."
        exit 1
      fi

      # Wait for the job to complete with timeout
      echo "Waiting for migration job to complete (timeout: 10 minutes)..."
      if kubectl wait --for=condition=complete --timeout=600s "job/$JOB_NAME" -n imip-idjas-dev; then
        echo "✅ Migration job completed successfully!"
        
        # Get the pod logs to verify success
        POD_LIST=$(kubectl get pods -n imip-idjas-dev -l "job-name=$JOB_NAME" -o name)
        if [ -n "$POD_LIST" ]; then
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Migration job logs:"
          kubectl logs "pod/$POD_NAME" -n imip-idjas-dev
          
          # Check for success message in logs
          if kubectl logs "pod/$POD_NAME" -n imip-idjas-dev | grep -q "Successfully completed all database migrations"; then
            echo "✅ Migration confirmed successful from logs."
          else
            echo "⚠️ Migration success message not found in logs, but job completed successfully."
          fi
        fi
      else
        echo "❌ Migration job failed or timed out!"
        
        # Get job details and pod logs for debugging
        echo "Job details:"
        kubectl describe "job/$JOB_NAME" -n imip-idjas-dev
        
        POD_LIST=$(kubectl get pods -n imip-idjas-dev -l "job-name=$JOB_NAME" -o name)
        if [ -n "$POD_LIST" ]; then
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Pod status:"
          kubectl get "pod/$POD_NAME" -n imip-idjas-dev -o wide
          echo "Pod logs:"
          kubectl logs "pod/$POD_NAME" -n imip-idjas-dev
        fi
        
        echo "❌ Deployment cannot proceed without successful database migration!"
        exit 1
      fi
  needs:
    - prepare_dev_config
  only:
    - dev

# Deploy to Development
deploy_dev:
  stage: deploy
  tags:
    - idjasdev # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
    url: https://idjas-dev.imip.co.id
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Apply Pod Disruption Budget to ensure minimum availability
    - echo "Applying Pod Disruption Budget..."
    - kubectl apply -f k8s/dev/pod-disruption-budget.yaml

    # Clean up old jobs but preserve running pods for zero-downtime deployment
    - |
      echo "Cleaning up old completed jobs..."
      # List all jobs in the namespace and filter using grep/awk instead of field selectors
      OLD_JOBS=$(kubectl get jobs -n imip-idjas-dev -o name | grep "imip-idjas-db-migrator")
      if [ -n "$OLD_JOBS" ]; then
        echo "Found completed jobs to clean up:"
        echo "$OLD_JOBS"
        # Use a loop to handle each job individually to prevent errors if one job can't be deleted
        for JOB in $OLD_JOBS; do
          echo "Deleting job: $JOB"
          kubectl delete $JOB -n imip-idjas-dev --ignore-not-found || echo "Failed to delete $JOB, continuing..."
        done
      fi

      # Check node resources
      echo "Node resource usage:"
      kubectl describe nodes k8s-worker1 | grep -A 10 "Allocated resources"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Recreating GitLab registry credentials secret for web deployment..."
      kubectl delete secret gitlab-registry-credentials -n imip-idjas-dev --ignore-not-found

      # Create a base64 encoded Docker config JSON for the registry credentials
      REGISTRY_AUTH=$(echo -n "{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$GITLAB_REGISTRY_TOKEN\",\"email\":\"$GITLAB_USER_EMAIL\",\"auth\":\"$(echo -n "$CI_REGISTRY_USER:$GITLAB_REGISTRY_TOKEN" | base64)\"}}}" | base64 -w 0)
      export GITLAB_REGISTRY_AUTH=$REGISTRY_AUTH
      envsubst < k8s/dev/registry-credentials.yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-idjas-dev

    # Apply web deployment using the YAML file
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/dev/web-deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/dev/web-deployment.yaml
    - kubectl apply -f k8s/dev/web-deployment.yaml

    # Apply services
    - kubectl apply -f k8s/dev/web-service.yaml
    # Delete existing NodePort service to avoid port conflict
    - echo "Deleting existing NodePort service to avoid port conflict..."
    # - kubectl delete service imip-idjas-web-nodeport -n imip-idjas-dev --ignore-not-found || true
    # - kubectl delete service imip-idjas-web-nodeport -n imip-idjas-dev --ignore-not-found || true
    # - kubectl delete service imip-idjas-web-nodeport -n imip-idjas-dev --ignore-not-found || true
    # Wait a moment to ensure the port is released
    # - sleep 5
    - kubectl apply -f k8s/dev/web-service-nodeport.yaml

    # Apply ingress
    - kubectl apply -f k8s/dev/ingress.yaml

    # Monitor deployment status with improved handling
    - |
      echo "Monitoring deployment status..."
      if kubectl rollout status deployment/imip-idjas-web -n imip-idjas-dev --timeout=300s; then
        echo "✅ Deployment completed successfully!"
      else
        echo "⚠️ Deployment may not have completed within the timeout period."
        echo "Checking deployment status..."
        kubectl get deployment imip-idjas-web -n imip-idjas-dev -o wide

        echo "Current pod status:"
        kubectl get pods -n imip-idjas-dev -l app=imip-idjas-web -o wide

        # Check for any problematic pods and get their logs
        PROBLEMATIC_PODS=$(kubectl get pods -n imip-idjas-dev -l app=imip-idjas-web --field-selector=status.phase!=Running,status.phase!=Succeeded,status.phase!=Completed -o name)
        if [ -n "$PROBLEMATIC_PODS" ]; then
          echo "Found problematic pods:"
          for POD in $PROBLEMATIC_PODS; do
            echo "Details for $POD:"
            kubectl describe $POD -n imip-idjas-dev
            echo "Logs for $POD:"
            kubectl logs $POD -n imip-idjas-dev --tail=50 || echo "Could not get logs"
          done
        fi

        # Check for any events that might indicate issues
        echo "Recent events:"
        kubectl get events -n imip-idjas-dev --sort-by='.lastTimestamp' | tail -n 20

        # Continue anyway - we don't want to fail the pipeline if some pods are still starting
        echo "Continuing despite deployment issues..."
      fi

      # Check if we have at least one pod running (minimum availability)
      RUNNING_PODS=$(kubectl get pods -n imip-idjas-dev -l app=imip-idjas-web --field-selector=status.phase=Running -o name | wc -l)
      if [ "$RUNNING_PODS" -ge 1 ]; then
        echo "✅ At least one pod is running. Service should be available."
      else
        echo "⚠️ Warning: No running pods found. Service may be unavailable!"
        # We still don't fail the pipeline, but this is a clear warning
      fi
  needs:
    - migrate_dev
  only:
    - dev

# Create ConfigMap and Secrets for Production
prepare_prod_config:
  stage: migrate
  tags:
    - jettyapprovalprod
  environment:
    name: production
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Create namespace if it doesn't exist
    - kubectl create namespace imip-idjas-prod --dry-run=client -o yaml | kubectl apply -f -

    # Apply PersistentVolumeClaim for data protection
    - kubectl apply -f k8s/prod/pvc.yaml

    # Export OpenIddict configuration variables for production
    - export PROD_APP_URL="${PROD_APP_URL}"
    - export PROD_CLIENT_URL="${PROD_CLIENT_URL}"
    - export PROD_CORS_ORIGINS="${PROD_CORS_ORIGINS}"

    # Apply ConfigMap
    - echo "Applying ConfigMap from k8s/prod/configmap.yaml..."
    - envsubst < k8s/prod/configmap.yaml | kubectl apply -f -

    # Apply Secrets
    - echo "Applying Secrets from k8s/prod/secrets.yaml..."
    - envsubst < k8s/prod/secrets.yaml | kubectl apply -f -

    # Apply Certificate Secret
    - echo "Applying Certificate Secret from k8s/prod/certificate-secret.yaml..."
    - envsubst < k8s/prod/certificate-secret.yaml | kubectl apply -f -

    # Create GitLab registry credentials secret
    - echo "Creating GitLab registry credentials secret..."
    - |
      # Create a base64 encoded Docker config JSON for the registry credentials
      REGISTRY_AUTH=$(echo -n "{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$GITLAB_REGISTRY_TOKEN\",\"email\":\"$GITLAB_USER_EMAIL\",\"auth\":\"$(echo -n "$CI_REGISTRY_USER:$GITLAB_REGISTRY_TOKEN" | base64)\"}}}" | base64 -w 0)
      export GITLAB_REGISTRY_AUTH=$REGISTRY_AUTH
      envsubst < k8s/prod/registry-credentials.yaml | kubectl apply -f -
  only:
    - main

# Run DB Migrator for Production
migrate_prod:
  stage: migrate
  tags:
    - jettyapprovalprod
  environment:
    name: production
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Login to Docker registry
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Recreating GitLab registry credentials secret..."
      kubectl delete secret gitlab-registry-credentials -n imip-idjas-prod --ignore-not-found

      # Create a base64 encoded Docker config JSON for the registry credentials
      REGISTRY_AUTH=$(echo -n "{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$GITLAB_REGISTRY_TOKEN\",\"email\":\"$GITLAB_USER_EMAIL\",\"auth\":\"$(echo -n "$CI_REGISTRY_USER:$GITLAB_REGISTRY_TOKEN" | base64)\"}}}" | base64 -w 0)
      export GITLAB_REGISTRY_AUTH=$REGISTRY_AUTH
      envsubst < k8s/prod/registry-credentials.yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-idjas-prod

    # Apply DB Migrator job with proper variable substitution
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/prod/db-migrator-job.yaml
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/prod/db-migrator-job.yaml
    # Verify the namespace in the file
    - echo "Verifying namespace in db-migrator-job.yaml:"
    - grep "namespace:" k8s/prod/db-migrator-job.yaml
    - kubectl apply -f k8s/prod/db-migrator-job.yaml

    # Check migration job status and wait for completion
    - |
      # Verify that we have a valid commit SHA
      if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
        echo "Error: CI_COMMIT_SHORT_SHA is empty. Cannot proceed with migration job."
        exit 1
      fi

      echo "Using commit SHA: $CI_COMMIT_SHORT_SHA"
      JOB_NAME="imip-idjas-db-migrator-$CI_COMMIT_SHORT_SHA"
      echo "Job name: $JOB_NAME"

      # Wait for the job to start
      echo "Waiting for the migration job to start..."
      sleep 10

      # Check if the job exists
      if ! kubectl get "job/$JOB_NAME" -n imip-idjas-prod &>/dev/null; then
        echo "Error: Migration job $JOB_NAME not found. Deployment cannot proceed."
        exit 1
      fi

      # Wait for the job to complete with timeout
      echo "Waiting for migration job to complete (timeout: 10 minutes)..."
      if kubectl wait --for=condition=complete --timeout=600s "job/$JOB_NAME" -n imip-idjas-prod; then
        echo "✅ Migration job completed successfully!"
        
        # Get the pod logs to verify success
        POD_LIST=$(kubectl get pods -n imip-idjas-prod -l "job-name=$JOB_NAME" -o name)
        if [ -n "$POD_LIST" ]; then
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Migration job logs:"
          kubectl logs "pod/$POD_NAME" -n imip-idjas-prod
          
          # Check for success message in logs
          if kubectl logs "pod/$POD_NAME" -n imip-idjas-prod | grep -q "Successfully completed all database migrations"; then
            echo "✅ Migration confirmed successful from logs."
          else
            echo "⚠️ Migration success message not found in logs, but job completed successfully."
          fi
        fi
      else
        echo "❌ Migration job failed or timed out!"
        
        # Get job details and pod logs for debugging
        echo "Job details:"
        kubectl describe "job/$JOB_NAME" -n imip-idjas-prod
        
        POD_LIST=$(kubectl get pods -n imip-idjas-prod -l "job-name=$JOB_NAME" -o name)
        if [ -n "$POD_LIST" ]; then
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Pod status:"
          kubectl get "pod/$POD_NAME" -n imip-idjas-prod -o wide
          echo "Pod logs:"
          kubectl logs "pod/$POD_NAME" -n imip-idjas-prod
        fi
        
        echo "❌ Deployment cannot proceed without successful database migration!"
        exit 1
      fi
  needs:
    - prepare_prod_config
  only:
    - main

# Deploy to Production
deploy_prod:
  stage: deploy
  tags:
    - idjasprod
  environment:
    name: production
    url: https://idjas.imip.co.id
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Apply Pod Disruption Budget to ensure minimum availability
    - echo "Applying Pod Disruption Budget..."
    - kubectl apply -f k8s/prod/pod-disruption-budget.yaml

    # Clean up old jobs but preserve running pods for zero-downtime deployment
    - |
      echo "Cleaning up old completed jobs..."
      # List all jobs in the namespace and filter using grep/awk instead of field selectors
      OLD_JOBS=$(kubectl get jobs -n imip-idjas-prod -o name | grep "imip-idjas-db-migrator")
      if [ -n "$OLD_JOBS" ]; then
        echo "Found completed jobs to clean up:"
        echo "$OLD_JOBS"
        # Use a loop to handle each job individually to prevent errors if one job can't be deleted
        for JOB in $OLD_JOBS; do
          echo "Deleting job: $JOB"
          kubectl delete $JOB -n imip-idjas-prod --ignore-not-found || echo "Failed to delete $JOB, continuing..."
        done
      fi

      # Check node resources
      echo "Node resource usage:"
      kubectl describe nodes imprdapp28 | grep -A 10 "Allocated resources"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Recreating GitLab registry credentials secret for web deployment..."
      kubectl delete secret gitlab-registry-credentials -n imip-idjas-prod --ignore-not-found

      # Create a base64 encoded Docker config JSON for the registry credentials
      REGISTRY_AUTH=$(echo -n "{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$GITLAB_REGISTRY_TOKEN\",\"email\":\"$GITLAB_USER_EMAIL\",\"auth\":\"$(echo -n "$CI_REGISTRY_USER:$GITLAB_REGISTRY_TOKEN" | base64)\"}}}" | base64 -w 0)
      export GITLAB_REGISTRY_AUTH=$REGISTRY_AUTH
      envsubst < k8s/prod/registry-credentials.yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-idjas-prod

    # Apply web deployment using the YAML file
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/prod/web-deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/prod/web-deployment.yaml
    - kubectl apply -f k8s/prod/web-deployment.yaml

    # Apply services
    - kubectl apply -f k8s/prod/web-service.yaml
    # Delete existing NodePort service to avoid port conflict
    - echo "Deleting existing NodePort service to avoid port conflict..."
    # - kubectl delete service imip-idjas-web-nodeport -n imip-idjas-prod --ignore-not-found || true
    # - kubectl delete service imip-idjas-web-nodeport -n imip-idjas-prod --ignore-not-found || true
    # - kubectl delete service imip-idjas-web-nodeport -n imip-idjas-prod --ignore-not-found || true
    # Wait a moment to ensure the port is released
    # - sleep 5
    - kubectl apply -f k8s/prod/web-service-nodeport.yaml

    # Apply ingress
    - kubectl apply -f k8s/prod/ingress.yaml

    # Monitor deployment status with improved handling
    - |
      echo "Monitoring deployment status..."
      if kubectl rollout status deployment/imip-idjas-web -n imip-idjas-prod --timeout=300s; then
        echo "✅ Deployment completed successfully!"
      else
        echo "⚠️ Deployment may not have completed within the timeout period."
        echo "Checking deployment status..."
        kubectl get deployment imip-idjas-web -n imip-idjas-prod -o wide

        echo "Current pod status:"
        kubectl get pods -n imip-idjas-prod -l app=imip-idjas-web -o wide

        # Check for any problematic pods and get their logs
        PROBLEMATIC_PODS=$(kubectl get pods -n imip-idjas-prod -l app=imip-idjas-web --field-selector=status.phase!=Running,status.phase!=Succeeded,status.phase!=Completed -o name)
        if [ -n "$PROBLEMATIC_PODS" ]; then
          echo "Found problematic pods:"
          for POD in $PROBLEMATIC_PODS; do
            echo "Details for $POD:"
            kubectl describe $POD -n imip-idjas-prod
            echo "Logs for $POD:"
            kubectl logs $POD -n imip-idjas-prod --tail=50 || echo "Could not get logs"
          done
        fi

        # Check for any events that might indicate issues
        echo "Recent events:"
        kubectl get events -n imip-idjas-prod --sort-by='.lastTimestamp' | tail -n 20

        # Continue anyway - we don't want to fail the pipeline if some pods are still starting
        echo "Continuing despite deployment issues..."
      fi

      # Check if we have at least one pod running (minimum availability)
      RUNNING_PODS=$(kubectl get pods -n imip-idjas-prod -l app=imip-idjas-web --field-selector=status.phase=Running -o name | wc -l)
      if [ "$RUNNING_PODS" -ge 1 ]; then
        echo "✅ At least one pod is running. Service should be available."
      else
        echo "⚠️ Warning: No running pods found. Service may be unavailable!"
        # We still don't fail the pipeline, but this is a clear warning
      fi
  needs:
    - migrate_prod
  only:
    - main
