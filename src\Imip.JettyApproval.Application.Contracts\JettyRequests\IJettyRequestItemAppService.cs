using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Application service interface for JettyRequestItem entity
/// </summary>
public interface IJettyRequestItemAppService :
    ICrudAppService<
        JettyRequestItemDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateJettyRequestItemDto,
        CreateUpdateJettyRequestItemDto>
{
    Task<PagedResultDto<JettyRequestItemDto>> FilterListAsync(QueryParametersDto parameters);
}