using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services;

public class AppToAppService
{
    private readonly ITokenService _tokenService;
    private readonly SilentTokenRefreshService _silentTokenRefreshService;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AppToAppService> _logger;

    public AppToAppService(
        ITokenService tokenService,
        SilentTokenRefreshService silentTokenRefreshService,
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<AppToAppService> logger)
    {
        _tokenService = tokenService;
        _silentTokenRefreshService = silentTokenRefreshService;
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Calls another application using the SSO token with OIDC-compliant silent refresh
    /// </summary>
    public async Task<T> CallOtherAppAsync<T>(string appUrl, string endpoint, object data = null)
    {
        try
        {
            // First, try silent token refresh (OIDC standard approach)
            var silentRefreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
            if (silentRefreshSuccess)
            {
                _logger.LogDebug("Silent token refresh completed successfully");
            }

            // Get a valid token (automatically refreshes if needed)
            var accessToken = await _tokenService.GetValidAccessTokenAsync();

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No valid access token available for app-to-app call");

                // Check if refresh token is also expired
                var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();
                if (isRefreshTokenExpired)
                {
                    _logger.LogWarning("Refresh token is expired, user needs to re-authenticate");
                    throw new UnauthorizedAccessException("Authentication required. Please log in again.");
                }
                else
                {
                    _logger.LogWarning("Access token is expired but refresh token is still valid");
                    throw new UnauthorizedAccessException("Token refresh required. Please try again.");
                }
            }

            // Set the authorization header with the SSO token
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", accessToken);

            // Add any additional headers if needed
            _httpClient.DefaultRequestHeaders.Add("X-App-Name", "JettyApproval");
            _httpClient.DefaultRequestHeaders.Add("X-App-Version", "1.0");

            var fullUrl = $"{appUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
            _logger.LogInformation("Calling other app: {Url}", fullUrl);

            HttpResponseMessage response;

            if (data != null)
            {
                // POST request with data
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                response = await _httpClient.PostAsync(fullUrl, content);
            }
            else
            {
                // GET request
                response = await _httpClient.GetAsync(fullUrl);
            }

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("App-to-app call successful: {StatusCode}", response.StatusCode);

                // Deserialize the response
                return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("App-to-app call returned 401, attempting silent token refresh");

                // Try silent refresh first (OIDC standard)
                var refreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
                if (refreshSuccess)
                {
                    // Get the new token
                    var newToken = await _tokenService.GetValidAccessTokenAsync();
                    if (!string.IsNullOrEmpty(newToken))
                    {
                        _httpClient.DefaultRequestHeaders.Authorization =
                            new AuthenticationHeaderValue("Bearer", newToken);

                        // Retry the request with new token
                        if (data != null)
                        {
                            var json = JsonSerializer.Serialize(data);
                            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                            response = await _httpClient.PostAsync(fullUrl, content);
                        }
                        else
                        {
                            response = await _httpClient.GetAsync(fullUrl);
                        }

                        if (response.IsSuccessStatusCode)
                        {
                            var responseContent = await response.Content.ReadAsStringAsync();
                            _logger.LogInformation("App-to-app call successful after silent token refresh: {StatusCode}", response.StatusCode);
                            return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true
                            });
                        }
                    }
                }

                // If silent refresh failed, check if refresh token is expired
                var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();
                if (isRefreshTokenExpired)
                {
                    _logger.LogWarning("Refresh token is expired, user needs to re-authenticate");
                    throw new UnauthorizedAccessException("Authentication required. Please log in again.");
                }
                else
                {
                    throw new UnauthorizedAccessException("App-to-app call failed after token refresh");
                }
            }
            else
            {
                _logger.LogError("App-to-app call failed with status: {StatusCode}", response.StatusCode);
                throw new HttpRequestException($"App-to-app call failed with status: {response.StatusCode}");
            }
        }
        catch (UnauthorizedAccessException)
        {
            // Re-throw UnauthorizedAccessException as-is for proper handling
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in app-to-app call");
            throw;
        }
    }

    /// <summary>
    /// Example: Call another app's API
    /// </summary>
    public async Task<object> GetDataFromOtherAppAsync()
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data");
    }

    /// <summary>
    /// Example: Send data to another app
    /// </summary>
    public async Task<object> SendDataToOtherAppAsync(object data)
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data", data);
    }
}