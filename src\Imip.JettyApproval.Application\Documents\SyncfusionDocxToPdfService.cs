using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Imip.JettyApproval.Attachments;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocIORenderer;
using Syncfusion.Pdf;
using Syncfusion.Pdf.Parsing;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Uow;

namespace Imip.JettyApproval.Documents;

/// <summary>
/// Service for converting DOCX files to PDF using Syncfusion.DocIO
/// </summary>
[RemoteService(false)] // Exclude from auto-API generation
public class SyncfusionDocxToPdfService : ApplicationService, ISyncfusionDocxToPdfService
{
    private readonly ILogger<SyncfusionDocxToPdfService> _logger;
    private readonly IBlobContainer _blobContainer;
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IConfiguration _configuration;

    // Semaphore to limit concurrent document conversions to prevent memory exhaustion
    private static SemaphoreSlim? _conversionSemaphore;
    private static int _activeConversions = 0;
    private static readonly object _semaphoreLock = new();

    public SyncfusionDocxToPdfService(
        ILogger<SyncfusionDocxToPdfService> logger,
        IBlobContainerFactory blobContainerFactory,
        IAttachmentRepository attachmentRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IConfiguration configuration)
    {
        _logger = logger;
        _blobContainer = blobContainerFactory.Create("default");
        _attachmentRepository = attachmentRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _configuration = configuration;

        // Initialize semaphore with configurable limit
        InitializeSemaphore();

        // Register Syncfusion license key
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(
            "Mgo+DSMBMAY9C3t2XFhhQlJHfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5WdERiWXpbc31QQWlbWkZ/");
    }

    private void InitializeSemaphore()
    {
        if (_conversionSemaphore == null)
        {
            lock (_semaphoreLock)
            {
                if (_conversionSemaphore == null)
                {
                    // Get the max concurrent conversions from configuration, default to 2 for dev, 4 for prod
                    var maxConcurrentConversions = _configuration.GetValue<int>("DocumentConversion:MaxConcurrentConversions", 2);
                    _conversionSemaphore = new SemaphoreSlim(maxConcurrentConversions, maxConcurrentConversions);
                    _logger.LogInformation("Initialized document conversion semaphore with limit: {MaxConcurrent}", maxConcurrentConversions);
                }
            }
        }
    }

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion.DocIO
    /// </summary>
    /// <param name="docxBytes">The DOCX file content as byte array</param>
    /// <param name="filename">The filename without extension</param>
    /// <returns>A FileDto containing the PDF content</returns>
    public async Task<FileDto> ConvertDocxToPdfAsync(byte[] docxBytes, string filename)
    {
        try
        {
            using var docxStream = new MemoryStream(docxBytes);
            return await ConvertDocxToPdfAsync(docxStream, filename);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting DOCX to PDF using Syncfusion");
            throw new UserFriendlyException(L["ErrorConvertingDocxToPdf"], ex.Message);
        }
    }

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion.DocIO
    /// </summary>
    /// <param name="docxStream">The DOCX file content as stream</param>
    /// <param name="filename">The filename without extension</param>
    /// <returns>A FileDto containing the PDF content</returns>
    public async Task<FileDto> ConvertDocxToPdfAsync(Stream docxStream, string filename)
    {
        // Wait for semaphore to limit concurrent conversions
        await _conversionSemaphore.WaitAsync();

        try
        {
            Interlocked.Increment(ref _activeConversions);
            _logger.LogInformation("Starting PDF conversion for {Filename}. Active conversions: {ActiveCount}",
                filename, _activeConversions);

            // Create temporary files for processing
            var tempDocxPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}_{filename}.docx");
            var tempPdfPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}_{filename}.pdf");

            try
            {
                // Check available memory before conversion
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var memoryBefore = GC.GetTotalMemory(false);
                _logger.LogDebug("Memory before conversion: {MemoryMB} MB", memoryBefore / 1024 / 1024);

                // Load the document
                using var wordDocument = new WordDocument(docxStream, FormatType.Docx);

                // Initialize DocIO renderer with optimized settings for Linux containers
                using var docIORenderer = new DocIORenderer();

                // Configure PDF optimization settings to reduce file size
                ConfigurePdfOptimization(docIORenderer);

                // Convert to PDF
                using var pdfDocument = docIORenderer.ConvertToPDF(wordDocument);

                // Apply additional PDF optimization
                OptimizePdfDocument(pdfDocument);

                // Save the PDF to a memory stream
                using var pdfStream = new MemoryStream();
                pdfDocument.Save(pdfStream);
                pdfStream.Position = 0;

                // Get the PDF bytes
                var pdfBytes = pdfStream.ToArray();

                // Log detailed PDF analysis before optimization
                AnalyzePdfContent(pdfBytes, filename);

                // Apply post-processing optimization if the file is too large
                var maxSizeForOptimization = _configuration.GetValue<double>("PdfOptimization:MaxFileSizeForPostProcessingMB", 2.0);
                if (pdfBytes.Length / 1024.0 / 1024.0 > maxSizeForOptimization)
                {
                    _logger.LogWarning("PDF file {Filename} is {SizeMB:F2} MB, applying post-processing optimization",
                        filename, pdfBytes.Length / 1024.0 / 1024.0);

                    pdfBytes = await PostProcessOptimizePdf(pdfBytes, filename);
                }

                // Save the PDF to a temporary file (for potential SFTP storage)
                await File.WriteAllBytesAsync(tempPdfPath, pdfBytes);

                // Store the PDF in SFTP and create an attachment record
                await SavePdfToSftpAsync(pdfBytes, $"{filename}.pdf", tempPdfPath);

                var memoryAfter = GC.GetTotalMemory(false);
                _logger.LogDebug("Memory after conversion: {MemoryMB} MB, Difference: {DiffMB} MB",
                    memoryAfter / 1024 / 1024, (memoryAfter - memoryBefore) / 1024 / 1024);

                // Log detailed file size information for debugging
                var pdfSizeKB = pdfBytes.Length / 1024.0;
                var pdfSizeMB = pdfSizeKB / 1024.0;
                var docxSizeKB = docxStream.Length / 1024.0;

                _logger.LogInformation("Successfully converted {Filename} to PDF. DOCX size: {DocxSizeKB:F2} KB, PDF size: {PdfSizeKB:F2} KB ({PdfSizeMB:F2} MB), Compression ratio: {CompressionRatio:F2}x",
                    filename, docxSizeKB, pdfSizeKB, pdfSizeMB, docxSizeKB / pdfSizeKB);

                // Log warning if PDF is unusually large
                var maxSizeWarningMB = _configuration.GetValue<double>("PdfOptimization:MaxFileSizeWarningMB", 5.0);
                if (pdfSizeMB > maxSizeWarningMB)
                {
                    _logger.LogWarning("Generated PDF file {Filename} is unusually large: {PdfSizeMB:F2} MB (threshold: {ThresholdMB:F1} MB). This may indicate font embedding or image optimization issues in the container environment.",
                        filename, pdfSizeMB, maxSizeWarningMB);
                }

                // Return the PDF file
                return new FileDto(
                    $"{filename}.pdf",
                    "application/pdf",
                    pdfBytes
                );
            }
            finally
            {
                // Clean up temporary files
                try
                {
                    if (File.Exists(tempDocxPath))
                        File.Delete(tempDocxPath);

                    if (File.Exists(tempPdfPath))
                        File.Delete(tempPdfPath);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete temporary files");
                }

                // Force garbage collection after conversion
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }
        catch (OutOfMemoryException ex)
        {
            _logger.LogError(ex, "Out of memory error during PDF conversion for {Filename}", filename);
            throw new UserFriendlyException("The document is too large to convert. Please try with a smaller document or contact support.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting DOCX to PDF using Syncfusion for {Filename}", filename);
            throw new UserFriendlyException(L["ErrorConvertingDocxToPdf"], ex.Message);
        }
        finally
        {
            Interlocked.Decrement(ref _activeConversions);
            _conversionSemaphore.Release();
            _logger.LogDebug("Released semaphore. Active conversions: {ActiveCount}", _activeConversions);
        }
    }

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion.DocIO
    /// </summary>
    /// <param name="docxFilePath">The path to the DOCX file</param>
    /// <param name="filename">The filename without extension (if null, the name from the path will be used)</param>
    /// <returns>A FileDto containing the PDF content</returns>
    public async Task<FileDto> ConvertDocxToPdfFromFileAsync(string docxFilePath, string? filename = null)
    {
        try
        {
            // If no filename is provided, use the name from the path
            if (string.IsNullOrEmpty(filename))
            {
                filename = Path.GetFileNameWithoutExtension(docxFilePath);
            }

            // Read the DOCX file
            var docxBytes = await File.ReadAllBytesAsync(docxFilePath);

            // Convert to PDF
            return await ConvertDocxToPdfAsync(docxBytes, filename);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting DOCX to PDF from file using Syncfusion");
            throw new UserFriendlyException(L["ErrorConvertingDocxToPdf"], ex.Message);
        }
    }

    /// <summary>
    /// Saves a PDF file to SFTP storage and creates an attachment record
    /// </summary>
    /// <param name="pdfBytes">The PDF content</param>
    /// <param name="fileName">The file name</param>
    /// <param name="tempFilePath">The temporary file path (not used, but kept for reference)</param>
    /// <returns>The created attachment</returns>
    private async Task<Attachment> SavePdfToSftpAsync(byte[] pdfBytes, string fileName, string _)
    {
        try
        {
            // Generate a unique blob name
            var blobName = $"documents/{Guid.NewGuid():N}_{fileName}";

            // Save the file to blob storage
            using var memoryStream = new MemoryStream(pdfBytes);
            await _blobContainer.SaveAsync(blobName, memoryStream);

            // Create an attachment record
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            try
            {
                var attachment = new Attachment(
                    GuidGenerator.Create(),
                    fileName,
                    "application/pdf",
                    pdfBytes.Length,
                    blobName,
                    null,
                    null,
                    null
                );

                await _attachmentRepository.InsertAsync(attachment);
                await uow.CompleteAsync();

                return attachment;
            }
            catch
            {
                await uow.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving PDF to SFTP");
            throw;
        }
    }

    /// <summary>
    /// Configures PDF optimization settings for DocIORenderer to reduce file size
    /// </summary>
    /// <param name="docIORenderer">The DocIORenderer instance to configure</param>
    private void ConfigurePdfOptimization(DocIORenderer docIORenderer)
    {
        try
        {
            // Get configuration settings
            var embedFonts = _configuration.GetValue<bool>("PdfOptimization:EmbedFonts", false);
            var optimizeImages = _configuration.GetValue<bool>("PdfOptimization:OptimizeIdenticalImages", true);

            // Configure font embedding settings to reduce file size
            docIORenderer.Settings.EmbedFonts = embedFonts;

            // Configure image optimization
            docIORenderer.Settings.OptimizeIdenticalImages = optimizeImages;

            // Additional optimization settings for Syncfusion DocIORenderer
            try
            {
                // Try to access additional settings that might be available
                var settingsType = docIORenderer.Settings.GetType();

                // Check for AutoTag property and disable it to reduce file size
                var autoTagProperty = settingsType.GetProperty("AutoTag");
                if (autoTagProperty != null && autoTagProperty.CanWrite)
                {
                    autoTagProperty.SetValue(docIORenderer.Settings, false);
                    _logger.LogDebug("Disabled AutoTag for PDF optimization");
                }

                // Check for PreserveFormFields and disable it
                var preserveFormFieldsProperty = settingsType.GetProperty("PreserveFormFields");
                if (preserveFormFieldsProperty != null && preserveFormFieldsProperty.CanWrite)
                {
                    preserveFormFieldsProperty.SetValue(docIORenderer.Settings, false);
                    _logger.LogDebug("Disabled PreserveFormFields for PDF optimization");
                }

                // Check for ExportBookmarks and disable it
                var exportBookmarksProperty = settingsType.GetProperty("ExportBookmarks");
                if (exportBookmarksProperty != null && exportBookmarksProperty.CanWrite)
                {
                    exportBookmarksProperty.SetValue(docIORenderer.Settings, false);
                    _logger.LogDebug("Disabled ExportBookmarks for PDF optimization");
                }
            }
            catch (Exception reflectionEx)
            {
                _logger.LogDebug(reflectionEx, "Could not apply additional optimization settings via reflection");
            }

            _logger.LogInformation("Configured PDF optimization settings: EmbedFonts={EmbedFonts}, OptimizeIdenticalImages={OptimizeImages}",
                embedFonts, optimizeImages);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to configure PDF optimization settings, using defaults");
        }
    }

    /// <summary>
    /// Applies additional optimization to the generated PDF document
    /// </summary>
    /// <param name="pdfDocument">The PDF document to optimize</param>
    private void OptimizePdfDocument(PdfDocument pdfDocument)
    {
        try
        {
            // Configure compression settings
            pdfDocument.Compression = PdfCompressionLevel.Best;

            // Optimize for web viewing (linearization)
            pdfDocument.FileStructure.IncrementalUpdate = false;

            // Set PDF version for better compression
            pdfDocument.FileStructure.Version = PdfVersion.Version1_7;

            // Additional optimization for images and fonts
            try
            {
                // Iterate through all pages to optimize content
                foreach (PdfPage page in pdfDocument.Pages)
                {
                    OptimizePdfPage(page);
                }
            }
            catch (Exception pageOptimizationEx)
            {
                _logger.LogWarning(pageOptimizationEx, "Failed to optimize PDF pages, continuing with basic optimization");
            }

            _logger.LogDebug("Applied PDF document optimization: Compression=Best, Version=1.7");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to apply PDF document optimization, using defaults");
        }
    }

    /// <summary>
    /// Optimizes individual PDF pages to reduce file size
    /// </summary>
    /// <param name="page">The PDF page to optimize</param>
    private void OptimizePdfPage(PdfPage page)
    {
        try
        {
            // Get the page graphics to access resources
            var graphics = page.Graphics;

            // Try to optimize images on the page
            var pageType = page.GetType();
            var resourcesProperty = pageType.GetProperty("Resources");

            if (resourcesProperty != null)
            {
                var resources = resourcesProperty.GetValue(page);
                if (resources != null)
                {
                    OptimizePageResources(resources);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Could not optimize page resources");
        }
    }

    /// <summary>
    /// Optimizes page resources like images and fonts
    /// </summary>
    /// <param name="resources">The page resources to optimize</param>
    private void OptimizePageResources(object resources)
    {
        try
        {
            var resourcesType = resources.GetType();

            // Try to access and optimize images
            var imagesProperty = resourcesType.GetProperty("Images");
            if (imagesProperty != null)
            {
                var images = imagesProperty.GetValue(resources);
                if (images != null)
                {
                    _logger.LogDebug("Found images in PDF page resources, attempting optimization");
                    // Additional image optimization could be implemented here
                }
            }

            // Try to access and optimize fonts
            var fontsProperty = resourcesType.GetProperty("Fonts");
            if (fontsProperty != null)
            {
                var fonts = fontsProperty.GetValue(resources);
                if (fonts != null)
                {
                    _logger.LogDebug("Found fonts in PDF page resources");
                    // Font optimization could be implemented here
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Could not access page resources for optimization");
        }
    }

    /// <summary>
    /// Analyzes PDF content to understand what's causing large file sizes
    /// </summary>
    /// <param name="pdfBytes">The PDF content to analyze</param>
    /// <param name="filename">The filename for logging</param>
    private void AnalyzePdfContent(byte[] pdfBytes, string filename)
    {
        try
        {
            // Basic analysis of PDF content
            var pdfSizeMB = pdfBytes.Length / 1024.0 / 1024.0;

            // Convert to string to analyze content (this is a rough analysis)
            var pdfText = System.Text.Encoding.Latin1.GetString(pdfBytes);

            // Count font references
            var fontCount = CountOccurrences(pdfText, "/Font");
            var imageCount = CountOccurrences(pdfText, "/Image");
            var streamCount = CountOccurrences(pdfText, "stream");
            var fontDataSize = EstimateFontDataSize(pdfText);

            _logger.LogInformation("PDF Analysis for {Filename}: Size={SizeMB:F2}MB, Fonts={FontCount}, Images={ImageCount}, Streams={StreamCount}, EstimatedFontDataKB={FontDataKB:F0}",
                filename, pdfSizeMB, fontCount, imageCount, streamCount, fontDataSize / 1024.0);

            // Check for specific font issues
            if (pdfText.Contains("Noto Sans CJK") || pdfText.Contains("NotoSansCJK"))
            {
                _logger.LogWarning("PDF {Filename} contains CJK fonts which may be causing large file size", filename);
            }

            if (fontDataSize > 1024 * 1024) // > 1MB of font data
            {
                _logger.LogWarning("PDF {Filename} contains large amount of font data: {FontDataMB:F2}MB",
                    filename, fontDataSize / 1024.0 / 1024.0);
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Could not analyze PDF content for {Filename}", filename);
        }
    }

    /// <summary>
    /// Estimates the size of font data in the PDF
    /// </summary>
    /// <param name="pdfText">The PDF content as text</param>
    /// <returns>Estimated font data size in bytes</returns>
    private long EstimateFontDataSize(string pdfText)
    {
        try
        {
            long totalFontSize = 0;
            var lines = pdfText.Split('\n');

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                if (line.Contains("/Length") && i > 0 && lines[i - 1].Contains("/Font"))
                {
                    // Try to extract length value
                    var lengthMatch = System.Text.RegularExpressions.Regex.Match(line, @"/Length\s+(\d+)");
                    if (lengthMatch.Success && long.TryParse(lengthMatch.Groups[1].Value, out var length))
                    {
                        totalFontSize += length;
                    }
                }
            }

            return totalFontSize;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// Counts occurrences of a substring in a string
    /// </summary>
    /// <param name="text">The text to search</param>
    /// <param name="substring">The substring to count</param>
    /// <returns>Number of occurrences</returns>
    private int CountOccurrences(string text, string substring)
    {
        try
        {
            int count = 0;
            int index = 0;
            while ((index = text.IndexOf(substring, index)) != -1)
            {
                count++;
                index += substring.Length;
            }
            return count;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// Applies post-processing optimization to reduce PDF file size
    /// </summary>
    /// <param name="originalPdfBytes">The original PDF bytes</param>
    /// <param name="filename">The filename for logging</param>
    /// <returns>Optimized PDF bytes</returns>
    private async Task<byte[]> PostProcessOptimizePdf(byte[] originalPdfBytes, string filename)
    {
        try
        {
            _logger.LogInformation("Starting post-processing optimization for {Filename}", filename);

            // Method 1: Try to recreate the PDF with minimal settings
            var optimizedBytes = await RecreateOptimizedPdf(originalPdfBytes, filename);

            var originalSizeMB = originalPdfBytes.Length / 1024.0 / 1024.0;
            var optimizedSizeMB = optimizedBytes.Length / 1024.0 / 1024.0;
            var reductionPercent = ((originalSizeMB - optimizedSizeMB) / originalSizeMB) * 100;

            _logger.LogInformation("Post-processing optimization for {Filename}: {OriginalMB:F2}MB -> {OptimizedMB:F2}MB ({ReductionPercent:F1}% reduction)",
                filename, originalSizeMB, optimizedSizeMB, reductionPercent);

            return optimizedBytes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to post-process optimize PDF {Filename}, returning original", filename);
            return originalPdfBytes;
        }
    }

    /// <summary>
    /// Recreates the PDF with optimized settings
    /// </summary>
    /// <param name="originalPdfBytes">The original PDF bytes</param>
    /// <param name="filename">The filename for logging</param>
    /// <returns>Recreated optimized PDF bytes</returns>
    private async Task<byte[]> RecreateOptimizedPdf(byte[] originalPdfBytes, string filename)
    {
        try
        {
            // Load the original PDF
            using var originalStream = new MemoryStream(originalPdfBytes);
            using var loadedDocument = new PdfLoadedDocument(originalStream);

            // Create a new document with optimization
            using var optimizedDocument = new PdfDocument();
            optimizedDocument.Compression = PdfCompressionLevel.Best;
            optimizedDocument.FileStructure.Version = PdfVersion.Version1_7;

            // Copy pages with optimization
            for (int i = 0; i < loadedDocument.Pages.Count; i++)
            {
                var sourcePage = loadedDocument.Pages[i];
                var newPage = optimizedDocument.Pages.Add();

                // Copy content with optimization
                await CopyPageContentOptimized(sourcePage, newPage);
            }

            // Save the optimized document
            using var optimizedStream = new MemoryStream();
            optimizedDocument.Save(optimizedStream);

            return optimizedStream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to recreate optimized PDF for {Filename}", filename);
            throw;
        }
    }

    /// <summary>
    /// Copies page content with optimization
    /// </summary>
    /// <param name="sourcePage">The source page</param>
    /// <param name="targetPage">The target page</param>
    private async Task CopyPageContentOptimized(PdfPageBase sourcePage, PdfPage targetPage)
    {
        try
        {
            // This is a simplified approach - in practice, you might need more sophisticated content copying
            // For now, we'll rely on the document-level optimization
            await Task.CompletedTask; // Placeholder for async operation
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Could not optimize page content copy");
        }
    }
}