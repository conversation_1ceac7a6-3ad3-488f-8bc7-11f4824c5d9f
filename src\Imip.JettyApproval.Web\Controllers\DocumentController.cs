using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Documents;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[RemoteService]
[Route("api/idjas/document")]
[Authorize]
public class DocumentController : AbpController
{
    private readonly IDocumentService _documentService;
    private readonly IApplicationDocumentService _applicationDocumentService;

    public DocumentController(
        IDocumentService documentService,
        IApplicationDocumentService applicationDocumentService)
    {
        _documentService = documentService;
        _applicationDocumentService = applicationDocumentService;
    }

    [HttpPost]
    [Route("templates/upload")]
    [Consumes("multipart/form-data")]
    public async Task<ActionResult<DocumentTemplateDto>> UploadTemplateAsync([FromForm] UploadDocumentTemplateDto input)
    {
        if (input.File == null || input.File.Length == 0)
        {
            return BadRequest("No file was uploaded");
        }

        var template = await _documentService.UploadTemplateAsync(input);
        return Ok(template);
    }

    /// <summary>
    /// Generates an application document for a JettyRequestItem
    /// </summary>
    [HttpPost]
    [Route("generate-application")]
    public async Task<IActionResult> GenerateApplicationDocumentAsync([FromBody] ApplicationDocumentGenerationDto input)
    {
        try
        {
            // Generate the document and save it as an attachment to get a stream URL
            var result = await _applicationDocumentService.GenerateApplicationDocumentAsAttachmentAsync(input);

            // Return the stream URL instead of the file content
            return Ok(new
            {
                success = true,
                streamUrl = result.StreamUrl,
                fileName = result.FileName,
                fileId = result.Id
            });
        }
        catch (UserFriendlyException ex)
        {
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "An error occurred while generating the application document", details = ex.Message });
        }
    }
}
