using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Approvals;

public class ApprovalApproverRepository : EfCoreRepository<JettyApprovalDbContext, ApprovalApprover, Guid>, IApprovalApproverRepository
{
    public ApprovalApproverRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<ApprovalApprover>> GetByApprovalIdAsync(Guid approvalId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalApprover>()
            .Where(x => x.ApprovalId == approvalId)
            .ToListAsync();
    }

    public async Task<List<ApprovalApprover>> GetByApproverIdAsync(Guid approverId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalApprover>()
            .Where(x => x.ApproverId == approverId)
            .ToListAsync();
    }

    public async Task<List<ApprovalApprover>> GetByStatusAsync(string status)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalApprover>()
            .Where(x => x.Status == status)
            .ToListAsync();
    }

    public async Task<List<ApprovalApprover>> GetOrderedBySequenceAsync(Guid approvalId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalApprover>()
            .Where(x => x.ApprovalId == approvalId)
            .OrderBy(x => x.Sequence)
            .ToListAsync();
    }
}