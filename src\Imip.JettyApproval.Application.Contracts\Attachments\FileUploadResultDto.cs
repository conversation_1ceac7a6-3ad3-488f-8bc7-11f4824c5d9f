using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// DTO for file upload results
/// </summary>
public class FileUploadResultDto
{
    /// <summary>
    /// The unique identifier for the uploaded file
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The name of the file
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// The content type of the file
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// The size of the file in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// The URL to download the file
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// The URL to stream/view the file in browser
    /// </summary>
    public string? StreamUrl { get; set; }

    /// <summary>
    /// The upload timestamp
    /// </summary>
    public DateTime UploadTime { get; set; }
}