using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// Entity for storing jetty request item information
/// </summary>
public class JettyRequestItem : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// Foreign key to the parent jetty request
    /// </summary>
    public Guid JettyRequestId { get; set; }

    /// <summary>
    /// Tenant name for the item
    /// </summary>
    public string? TenantName { get; set; }

    /// <summary>
    /// Name of the item
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// Quantity of the item
    /// </summary>
    public decimal Qty { get; set; }

    /// <summary>
    /// Unit of measurement
    /// </summary>
    public string? UoM { get; set; }

    /// <summary>
    /// Additional notes for the item
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Status of the item
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Letter number for the item
    /// </summary>
    public string? LetterNo { get; set; }

    /// <summary>
    /// Letter date for the item
    /// </summary>
    public DateTime? LetterDate { get; set; }

    /// <summary>
    /// Reference ID for linking to attachments
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Navigation property to the parent jetty request
    /// </summary>
    public virtual JettyRequest JettyRequest { get; set; } = null!;

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected JettyRequestItem()
    {
    }

    /// <summary>
    /// Creates a new JettyRequestItem
    /// </summary>
    public JettyRequestItem(
        Guid id,
        Guid jettyRequestId,
        string? tenantName = null,
        string? itemName = null,
        decimal qty = 0,
        string? uoM = null,
        string? notes = null,
        string? status = null,
        string? letterNo = null,
        DateTime? letterDate = null,
        Guid? referenceId = null)
        : base(id)
    {
        JettyRequestId = jettyRequestId;
        TenantName = tenantName;
        ItemName = itemName;
        Qty = qty;
        UoM = uoM;
        Notes = notes;
        Status = status;
        LetterNo = letterNo;
        LetterDate = letterDate;
        ReferenceId = referenceId;
    }

    /// <summary>
    /// Creates a new JettyRequestItem without ID (for mapping from DTOs)
    /// </summary>
    public JettyRequestItem(
        Guid jettyRequestId,
        string? tenantName = null,
        string? itemName = null,
        decimal qty = 0,
        string? uoM = null,
        string? notes = null,
        string? status = null,
        string? letterNo = null,
        DateTime? letterDate = null,
        Guid? referenceId = null)
    {
        JettyRequestId = jettyRequestId;
        TenantName = tenantName;
        ItemName = itemName;
        Qty = qty;
        UoM = uoM;
        Notes = notes;
        Status = status;
        LetterNo = letterNo;
        LetterDate = letterDate;
        ReferenceId = referenceId;
    }
}