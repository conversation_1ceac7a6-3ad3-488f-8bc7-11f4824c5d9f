using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Mapping.Mappers;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Permissions.Apps;
using Imip.JettyApproval.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalApprovers;

/// <summary>
/// Application service for ApprovalApprover entity
/// </summary>
[Authorize(JettyApprovalPermission.PolicyApprovalApprover.Default)]
public class ApprovalApproverAppService :
    CrudAppService<ApprovalApprover, ApprovalApproverDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalApproverDto, CreateUpdateApprovalApproverDto>,
    IApprovalApproverAppService
{
    private readonly IApprovalApproverRepository _approvalApproverRepository;
    private readonly ApprovalApproverMapper _mapper;
    private readonly ILogger<ApprovalApproverAppService> _logger;

    public ApprovalApproverAppService(
        IApprovalApproverRepository approvalApproverRepository,
        ApprovalApproverMapper mapper,
        ILogger<ApprovalApproverAppService> logger)
        : base(approvalApproverRepository)
    {
        _approvalApproverRepository = approvalApproverRepository;
        _mapper = mapper;
        _logger = logger;
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalApprover.Create)]
    public override async Task<ApprovalApproverDto> CreateAsync(CreateUpdateApprovalApproverDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalApproverRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalApprover.Edit)]
    public override async Task<ApprovalApproverDto> UpdateAsync(Guid id, CreateUpdateApprovalApproverDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalApproverRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalApproverRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalApprover.Delete)]
    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalApproverRepository.GetAsync(id);

        await _approvalApproverRepository.DeleteAsync(entity, autoSave: true);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalApprover.View)]
    public override async Task<ApprovalApproverDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalApproverRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalApprover.View)]
    public override async Task<PagedResultDto<ApprovalApproverDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalApproverRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalApproverDto>(totalCount, dtos);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalApprover.View)]
    public virtual async Task<PagedResultDto<ApprovalApproverDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _approvalApproverRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<ApprovalApproverDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<ApprovalApprover> ApplyDynamicQuery(IQueryable<ApprovalApprover> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalApprover>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalApprover>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}